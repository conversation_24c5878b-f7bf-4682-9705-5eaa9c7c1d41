<template>
  <div class="experiment-env">
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-formData"
    >
      <el-form-item
        v-for="(item, index) in formData.i3NetworkVos"
        :key="index"
        label="网络"
        prop="network"
      >
        <el-form-item
          :prop="'i3NetworkVos.' + index + '.id'"
          :rules="rules.i3SubnetId"
          label="子网"
        >
          <el-select
            ref="headerSearchSelect"
            v-model="item.id"
            :filterable="true"
            class="header-search-select"
            clearable
            @change="changeI3SubnetId(item.id, index)"
          >
            <el-option-group v-for="group in typeOptions" :key="group.id" :label="group.networkName">
              <el-option
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-option-group>
            <!-- <el-option
              v-for="option in typeOptions"
              :key="option.id"
              :value="option.id"
              :label="option.name"
            />-->
          </el-select>
        </el-form-item>
        <el-form-item :prop="'i3NetworkVos.' + index + '.ip'" :rules="rules.ip" label="IP">
          <el-input v-model="item.ip" placeholder="默认自动分配IP地址" disabled/>
          <el-button
            v-if="formData.i3NetworkVos.length > 1"
            style="margin-left: 20px"
            type="info"
            icon="el-icon-minus"
            @click="deleteType(item, index)"
          />
        </el-form-item>
      </el-form-item>
      <el-form-item label>
        <el-button :disabled="addDisable" type="primary" icon="el-icon-plus" @click="addType"/>
      </el-form-item>
      <el-form-item label="安全组" prop="securityGroupId">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.securityGroupId"
          class="header-search-select"
        >
          <el-option
            v-for="option in securityGroupNameOptions"
            :key="option.id"
            :value="option.id"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getSecurityGroup, getNetworks } from '@/api/sourceLibrary/virtualApi'
export default {
  name: 'EditEnv',
  components: {},
  // eslint-disable-next-line vue/require-prop-types
  props: ['isSubmitEnv', 'formData'],
  data() {
    // ip校验
    var checkIp = (rule, value, callback) => {
      const reg =
        /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/
      if (!value) {
        callback()
      } else if (!reg.test(value)) {
        callback(new Error('ip格式不正确！'))
      } else {
        callback()
      }
    }
    return {
      addDisable: false,
      // formData: {
      //   i3NetworkVos: [{ type: '', ip: '' }],
      //   securityGroupId: '',
      // },
      typeOptions: [],
      ipOptions: [],
      securityGroupNameOptions: [],
      rules: {
        securityGroupId: [
          { required: true, message: '请选择安全组', trigger: 'change' }
        ],
        ip: [
          // { required: true, message: '请选择安全组', trigger: 'change' },
          { validator: checkIp, trigger: 'blur' }
        ],
        i3SubnetId: [
          { required: true, message: '请选择子网', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    isSubmitEnv: function(newVal) {
      if (newVal) {
        // 提交
        this.$refs['formData'].validate(async(valid) => {
          if (valid) {
            console.log('网络配置通过验证')
            // eslint-disable-next-line no-unused-vars
            const params = {
              chapterId: this.formData.chapterId,
              topologyId: this.formData.topology_id,
              id: this.expId
            }
            this.$emit('formOk')
            /* try {
              if (this.expId) {
                // 修改
                // await updateExperiment(params)
              } else {
                // 新增
                // await addExperiment(params)
              }
              this.$emit('formOk')
            } catch { this.$emit('formError') }*/
          } else {
            this.$message.error('请完善表单！')
            this.$emit('formError')
            return false
          }
        })
      }
    }
  },
  created() {
    this.getList()
    this.getNetwork()
  },
  async mounted() {},
  methods: {
    changeI3SubnetId(val, index) {
      for (const item of this.typeOptions) {
        // eslint-disable-next-line no-unused-vars
        if (val == item.id) {
          this.formData.i3NetworkVos[index].i3NetworkName = item.networkName
          this.formData.i3NetworkVos[index].i3NetworkId = item.networkId
          this.formData.i3NetworkVos[index].i3SubnetId = item.id
        }
      }
    },
    async getList() {
      await getSecurityGroup()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.securityGroupNameOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    async getNetwork() {
      await getNetworks()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.typeOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    onSubmit() {
      this.getList()
    },
    addType() {
      if (this.formData.i3NetworkVos.length >= 16) {
        this.addDisable = true
        this.$message.error('系统允许的最大网络数量为16')
        return
      }
      const obj = { type: '', ip: '' }
      this.formData.i3NetworkVos.push(obj)
    },
    deleteType(item, index) {
      this.formData.i3NetworkVos.splice(index, 1)
      if (this.formData.i3NetworkVos.length < 16) {
        this.addDisable = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-select-group .el-select-dropdown__item ::v-deep {
  padding-left: 40px;
}
.experiment-env {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  box-sizing: border-box;
  // @include paddingBoxSizing(0 30px);
  > div {
    width: 100%;
    min-height: 30px;
    display: flex;
    align-items: flex-start;
    // @include flex(flex-start);
    font-size: 14px;
    label {
      display: inline-block;
      text-align: right;
      line-height: 30px;
      width: 70px;
      height: 30px;
      vertical-align: top;
    }
  }
}
.el-table {
  .testPaperTableDataRadio::v-deep {
    .el-radio__label {
      display: none;
    }
  }
}
.dialog::v-deep {
  .el-dialog {
    margin-top: 6vh !important;
  }
}
</style>
