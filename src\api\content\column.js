/*
 * @Author: <PERSON><PERSON>iin
 * @Date: 2024-09-03 17:03:48
 * @LastEditors: FinKiin
 * @LastEditTime: 2024-10-17 17:11:44
 * @FilePath: \huanyu_college_admin\src\api\content\column.js
 * @Description:
 *
 */
import request from '@/utils/request'

// 获取专栏分页列表
export function getColumnPageList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/getPageList',
    method: 'post',
    data: params
  })
}

// 添加专栏
export function addColumn(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/add',
    method: 'post',
    data: params
  })
}
// 编辑专栏
export function editColumn(params = { }) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/edit',
    method: 'post',
    data: params
  })
}
// 批量下架专栏
export function downColumn(params = { }) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/deleteBatch',
    method: 'post',
    data: params
  })
}

// 批量上架专栏
export function shelfColumn(params = { }) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/shelfBatch',
    method: 'post',
    data: params
  })
}

// 批量删除专栏
export function delColumn(params = { }) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/removeBatch',
    method: 'post',
    data: params
  })
}

// 获取商品列表
export function getGoodsPageList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/getGoodsPageList',
    method: 'post',
    data: params
  })
}

// 专栏添加商品
export function addGoods(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/addGoods',
    method: 'post',
    data: params
  })
}

// 专栏删除商品
export function cancelRelation(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/cancelRelation',
    method: 'post',
    data: params
  })
}

// 保存商品排序
export function updateGoodsSortBatch(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainColumn/updateGoodsSortBatch',
    method: 'post',
    data: params
  })
}
