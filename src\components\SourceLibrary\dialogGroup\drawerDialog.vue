<template>
  <div class="drawer_dialog">
    <dialogCom
      v-if="dialogFlag"
      :title="editInfo ? '编辑快照' : '创建快照'"
      :dialog-visible="dialogFlag"
      :submit="_submit"
      :cancel="_closeDialog"
      :dis-flag="disFlag"
    >
      <template>
        <div class="dialog_con">
          <el-form
            ref="formData"
            :model="formData"
            :rules="rules"
            class="drawer_form"
            label-width="120px"
          >
            <el-form-item label="云主机:">
              <svg-icon icon-class="computer" class-name="card-panel-icon" />
              {{ drawerData.facilityName }}
            </el-form-item>
            <el-form-item label="系统盘:">
              <!-- {{formData.volume_name}} -->
              <el-checkbox-group v-model="formData.volumeList">
                <el-checkbox
                  v-for="item in volumeDataList"
                  :label="item.volume_id"
                  :key="item.volume_id"
                  disabled
                >{{ item.volume_name }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="数据盘:">
              <el-checkbox-group v-model="formData.diskVos">
                <el-checkbox
                  v-for="item in diskVosList"
                  :label="item.volume_id"
                  :key="item.volume_id"
                >{{ item.name }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="名称:" prop="name">
              <el-input v-model="formData.name"/>
              <!-- {{drawerData.cloudCategory}} -->
            </el-form-item>
            <el-form-item label="描述:" prop="description">
              <el-input v-model="formData.description" :rows="5" type="textarea"/>
              <!-- {{drawerData.cloudCategory}} -->
            </el-form-item>
          </el-form>
          <div class="right_charts">
            <div class="title_text">
              <span>云硬盘数量</span>
              <span
                v-if="volumesList && volumesList.maxTotalSnapshots"
              >{{ volumesList.totalSnapshotsUsed + '/' + volumesList.maxTotalSnapshots }}</span>
            </div>
            <el-progress
              v-if="volumesList.maxTotalSnapshots"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(volumesList.totalSnapshotsUsed/volumesList.maxTotalSnapshots)"
            />
            <div class="title_text">
              <span>云硬盘及快照总大小</span>
              <span
                v-if="volumesList && volumesList.maxTotalVolumeGigabytes"
              >{{ volumesList.totalGigabytesUsed + '/' + volumesList.maxTotalVolumeGigabytes }}</span>
            </div>
            <el-progress
              v-if="volumesList.maxTotalVolumeGigabytes"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(volumesList.totalGigabytesUsed/volumesList.maxTotalVolumeGigabytes)"
            />
          </div>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import {
  volumesQuotas,
  cloudHostVolumes,
  findCloudHostUUID,
  addSnapshots,
  editSnapshots
} from '@/api/sourceLibrary/virtualApi'
import dialogCom from '@/components/SourceLibrary/dialogGroup/index'
export default {
  components: {
    dialogCom
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    drawerData: {
      type: Object,
      default: null
    },
    editInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      formData: {
        diskVos: [],
        volumeList: [],
        name: '',
        description: '',
        snapshotsId: ''
      },
      disFlag: false,
      dialogFlag: false,
      rules: {
        name: [
          { required: true, message: '请输入快照名称', trigger: 'change' }
        ]
      },
      volumesList: {},
      volumeDataList: [], // 系统盘checkList
      diskVosList: []
    }
  },
  created() {
    if (this.editInfo) {
      this.formData.name = this.editInfo.name
      this.formData.description = this.editInfo.description
      this.formData.snapshotsId = this.editInfo.id
    }
    this.getFindCloudHostUUID()
    this.getCloudHostVolumes()
    this.getVolumesQuotas()
    this.dialogFlag = this.dialogVisible
  },
  methods: {
    // 查询云主机已挂载的系统盘
    async getFindCloudHostUUID() {
      await findCloudHostUUID(this.drawerData.id)
        .then((res) => {
          if (res.code == 200) {
            this.formData.volume_id = res.data.volume_id
            this.formData.volume_name = res.data.volume_name
            this.formData.volume_size = res.data.volume_size
            this.formData.volumeList.push(res.data.volume_id)

            this.volumeDataList.push(res.data)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 查询云主机已挂载的数据盘
    async getCloudHostVolumes() {
      console.log('eqwe', this.drawerData)
      await cloudHostVolumes(this.drawerData.id)
        .then((res) => {
          if (res.code == 200) {
            this.diskVosList = res.data.data
            this.diskVosList.forEach((ele) => {
              this.formData.diskVos.push(ele.volume_id)
            })
            // this.formData.diskVos = res.data.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    async getVolumesQuotas() {
      await volumesQuotas()
        .then((res) => {
          if (res.code == 200) {
            this.volumesList = res.data.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleDiskTypeId() {},
    async _submit() {
      this.disFlag = true
      if (!this.editInfo) {
        const id =
          this.drawerData.id +
          '?description=' +
          this.formData.description +
          '&name=' +
          this.formData.name
        await addSnapshots(id)
          .then((res) => {
            if (res.code == 200) {
              // this.$message.success(res.msg)
              res.name = this.drawerData.facilityName + '添加快照， '
              this.$parent.msgList = res
              this.$emit('msgShow')
              this.disFlag = true
              this.$emit('updateList')
              this._closeDialog()
            } else {
              this.$message.error(res.msg)
            }
            this.disFlag = false
          })
          .catch((err) => {
            this.disFlag = false
            console.log(err)
          })
      } else {
        let id =
          this.formData.snapshotsId +
          '?description=' +
          this.formData.description +
          '&name=' +
          this.formData.name
        id = id + '&volumeId=' + this.editInfo.volumeId
        await editSnapshots(id)
          .then((res) => {
            if (res.code == 200) {
              res.name = this.drawerData.facilityName + '编辑快照， '
              this.$parent.msgList = res
              this.$emit('msgShow')
              this.disFlag = true
              // this.$message.success(res.msg)
              this.$emit('updateList')
              this._closeDialog()
            } else {
              this.$message.error(res.msg)
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    },
    _closeDialog() {
      this.dialogFlag = false
      this.$parent.drawerDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer_dialog {
  .dialog_con {
    display: flex !important;
    .drawer_form {
      width: 70%;
      padding-right: 20px;
      box-sizing: border-box;
    }
    .right_charts {
      width: 30%;
      padding-left: 20px;
      box-sizing: border-box;
      border-left: 1px solid #999;
      .title_text {
        margin-bottom: 10px;
        > span {
          margin-right: 10px;
        }
      }
      .el-progress {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
