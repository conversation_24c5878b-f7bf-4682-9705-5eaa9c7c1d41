import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/disProfitRecord/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/disProfitRecord/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/disProfitRecord/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/disProfitRecord/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/disProfitRecord/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/disProfitRecord/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
