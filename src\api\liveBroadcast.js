import request from "@/utils/request";

// 添加讲者分类
export function addCategory(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/add",
    method: "post",
    data: params
  });
}

// 编辑讲者分类
export function editCategory(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/edit",
    method: "post",
    data: params
  });
}

// 获取分类列表
export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/getList",
    method: "post",
    data: params
  });
}

// 获取分类列表
export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/deleteBatch",
    method: "post",
    data: params
  });
}

// 添加直播课程信息
export function addLive(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/add",
    method: "post",
    data: params
  });
}

// 分页获取直播课程信息列表
export function getLiveList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/getList",
    method: "post",
    data: params
  });
}
// 分页获取直播审核课程信息列表
export function getLiveAuditList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/selectCheckList",
    method: "post",
    data: params
  });
}
// 批量删除直播课程信息
export function liveDeleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/deleteBatch",
    method: "post",
    data: params
  });
}
// 批量上下架直播
export function liveStatusBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/setStatusBatch",
    method: "post",
    data: params
  });
}
// 批量审核直播
export function liveAuditBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/setCheckState",
    method: "post",
    data: params
  });
}
// 更新直播课程排序
export function liveSort(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/sortNew",
    method: "post",
    data: params
  });
}
// 置顶直播课程到讲堂首页
export function liveUpdateTop(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/updateTop",
    method: "post",
    data: params
  });
}
// 置顶直播课程到指令者首页
export function liveUpdateTopHome(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/updateTopHome",
    method: "post",
    data: params
  });
}
// 课程上下架
export function liveUpdateStatus(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/updateStatus",
    method: "post",
    data: params
  });
}

// 企业分页查询
export function getEnterpriseList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/getList",
    method: "post",
    data: params
  });
}

// 直播课程详情
export function getInfoLive(liveUid) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/getInfo?liveUid=" + liveUid,
    method: "get"
  });
}

// 编辑直播课程详情
export function editLive(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/edit",
    method: "post",
    data: params
  });
}

// 粉丝列表 1.讲者 2.企业 3.直播
export function liveFansList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/fans/list",
    method: "post",
    data: params
  });
}

// 粉丝列表 导出 1.讲者 2.企业 3.直播
export function liveFansDownloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/fans/downloadExcel",
    method: "post",
    data: params,
    responseType: "blob"
  });
}

// 上传直播回放视频
export function liveUpdatePlayback(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/updatePlayback",
    method: "post",
    data: params
  });
}
