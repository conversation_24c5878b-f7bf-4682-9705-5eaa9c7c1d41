import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/deleteBatch",
    method: "post",
    data: params
  });
}

export function courseList() {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/courseList",
    method: "post"
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCourse/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
