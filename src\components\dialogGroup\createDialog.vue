<template>
  <div class="create_dialog">
    <dialogCom
      :title="title"
      :dialog-visible="dialogShowFlag"
      :submit="_submit"
      :cancel="_closeDialog"
      :show-footer="false"
    >
      <template>
        <div class="dialog_con">
          <div class="edit-experiment">
            <div class="edit-e-con">
              <template v-if="activeIndex === 1">
                <edit-info
                  v-if="activeIndex === 1"
                  :is-submit="isSubmit"
                  @formError="_formError"
                  @formOk="_formOk"
                />
              </template>
              <div class="btn-group">
                <el-button
                  :disabled="disFlag"
                  type="primary"
                  class="operate_btn"
                  @click="_next"
                >完成</el-button>
              </div>
            </div>
          </div>
          <div class="right_charts">
            <div class="title_text">
              <span>云主机配额</span>
              <span
                v-if="formData && formData.maxTotalInstances"
              >{{ formData.totalInstancesUsed + '/' + formData.maxTotalInstances }}</span>
            </div>
            <el-progress
              v-if="formData.maxTotalInstances"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil((formData.totalInstancesUsed/formData.maxTotalInstances)*100)"
            />
            <div class="title_text">
              <span>CPU配额</span>
              <span
                v-if="formData && formData.maxTotalCores"
              >{{ computeCoresUsed + '/' + formData.maxTotalCores }}</span>
            </div>
            <el-progress
              v-if="formData.maxTotalCores"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil((computeCoresUsed/formData.maxTotalCores)*100)"
            />
            <div class="title_text">
              <span>内存配额</span>
              <span
                v-if="formData && formData.maxTotalRAMSize"
              >{{ computeRAMSize + '/' + formData.maxTotalRAMSize }}</span>
            </div>
            <el-progress
              v-if="formData.maxTotalRAMSize"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil((computeRAMSize/formData.maxTotalRAMSize)*100)"
            />
            <div class="title_text">
              <span>云硬盘数量</span>
              <span
                v-if="formData && formData.volumesList.maxTotalVolumes"
              >{{ computeVolumesUsed + '/' + formData.volumesList.maxTotalVolumes }}</span>
            </div>
            <el-progress
              v-if="formData.volumesList.maxTotalVolumes"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil((computeVolumesUsed/formData.volumesList.maxTotalVolumes)*100)"
            />
            <div class="title_text">
              <span>云硬盘及快照总大小</span>
              <span
                v-if="formData && formData.volumesList.maxTotalVolumeGigabytes"
              >{{ computeDiskSize + '/' + formData.volumesList.maxTotalVolumeGigabytes }}</span>
            </div>
            <el-progress
              v-if="formData.volumesList.maxTotalVolumeGigabytes"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil((computeDiskSize/formData.volumesList.maxTotalVolumeGigabytes)*100)"
            />
            <div class="title_text">
              <span>外网ip配额</span>
              <span>{{ 0 + '/' + 0 }}</span>
            </div>
            <el-progress :stroke-width="12" :show-text="false" :percentage="0"/>
          </div>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import {
  instancesQuotas, // 查询云主机配额
  networksQuotas, // 查询网络配额
  volumesQuotas // 查询云硬盘配额
} from '@/api/sourceLibrary/virtualApi'
import EditInfo from '@/components/sleps/editInfo.vue'
import EditAdviser from '@/components/sleps/editAdviser.vue'
import EditEnv from '@/components/sleps/editEnv.vue'
import EditLogin from '@/components/sleps/editLogin.vue'
import dialogCom from '@/components/dialogGroup/index'
export default {
  components: {
    dialogCom,
    EditInfo,
    EditAdviser,
    EditEnv,
    EditLogin
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    dialogFlag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      disFlag: false,
      activeIndex: 1,
      isSubmit: false,
      isSubmitAdviser: false,
      isSubmitEnv: false,
      isSubmitLogin: false,
      allowNext: false,
      formData: {
        facilityCategoryId: null,
        chapterId: '',
        facilityIcon: '',
        url: '',
        systemImagetype: 0,
        systemImageName: 'windows',
        description: '',
        facilityDescription: '',
        specificationName: '',
        cloudCategory: '云主机',
        vcpus: 0,
        ram: 0,
        facilityBrandId: null,
        facilityBrandTypeId: '',
        version: '',
        administerIp: '',
        connectorId: '',
        productPort: [],
        diskSize: 20,
        specificationId: '',
        diskTypeId: '',
        systemImageId: '',
        // diskVos: [{ diskSize: 0, systemImageId: '', diskTypeName: '' }],
        i3NetworkVos: [{ i3NetworkId: '', i3NetworkName: '', i3SubnetId: '' }],
        securityGroupId: '',
        loginType: '密码',
        facilityName: '',
        username: 'root',
        security: '',
        count: '1',
        totalInstancesUsed: 0, // 云主机配额
        maxTotalInstances: 0, // 云主机配额
        totalRAMUsed: 0, // 内存
        maxTotalRAMSize: 0, // 内存
        totalCoresUsed: 0, // cpu
        maxTotalCores: 0, // cpu
        volumesList: {}, // 云硬盘配额
        clusterName: ''
      },
      dialogShowFlag: false
    }
  },
  computed: {
    // 计算cpu
    computeCoresUsed() {
      return parseInt(this.formData.vcpus) + this.formData.totalCoresUsed
    },
    // 计算ram
    computeRAMSize() {
      let memory = 0
      if (this.formData.ram && this.formData.ram.indexOf('MB') != -1) {
        memory = parseInt(this.formData.ram)
      } else {
        memory = parseInt(this.formData.ram) * 1024
      }
      return memory + this.formData.totalRAMUsed
    },
    // 计算云主机配额
    computeDiskSize() {
      let size = 0
      if (this.formData.diskVos && this.formData.diskVos.length) {
        for (const item of this.formData.diskVos) {
          size += item.diskSize
        }
      }
      return (
        this.formData.diskSize +
        size +
        this.formData.volumesList.totalGigabytesUsed
      )
    },
    // 计算云硬盘数量
    computeVolumesUsed() {
      return (
        (this.formData.diskVos && this.formData.diskVos.length
          ? this.formData.diskVos.length
          : 0) +
        this.formData.volumesList.totalVolumesUsed +
        1
      )
    }
  },
  created() {
    this.activeIndex = window.localStorage.getItem('aindex')
      ? window.localStorage.getItem('aindex') * 1
      : 1
    this.getAllQuotas()
  },
  destroyed() {
    window.localStorage.removeItem('aindex')
    // this.$store.dispatch('experiment/removeExpId', '')
  },
  methods: {
    getAllQuotas() {
      this.getInstancesQuotas()
      this.getVolumesQuotas()
      this.getNetworksQuotas()
      this.dialogShowFlag = this.dialogVisible
    },
    // 查询云主机配额
    async getInstancesQuotas() {
      await instancesQuotas()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            // 云主机配额
            this.formData.totalInstancesUsed = res.data.data.totalInstancesUsed
            this.formData.maxTotalInstances = res.data.data.maxTotalInstances
            // 内存
            this.formData.totalRAMUsed = res.data.data.totalRAMUsed
            this.formData.maxTotalRAMSize = res.data.data.maxTotalRAMSize
            // cpu
            this.formData.totalCoresUsed = res.data.data.totalCoresUsed
            this.formData.maxTotalCores = res.data.data.maxTotalCores
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 查询网络配额
    async getNetworksQuotas() {
      await networksQuotas()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.formData.networksQuotasList = res.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    // 查询云硬盘配额
    async getVolumesQuotas() {
      await volumesQuotas()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.formData.volumesList = res.data.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    _submit() {},
    _closeDialog() {
      this.$parent.createDialog = false
      this.dialogShowFlag = false
    },
    _prev() {
      if (this.activeIndex === 2) {
        this.isSubmit = false
        this.allowNext = false
      }
      if (this.activeIndex === 3) {
        this.isSubmitAdviser = false
        this.allowNext = false
      }
      if (this.activeIndex === 4) {
        this.isSubmitEnv = false
        this.allowNext = false
      }
      this.activeIndex--
      window.localStorage.setItem('aindex', this.activeIndex)
    },
    _next() {
      this.isSubmit = true
    },
    async _over() {
      this.isSubmit = false
      this.$emit('updateList')
      this._closeDialog()
    },
    _formError() {
      this.isSubmit = false
    },
    _formOk() {
      this.allowNext = true
      if (this.allowNext) {
        this.activeIndex++
        this.allowNext = false
        window.localStorage.setItem('aindex', this.activeIndex)
        this.$nextTick(() => {
          if (this.activeIndex == 2) {
            this.isSubmit = false
            this._over()
          } else {
            this.isSubmit = false
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scope>
.create_dialog {
  .el-dialog__body {
    height: 86vh;
  }
  /deep/ .dialog_con {
    display: flex !important;
    box-sizing: border-box;
    padding-right: 20px;
    .edit-experiment {
      width: 70%;
      .step {
        margin-bottom: 20px;
      }
      .btn-group {
        width: 100%;
        text-align: center;
      }
      .el-form {
        height: calc(86vh - 180px);
        overflow-y: auto;
        margin-bottom: 20px;
        .el-select,
        .el-cascader--medium {
          width: 100%;
        }
        .el-input--medium {
          width: 98%;
        }
      }
    }
    .right_charts {
      width: 30%;
      padding-left: 20px;
      box-sizing: border-box;
      border-left: 1px solid #999;
      .title_text {
        margin-bottom: 15px;
        > span {
          margin-right: 10px;
        }
      }
      .el-progress {
        margin-bottom: 10px;
      }
    }
  }

}
</style>
