import request from '@/utils/request'
 
const api = process.env.ADMIN_API

// 分类列表
export function lpTypeList(data) {
  return request({
    url: api + '/classification/list',
    method: 'post',
    data: data
  })
}

// 新增分类
export function addType(data) {
  return request({
    url: api + '/classification/insertClassification',
    method: 'post',
    data
  })
}

// 修改分类
export function editType(data) {
  return request({
    url: api + '/classification/updateClassification',
    method: 'post',
    data
  })
}

// 删除分类
export function delType(data) {
  return request({
    url: api + '/classification/deleteClassification',
    method: 'get',
    params: data
  })
}


// 漏洞列表
export function loopholeList(data) {
  return request({
    url: api + '/loophole/list',
    method: 'post',
    data: data
  })
}

// 新增漏洞
export function insertLoophole(data) {
  return request({
    url: api + '/loophole/insertLoophole',
    method: 'post',
    data
  })
}

// 修改漏洞
export function updateLoophole(data) {
  return request({
    url: api + '/loophole/updateLoophole',
    method: 'post',
    data
  })
}

// 删除漏洞
export function deleteLoopholeList(data) {
  return request({
    url: api + '/loophole/deleteLoopholeList',
    method: 'post',
    data: data
  })
}

// 漏洞查询详情
export function selectLoophole(data) {
  return request({
    url: api + '/loophole/selectLoophole',
    method: 'post',
    data
  })
}


// 漏洞查询详情
export function selectClassification(data) {
  return request({
    url: api + '/loophole/selectClassification',
    method: 'post',
    data
  })
}

// 查看统计
export function statistics(data) {
  return request({
    url: api + '/loophole/statistics',
    method: 'get',
    params: data
  })
}

// 攻克人名单 分页条件查询
export function finishList(data) {
  return request({
    url: api + '/loophole/finish',
    method: 'post',
    data
  })
}

// 挑战人名单 分页条件查询
export function tryList(data) {
  return request({
    url: api + '/loophole/try',
    method: 'post',
    data
  })
}

// 攻克人名单导出
export function finishExport(data) {
  return request({
    url: api + '/loophole/finish/export',
    method: 'post',
    params: data,
    responseType: "blob"
  })
}

// 挑战人名单导出
export function tryExport(data) {
  return request({
    url: api + '/loophole/try/export',
    method: 'post',
    params: data,
    responseType: "blob"
  })
}