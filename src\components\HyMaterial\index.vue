<template>
  <el-dialog :title="hy_title" :append-to-body='true' :visible.sync="visible" :width="width" top="6vh">
    <div class="flex" :style="'height:' + height">
      <div class="group-list scroll-div">
        <div class="ellipsis py-10 pl-10 cursor" v-for="item in groupList" :key="item.uid"
          :class="{ 'active-group': activeGroup == item.uid }" @click="clickGroup(item)">
          <div v-if="type == 1">{{ item.name }} ({{ item.pictureAmount }})</div>
          <div v-else-if="type == 2">{{ item.groupName }} ({{ item.videoAmount }})</div>
          <div v-if="type == 4">{{ item.name }} ({{ item.pictureAmount }})</div>
        </div>
      </div>
      <div class="data-list scroll-div ml-20">

        <div class="material-list scroll-div">
          <div class="ml-15 flex mb-10">
            <el-button v-if="multiple" size="small" @click="handleSelectionChange(dataList)" type="primary">
              一键选择
            </el-button>
          </div>

          <div class="flex-wrap flex">
            <div class="ml-15 pic-item mb-10 p-5" v-for="item in dataList" :key="item.uid + 300"
              :class="{ 'active-data': getSelectUids.includes(item.uid) }" @click="clickData(item)">
              <div v-if="type == 1" class="flex-col">
                <el-image style="width: 150px; height: 100px" :src="item.pictureUrl">
                  <div slot="error" class="image-slot h-100 font-36 flex-center">
                    <i class="el-icon-picture-outline color-999"></i>
                  </div>
                </el-image>
                <span class="mt-20 ellipsis">{{ item.picName }}</span>
              </div>
              <div v-if="type == 2" class="flex-col">
                <video :src="item.fileUrl" controls width="150px" height="100px" :id="`video-${item.uid}`" />
                <span class="mt-20 ellipsis">{{ item.videoName }}</span>
              </div>

              <div v-if="type == 4" class="flex-col">
                <img style="width: 150px; height: 100px" src="../../assets/logo/pdf.png">
                <span class="mt-20 ellipsis">{{ item.picName }}</span>
              </div>
            </div>
          </div>
        </div>

        <div ml-15>
          <div class="flex ai-center jc-between mt-20">
            <div class="color-666 font-14">共{{ total }}条数据，已选择{{ selectList.length }}</div>
            <el-pagination :current-page.sync="params.currentPage" :page-sizes="[25, 50, 75, 100, 200]"
              :page-size="params.pageSize" background :total="total" layout="sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>

          <div class="flex-right mt-10">
            <el-button size="small" @click="visible = false"> 取 消 </el-button>
            <el-button :disabled="selectList.length < 1" size="small" @click="selectSure" type="primary"> 确 认 </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { selectPictureGroup, selectPictureList, selectGroupList, selectMaterialVideo } from '@/api/medium/material.js'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '1000px'
    },
    height: {
      type: String,
      default: '700px'
    },
    type: {
      type: Number,
      default: 1 // { 1: '图片', 2: '视频', 3: '音频', 4: '材料' }
    },
    limit: {
      type: Number,
      default: 0 // 为零时不限制数量
    },
    multiple: {
      type: Boolean,
      default: false // 多选
    }
  },
  data () {
    return {
      visible: false,
      hy_title: '选择图片',
      groupFunction: {
        1: selectPictureGroup,
        2: selectGroupList,
        4: selectPictureGroup,
      },
      dataFunction: {
        1: selectPictureList,
        2: selectMaterialVideo,
        4: selectPictureList
      },
      activeGroup: '',
      groupList: [],
      dataList: [],
      params: {
        currentPage: 1,
        pageSize: 25
      },
      total: 0,
      selectList: [],
      groupType: { 1: 'PICTURE', 2: '', 3: '', 4: 'ARTICLE' }
    }
  },
  computed: {
    getLabel () {
      return { 1: '图片', 2: '视频', 3: '音频', 4: '材料' }[this.type]
    },
    getSelectUids () {
      return this.selectList.map(item => item.uid)
    }
  },
  watch: {
    show: {
      handler: function (v) {
        this.visible = v
        if (v) {
          this.initData()
          this.getGroup()
        }
      }, deep: true
    },
    visible: {
      handler: function (v) {
        this.$emit('update:show', v);
        this.hy_title = '选择' + this.getLabel
        if (this.title) this.hy_title = this.title
      }, deep: true
    },
    title: {
      handler: function (v) {
        this.hy_title = v
      }, deep: true,
    }
  },
  created () {
    this.visible = this.show
  },
  methods: {
    initData () {
      this.selectList = []
    },
    // 分组列表
    getGroup () {
      const param = { groupType: this.groupType[this.type], isChoose: true  }
      this.groupFunction[this.type](param).then(res => {
        if (res.code == 200) {
          this.groupList = res.data
          if (this.groupList.length > 0) this.clickGroup(this.groupList[0])
        }
      })
    },

    // 切换分组
    clickGroup (item) {
      this.activeGroup = item.uid
      this.getData(1)
    },

    // 素材信息
    getData (val) {
      if (val) this.params.currentPage = val
      const param = { ...this.params, pictureSortUid: this.activeGroup, manualState: 2 }
      this.dataFunction[this.type](param).then(res => {
        if (res.code == 200) {
          this.total = res.data.total
          this.dataList = res.data.records
        }
      })
    },

    // 点击数据
    clickData (data) {
      if (this.getSelectUids.includes(data.uid)) {
        this.selectList = this.selectList.filter(item => item.uid != data.uid)
      } else {
        if (this.multiple) {
          if (this.limit != 0) {
            if (this.selectList.length == this.limit) {
              return
            } else {
              this.selectList.push(data)
            }
          } else {
            this.selectList.push(data)
          }
        } else {
          this.selectList = [data]
        }
      }


    },

    // 一键选择，传入当前页数组，没选的选中，已选中的取消
    handleSelectionChange (val) {
      let filterList = []
      const list = val.filter(item => {
        if (this.getSelectUids.includes(item.uid)) { // 包含则过滤已有的
          filterList.push(item.uid)
          return false
        }
        return true
      })

      this.selectList = this.selectList.filter(item => !filterList.includes(item.uid))
      list.forEach(item => this.clickData(item))
    },

    selectSure () {
      this.$emit('select', this.selectList)
      this.visible = false
    },



    handleSizeChange (val) {
      this.params.pageSize = val
      this.getData(1)
    },
    handleCurrentChange (val) {
      this.params.currentPage = val
      this.getData()
    },
  }
}
</script>

<style lang="scss" scoped>
.group-list {
  height: 100%;
  width: 220px;
  border-right: 1px solid #DCDFE6;

}

.data-list {
  height: 100%;
  width: calc(100% - 250px);

  .material-list {
    height: calc(100% - 100px);
  }
}

.active-group {
  background: rgba(0, 110, 255, 0.15);
  color: rgba(0, 110, 255, 1);
  border-left: 2px solid rgba(0, 110, 255, 1);
}

/deep/ .el-dialog__body {
  padding-top: 10px;
}

.scroll-div {
  overflow: auto;

  // 滚动条整体样式
  &::-webkit-scrollbar {
    width: 4px !important;
    /* 纵向滚动条 宽度 */
    border-radius: 5px;
    /* 整体 圆角 */
  }

  //轨道部分
  &::-webkit-scrollbar-track {
    background: #fff !important;
  }

  // 滑块部分
  &::-webkit-scrollbar-thumb {
    background: #D4DBE0 !important;
    min-height: 167px;
    width: 2px !important;
    border-radius: 5px;
  }

  scrollbar-width: thin !important;
  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  -ms-overflow-style: none !important;
  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
}

.pic-item {
  border: 1px solid #DCDFE6;
  width: 160px;
  border-radius: 5px;
}

.pic-item:hover {
  cursor: pointer;
  border-color: rgba(0, 110, 255, 1);
}

.active-data {
  border-color: rgba(0, 110, 255, 1) !important;
}
</style>
