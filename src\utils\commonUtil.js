import { Message } from "element-ui";
// import {showdown} from 'showdown'
// import {TurndownService} from 'turndown'
import showdownKatex from "showdown-katex";

/** **********************************************************/
/**
 *  全局状态码
 */
const ECode = {
  // 成功状态提示码
  SUCCESS: 200,
  // 错误提示码
  ERROR: 201,
  // 弹窗提示码 下一步校验
  ERROR_DIALOG: 202
};

/**
 * 全局配置文件
 */
const SysConf = {
  defaultAvatar: require("@/assets/defaultAvatar.png") // 默认头像
};

/** **********************************************************/

/**
 * 通用工具类
 */
const FUNCTIONS = {
  /**
   * 标签转字符串
   * @param tags
   * @returns {string}
   */
  tagsToString: tags => {
    let str = "";
    for (let i = 0; i < tags.length; i++) {
      str += tags[i] + ",";
    }
    return str.substr(0, str.length - 1);
  },
  // 切割字符串
  splitStr: (str, flagCount) => {
    if (str == null || str == "") {
      return "";
    } else if (str.length > flagCount) {
      return str.substring(0, flagCount) + "...";
    } else {
      return str;
    }
  },
  /**
   * a标签下载文件标签重置文件名字(忽略同源的影响)
   * @param {*} urls
   * @param {*} fileName
   */
  downladResetFileName: (urls, fileName) => {
    const x = new window.XMLHttpRequest();
    x.open("GET", urls, true);
    x.responseType = "blob";
    x.onload = () => {
      const url = window.URL.createObjectURL(x.response);
      const a = document.createElement("a");
      a.href = url;
      a.target = "_blank";
      fileName && (a.download = fileName);
      a.style.display = "none";
      document.body.append(a);
      a.click();
      document.body.removeChild(a);
    };
    x.send();
  },
  /**
   * 切割字符串
   * @param str
   * @param count
   * @returns {string|*}
   */
  strSubstring: (str, count) => {
    if (str == null || str == "") {
      return "";
    } else if (str.length > count) {
      return str.substring(0, count) + "...";
    } else {
      return str;
    }
  },
  /**
   * 复制文字到剪切板
   * @param text
   */
  copyText: text => {
    const oInput = document.createElement("input");
    oInput.value = text;
    document.body.appendChild(oInput);
    oInput.select(); // 选择对象
    document.execCommand("Copy"); // 执行浏览器复制命令
    oInput.className = "oInput";
    oInput.style.display = "none";
  },
  /**
   * 将Markdown转成Html
   * @param text
   */
  markdownToHtml: text => {
    let converter = new showdown.Converter({
      tables: true,
      extensions: [
        showdownKatex({
          // maybe you want katex to throwOnError
          throwOnError: true,
          // disable displayMode
          displayMode: false,
          // change errorColor to blue
          errorColor: "#1500ff"
        })
      ]
    });
    let html = converter.makeHtml(text);
    return html;
  },
  /**
   * 将Html转成Markdown
   * @param text
   */
  htmlToMarkdown: text => {
    var turndownService = new TurndownService();

    // 用于提取代码语言
    turndownService.addRule("CodeBlock", {
      filter: function(node, options) {
        return (
          node.nodeName === "PRE" &&
          node.firstChild &&
          node.firstChild.nodeName === "CODE"
        );
      },
      replacement: function(content, node, options) {
        var className = node.firstChild.getAttribute("class") || "";
        var language = (className.match(/language-(\S+)/) || [null, ""])[1];
        return (
          "\n\n" +
          options.fence +
          language +
          "\n" +
          node.firstChild.textContent +
          options.fence
        );
      }
    });

    // 提取数学公式进行转换
    turndownService.addRule("multiplemath", {
      filter(node, options) {
        return node.classList.contains("vditor-math");
      },
      replacement(content, node, options) {
        console.log("中间内容", node.firstChild.textContent);
        return `$$ \n${node.firstChild.textContent}\n $$`;
      }
    });

    var turndownPluginGfm = require("turndown-plugin-gfm");
    var gfm = turndownPluginGfm.gfm;
    var tables = turndownPluginGfm.tables;
    var strikethrough = turndownPluginGfm.strikethrough;
    turndownService.use(gfm);
    turndownService.use([tables, strikethrough]);

    console.log("转换后", turndownService.turndown(text));
    return turndownService.turndown(text);
  },
  /**
   * 将Html转成Markdown文件
   * @param title：标题
   * @param text：正文
   */
  htmlToMarkdownFile: (title, text) => {
    title = title || "默认标题";

    let turndownService = new TurndownService();

    let markdown = turndownService.turndown(text);

    //创建一个blob对象,file的一种
    let blob = new Blob([markdown]);

    let link = document.createElement("a");

    link.href = window.URL.createObjectURL(blob);

    //配置下载的文件名
    link.download = title + ".md";

    link.click();
  },
  deepClone(obj, hash = new WeakMap()) {
    if (obj == null) return obj; //如果是null 或者undefined 我就不进行拷贝操作
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof RegExp) return new RegExp(obj);
    //可能是对象 或者普通的值 如果是函数的话，是不需要深拷贝的  因为函数是用来调用的，不需要再拷贝一个新的函数
    if (typeof obj !== "object") return obj;
    // 是对象的话就要进行深拷贝 [] {} Object.prototype.toString.call(obj)==[object Array]?[]:{}
    if (hash.get(obj)) return hash.get(obj);

    let cloneObj = new obj.constructor();
    hash.set(obj, cloneObj);
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        //实现一个递归拷贝
        cloneObj[key] = this.deepClone(obj[key], hash);
      }
    }
    return cloneObj;
  },

  /**
   * 将接口返回的Excel导出
   * @param {} res
   */
  downloadExcel: (res, fileName = "msg.xls") => {
    let url = window.URL.createObjectURL(
      new Blob([res], {
        type: "application/vnd.ms-excel"
      })
    );

    //创建隐藏的a标签
    let link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    //设置导出文件的名字
    let excelName = fileName;
    link.setAttribute("download", excelName);
    document.body.appendChild(link);
    //模拟点击事件
    link.click();
    //导出成功后删除这个标签并释放blob对象
    link.remove();
    window.URL.revokeObjectURL(url); //释放掉blob对象
  },

  // 清空对象值
  clearValue(obj) {
    if (obj != null && obj != undefined) {
      Object.keys(obj).forEach(key => {
        if (typeof obj[key] == "object") {
          this.clearValue(obj[key]);
        } else {
          obj[key] = "";
        }
      });
    }
  },
  // 秒转时分秒
  formatSeconds(value) {
    //  秒
    let second = parseInt(value);
    //  分
    let minute = 0;
    //  小时
    let hour = 0;
    //  天
    //  let day = 0
    //  如果秒数大于60，将秒数转换成整数
    if (second > 60) {
      //  获取分钟，除以60取整数，得到整数分钟
      minute = parseInt(second / 60);
      //  获取秒数，秒数取佘，得到整数秒数
      second = parseInt(second % 60);
      //  如果分钟大于60，将分钟转换成小时
      if (minute > 60) {
        //  获取小时，获取分钟除以60，得到整数小时
        hour = parseInt(minute / 60);
        //  获取小时后取佘的分，获取分钟除以60取佘的分
        minute = parseInt(minute % 60);
        //  如果小时大于24，将小时转换成天
        //  if (hour > 23) {
        //    //  获取天数，获取小时除以24，得到整天数
        //    day = parseInt(hour / 24)
        //    //  获取天数后取余的小时，获取小时除以24取余的小时
        //    hour = parseInt(hour % 24)
        //  }
      }
    }

    let result = "" + parseInt(second) + "秒";
    if (minute > 0) {
      result = "" + parseInt(minute) + "分" + result;
    }
    if (hour > 0) {
      result = "" + parseInt(hour) + "小时" + result;
    }
    //  if (day > 0) {
    //    result = '' + parseInt(day) + '天' + result
    //  }
    console.log("result：", result);
    return result;
  },
  //日期转字符串格式
  dateToStr(time) {
    const date = new Date(time);
    var year = date.getFullYear(); //年
    var month = date.getMonth(); //月
    var day = date.getDate(); //日
    var hours = date.getHours(); //时
    var min = date.getMinutes(); //分
    var second = date.getSeconds(); //秒
    return (
      year +
      "-" +
      (month + 1 > 9 ? month + 1 : "0" + (month + 1)) +
      "-" +
      (day > 9 ? day : "0" + day) +
      " " +
      (hours > 9 ? hours : "0" + hours) +
      ":" +
      (min > 9 ? min : "0" + min) +
      ":" +
      (second > 9 ? second : "0" + second)
    );
  },

  isEmpty(value) {
    return value == null || value == undefined || value == "";
  },
  isNotEmpty(value) {
    return value != null && value != undefined && value != "";
  },

  /**
   * 通用提示信息
   * @type {{success: message.success, warning: message.warning, error: message.error, info: message.info}}
   */
  message: {
    success: function(message) {
      Message({
        showClose: true,
        message: message || "成功",
        type: "success"
      });
    },
    warning: function(message) {
      Message({
        showClose: true,
        message: message || "警告",
        type: "warning"
      });
    },
    info: function(message) {
      Message({
        showClose: true,
        message: message || "提示"
      });
    },
    error: function(message) {
      Message({
        showClose: true,
        message: message || "异常",
        type: "error"
      });
    }
  },
  /**
   * 时间日期格式化
   * @param {*} date
   * @param {*} fmt
   * @returns
   */
  formatDate(date, fmt) {
    date = date == undefined ? new Date() : date;
    date = typeof date == "number" ? new Date(date) : date;
    fmt = fmt || "yyyy-MM-dd HH:mm:ss";
    var obj = {
      y: date.getFullYear(), // 年份，注意必须用getFullYear
      M: date.getMonth() + 1, // 月份，注意是从0-11
      d: date.getDate(), // 日期
      q: Math.floor((date.getMonth() + 3) / 3), // 季度
      w: date.getDay(), // 星期，注意是0-6
      H: date.getHours(), // 24小时制
      h: date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, // 12小时制
      m: date.getMinutes(), // 分钟
      s: date.getSeconds(), // 秒
      S: date.getMilliseconds() // 毫秒
    };
    var week = ["天", "一", "二", "三", "四", "五", "六"];
    for (var i in obj) {
      fmt = fmt.replace(new RegExp(i + "+", "g"), function(m) {
        var val = obj[i] + "";
        if (i == "w") return (m.length > 2 ? "星期" : "周") + week[val];
        for (var j = 0, len = val.length; j < m.length - len; j++)
          val = "0" + val;
        return m.length == 1 ? val : val.substring(val.length - m.length);
      });
    }
    return fmt;
  }
};

var BASE64_MAPPING = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "J",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
  "a",
  "b",
  "c",
  "d",
  "e",
  "f",
  "g",
  "h",
  "i",
  "j",
  "k",
  "l",
  "m",
  "n",
  "o",
  "p",
  "q",
  "r",
  "s",
  "t",
  "u",
  "v",
  "w",
  "x",
  "y",
  "z",
  "0",
  "1",
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "+",
  "/"
];

/**
 *ascii convert to binary
 */
var _toBinary = function(ascii) {
  var binary = new Array();
  while (ascii > 0) {
    var b = ascii % 2;
    ascii = Math.floor(ascii / 2);
    binary.push(b);
  }
  /*
	var len = binary.length;
	if(6-len > 0){
		for(var i = 6-len ; i > 0 ; --i){
			binary.push(0);
		}
	}*/
  binary.reverse();
  return binary;
};

/**
 *binary convert to decimal
 */
var _toDecimal = function(binary) {
  var dec = 0;
  var p = 0;
  for (var i = binary.length - 1; i >= 0; --i) {
    var b = binary[i];
    if (b == 1) {
      dec += Math.pow(2, p);
    }
    ++p;
  }
  return dec;
};

/**
 *unicode convert to utf-8
 */
var _toUTF8Binary = function(c, binaryArray) {
  var mustLen = 8 - (c + 1) + (c - 1) * 6;
  var fatLen = binaryArray.length;
  var diff = mustLen - fatLen;
  while (--diff >= 0) {
    binaryArray.unshift(0);
  }
  var binary = [];
  var _c = c;
  while (--_c >= 0) {
    binary.push(1);
  }
  binary.push(0);
  var i = 0,
    len = 8 - (c + 1);
  for (; i < len; ++i) {
    binary.push(binaryArray[i]);
  }

  for (var j = 0; j < c - 1; ++j) {
    binary.push(1);
    binary.push(0);
    var sum = 6;
    while (--sum >= 0) {
      binary.push(binaryArray[i++]);
    }
  }
  return binary;
};

//window.BASE64 = __BASE64;
// module.exports = {
//   CusBASE64: __BASE64
// }

const BASE64 = {
  /**
   *BASE64 Encode
   */
  encoder: function(str) {
    var base64_Index = [];
    var binaryArray = [];
    for (var i = 0, len = str.length; i < len; ++i) {
      var unicode = str.charCodeAt(i);
      var _tmpBinary = _toBinary(unicode);
      if (unicode < 0x80) {
        var _tmpdiff = 8 - _tmpBinary.length;
        while (--_tmpdiff >= 0) {
          _tmpBinary.unshift(0);
        }
        binaryArray = binaryArray.concat(_tmpBinary);
      } else if (unicode >= 0x80 && unicode <= 0x7ff) {
        binaryArray = binaryArray.concat(_toUTF8Binary(2, _tmpBinary));
      } else if (unicode >= 0x800 && unicode <= 0xffff) {
        //UTF-8 3byte
        binaryArray = binaryArray.concat(_toUTF8Binary(3, _tmpBinary));
      } else if (unicode >= 0x10000 && unicode <= 0x1fffff) {
        //UTF-8 4byte
        binaryArray = binaryArray.concat(_toUTF8Binary(4, _tmpBinary));
      } else if (unicode >= 0x200000 && unicode <= 0x3ffffff) {
        //UTF-8 5byte
        binaryArray = binaryArray.concat(_toUTF8Binary(5, _tmpBinary));
      } else if (unicode >= 4000000 && unicode <= 0x7fffffff) {
        //UTF-8 6byte
        binaryArray = binaryArray.concat(_toUTF8Binary(6, _tmpBinary));
      }
    }

    var extra_Zero_Count = 0;
    for (let i = 0, len = binaryArray.length; i < len; i += 6) {
      var diff = i + 6 - len;
      if (diff == 2) {
        extra_Zero_Count = 2;
      } else if (diff == 4) {
        extra_Zero_Count = 4;
      }
      //if(extra_Zero_Count > 0){
      //	len += extra_Zero_Count+1;
      //}
      var _tmpExtra_Zero_Count = extra_Zero_Count;
      while (--_tmpExtra_Zero_Count >= 0) {
        binaryArray.push(0);
      }
      base64_Index.push(_toDecimal(binaryArray.slice(i, i + 6)));
    }

    var base64 = "";
    for (let i = 0, len = base64_Index.length; i < len; ++i) {
      base64 += BASE64_MAPPING[base64_Index[i]];
    }

    for (let i = 0, len = extra_Zero_Count / 2; i < len; ++i) {
      base64 += "=";
    }
    return base64;
  },
  /**
   *BASE64  Decode for UTF-8
   */
  decoder: function(_base64Str) {
    var _len = _base64Str.length;
    var extra_Zero_Count = 0;
    /**
     *计算在进行BASE64编码的时候，补了几个0
     */
    if (_base64Str.charAt(_len - 1) == "=") {
      //alert(_base64Str.charAt(_len-1));
      //alert(_base64Str.charAt(_len-2));
      if (_base64Str.charAt(_len - 2) == "=") {
        //两个等号说明补了4个0
        extra_Zero_Count = 4;
        _base64Str = _base64Str.substring(0, _len - 2);
      } else {
        //一个等号说明补了2个0
        extra_Zero_Count = 2;
        _base64Str = _base64Str.substring(0, _len - 1);
      }
    }

    var binaryArray = [];
    for (var i = 0, len = _base64Str.length; i < len; ++i) {
      var c = _base64Str.charAt(i);
      for (var j = 0, size = BASE64_MAPPING.length; j < size; ++j) {
        if (c == BASE64_MAPPING[j]) {
          var _tmp = _toBinary(j);
          /*不足6位的补0*/
          var _tmpLen = _tmp.length;
          if (6 - _tmpLen > 0) {
            for (var k = 6 - _tmpLen; k > 0; --k) {
              _tmp.unshift(0);
            }
          }
          binaryArray = binaryArray.concat(_tmp);
          break;
        }
      }
    }

    if (extra_Zero_Count > 0) {
      binaryArray = binaryArray.slice(0, binaryArray.length - extra_Zero_Count);
    }

    var unicode = [];
    var unicodeBinary = [];
    for (let i = 0, len = binaryArray.length; i < len; ) {
      if (binaryArray[i] == 0) {
        unicode = unicode.concat(_toDecimal(binaryArray.slice(i, i + 8)));
        i += 8;
      } else {
        var sum = 0;
        while (i < len) {
          if (binaryArray[i] == 1) {
            ++sum;
          } else {
            break;
          }
          ++i;
        }
        unicodeBinary = unicodeBinary.concat(
          binaryArray.slice(i + 1, i + 8 - sum)
        );
        i += 8 - sum;
        while (sum > 1) {
          unicodeBinary = unicodeBinary.concat(binaryArray.slice(i + 2, i + 8));
          i += 8;
          --sum;
        }
        unicode = unicode.concat(_toDecimal(unicodeBinary));
        unicodeBinary = [];
      }
    }
    return unicode;
  },
  decrypt: function(_base64Str) {
    //二次解密
    const data = this.decoder(_base64Str);
    //   var reg = new RegExp('^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$');
    var content = "";
    data.forEach(element => {
      content += String.fromCharCode(element);
    });
    return content;
  }
};

/**
 * 防抖函数
 * func 需要执行防抖的函数逻辑
 * delay 防抖频率默认500毫秒
 * immediately 是否立即执行(默认立即执行)
 */
let debounceTimer;
export function debounce(func, delay = 500, immediately = true) {
  return function() {
    let context = this;
    let args = arguments;
    // 立即执行
    if (immediately && !debounceTimer) {
      func.apply(context, arguments);
    }
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(function() {
      // 如果立即执行则延时器里的不执行
      if (!immediately) {
        func.apply(context, arguments);
      }
      // 置空
      debounceTimer = null;
    }, delay);
  };
}
/**
 * 节流函数
 * func 需要执行节流的函数逻辑
 * delay 节流频率默认500毫秒
 * immediately 是否立即执行(默认立即执行)
 */
let throttleTimer = null;
export function throttle(func, delay = 500, immediately = true) {
  return function() {
    if (throttleTimer) return;
    // 立即执行
    if (immediately && !throttleTimer) {
      func.apply(this, arguments);
    }
    throttleTimer = setTimeout(() => {
      //立即执行模式
      if (!immediately) {
        func.apply(this, arguments);
      }
      throttleTimer = null;
    }, delay);
  };
}

/**
 * 格式化时间
 * @param {*} date
 * @param {*} fmt
 * @returns
 */
function formatDate(date, fmt) {
  let ret;
  let opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString() // 秒
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
}

/**
 *
 * @param {数据对象} data
 * @param {标识} mask
 * @param {起始未知} start
 * @param {结束位置} end
 * @param {数据对象中，需要替换为*号的字段} filed
 * @returns
 */
function maskPhone(data, start, end, mask, filed = "phone") {
  if (!data[filed]) return;
  if (mask) {
    let reg = /^(\d{3})\d{4}(\d{4})$/;
    return data[filed]
      ? data[filed].replace(reg, `$1${"*".repeat(end - start)}$2`)
      : "--";
  }
  return data[filed];
}

export default {
  formatDate,
  ECode,
  SysConf,
  FUNCTIONS,
  BASE64,
  maskPhone
};
