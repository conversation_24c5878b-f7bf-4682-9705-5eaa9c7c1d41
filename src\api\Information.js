import request from '@/utils/request'

// 编辑培训资讯
export function editor(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/edit',
        method: "post",
        data:params
    })
}

// 添加培训资讯
export function add(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/add',
        method: "post",
        data: params
    })
}

//获取培训资讯列表
export function list(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/getList',
        method: "post",
        data: params
    })
}
// 删除培训资讯
export function deleteInformation(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/deleteBatch',
        method: "post",
        data: params
    })
}
//获取培训资讯详情
export function detail(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/getInfo',
        method: "get",
        params
    })
}
// 培训资讯上下架
export function upOrDown(params) {
    return request({
        url: process.env.ADMIN_API + `/trainInfo/updateStatus?status=${params.status}`,
        method: "post",
        data:params.uids
    })
}
// 获取培训资讯类别
export function category(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/getCategoryList',
        method: "get",
        params
    })
}
// 培训资讯排序
export function sort(params) {
    return request({
        url: process.env.ADMIN_API + '/trainInfo/sort',
        method: "post",
        data: params
    })
}