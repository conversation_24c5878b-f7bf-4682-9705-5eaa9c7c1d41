import request from '@/utils/request'

// 同步微信公众号相关信息
export function syncWxOfficialAccountInfo (params) {
  return request({
    url: process.env.ADMIN_API + '/newMedia/syncWxOfficialAccountInfo',
    method: 'get',
    params: params
  })
}


// 获取微信公众号相关信息
export function getWxOfficialAccountInfo (params) {
  return request({
    url: process.env.ADMIN_API + '/newMedia/getWxOfficialAccountInfo',
    method: 'get',
    params: params
  })
}

// 体系信息新增
export function sysAdd (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/add',
    method: 'post',
    data: params
  })
}

// 体系信息更新
export function sysEdit (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/edit',
    method: 'post',
    data: params
  })
}

// 体系信息删除
export function sysDelBatch (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/deleteBatch',
    method: 'post',
    data: params
  })
}

// 体系信息详情
export function sysInfo (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/getInfo',
    method: 'get',
    params: params
  })
}



// 体系信息列表
export function getSysList (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/getList',
    method: 'post',
    data: params
  })
}

// 体系信息列表
export function sysSort (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/sort',
    method: 'post',
    data: params
  })
}

// 体系上下架
export function updateStatus (params) {
  return request({
    url: process.env.ADMIN_API + `/home/<USER>/system/updateStatus?status=${params.status}`,
    method: 'post',
    data: params.uids
  })
}

// 体系置顶
export function updateTop (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/updateTop',
    method: 'post',
    data: params
  })
}

// 体系批量显示
export function showBatch (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/showBatch',
    method: 'post',
    data: params
  })
}

// 体系批量隐藏
export function hideBatch (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/hideBatch',
    method: 'post',
    data: params
  })
}


// 研究报告新增
export function addReport (params) {
  return request({
    url: process.env.ADMIN_API + '/research/report/add',
    method: 'post',
    data: params
  })
}


// 研究报告分页列表
export function getReportList (params) {
  return request({
    url: process.env.ADMIN_API + '/research/report/getList',
    method: 'post',
    data: params
  })
}

// 研究报告信息详情
export function getReportInfo (params) {
  return request({
    url: process.env.ADMIN_API + '/research/report/getInfo',
    method: 'get',
    params: params
  })
}

// 研究报告编辑
export function editReport (params) {
  return request({
    url: process.env.ADMIN_API + '/research/report/edit',
    method: 'post',
    data: params
  })
}

// 研究报告人工审核
export function manualAuditStatus (params) {
  return request({
    url: process.env.ADMIN_API + '/research/report/manualAuditStatus',
    method: 'post',
    data: params
  })
}

// 研究报告批量删除
export function deleteBatchReport (params) {
  return request({
    url: process.env.ADMIN_API + '/research/report/deleteBatch',
    method: 'post',
    data: params
  })
}

// 获取栏目所属体系
export function getListByReport (data = { columnName: '研究报告' }) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/system/getListByColumnName',
    method: 'get',
    params: data
  })
}

// 获取运营作品信息
export function getBusinessInfo (data = { columnName: '研究报告' }) {
  return request({
    url: process.env.ADMIN_API + '/newMedia/getBusinessInfo',
    method: 'get',
    params: data
  })
}