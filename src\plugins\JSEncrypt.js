import JSEncrypt from 'jsencrypt'

export function strEncrypt(str) {
  let encrypt = new JSEncrypt({ default_key_size: 1024 });
  encrypt.setPublicKey('MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDC0NiCinOgDI5rY0iBORHiOqAxiOLzSU/gIitehRMK9SExOsZrlLSgqd8qpEPk8gae5fCX/glrKM/Oemkoidh9WlQ48EPbGI4aXpyaGxCizbHQmx3Mq9ByAOBm/iGS8ERqX3PhGn7FQggOv3FXUfvpjAZDwgm7TlKzbqdD/GbwnwIDAQAB')
  return encrypt.encrypt(str)
}

export function strDecrypt(str) {
  let encrypt = new JSEncrypt({ default_key_size: 1024 });
  encrypt.setPrivateKey('MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAMe/jVH1yMlvklgoYMgLj1+R8BbxRpStbkQ5KCCkHz7/QF5VcprVjYtIHYffXXy78KqsS64O+wFPndCtAHxG9gFj1zo1DQnAcDye+6+ZgY4WBBVEM30ATF4Zr2YUCs6MsRBahzuaKfl9KQRgOscdoXCIDC84p9mMRXxC8Sxuw28VAgMBAAECgYBig2RtYazPPAmPFQr2Xk0ts0oX5pMKzKYvW4kg4BDQ/w599+J33o7afIyj8FOpKv+9DRCeHaFJujx5KHmu0UiN89KyAfhqmcNu0skZMxfun9xVRkInLxY+b3dHYrdTS9oUu0YAvtJHGRDnMMnh11ke65e+C8eHhGMEic14H3l7AQJBAOfBjtXnW//VR4FAoujqhlv9XQF1TwNa2pY3fvpkIhM1d25LqY9D9GmPIbak8z5bnxcuv8YLoBS+OF3TxRIzNLUCQQDcpNKCLujF5VMQpZ4KqqCTBOOcnHWcBPAmlNIosarBIoFTTTuL3Vb+V8/s6kjkjk13l4ypW9aG8/C1n+pCxyzhAkAnaHppVRYHwrSN/fe1s2t1aPfPr6rn3yMcOAOXgD9rFMRiNOt4OQ3uhTAuZo4WJG5A+uoROxjVbiMXkVMJbTuhAkBhcU0ifzL/ytJPZMUSMt6tbxhSXuq115xzfX/+xtABcYqpzk8A6uRzeTZzi9hbT9k4eq59UPjCKtNgctpTWYEhAkAgn639IEunh20GH36TV3PMxTpzfxzfUi2gnxxyP2FJLP+p8Jjw1d9/1iv/cKlH7Aj2HvMmCC8wSxvC0owOa9gw')
  return encrypt.decrypt(str)
}
