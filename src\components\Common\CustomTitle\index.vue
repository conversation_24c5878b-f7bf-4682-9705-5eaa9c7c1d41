<template>
  <div class="title">
    <span :style="{background: systemConfig}"></span>
    <h2>{{ title }}</h2>
  </div>
</template>

<script>
import { getSystemConfig } from "@/api/systemConfig";
export default {
  name: "CustomTitle",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data(){
    return {
      systemConfig:"",
    }
  },
  mounted() {
    this.getConfig();
  },
  methods: {
    getConfig() {
      getSystemConfig().then((response) => {
        if (response.code == this.$ECode.SUCCESS) {
          this.systemConfig = response.data.themeColor;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.title {
  margin-left: 16px;
  margin-bottom: 20px;
  display: flex;
    align-items: center;

  span {
    width: 3px;
    height: 14px;
    display: block;
   }

  h2 {
    padding-left: 10px;
    font-size: 16px;
    margin: 0;
    top: 2px;
    position: relative;
  }
}
</style>
