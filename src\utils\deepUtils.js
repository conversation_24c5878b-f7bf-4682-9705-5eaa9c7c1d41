/**
 * 根据子节点寻找父节点
 * @param {目标子节点id} id 
 * @param {递归数据源} dataResource 
 * @param {目标子节点id自定义名称} idProperty 
 * @param {自定义孩子节点集合名称} childrenProperty 
 * @param {收集结果集} result 
 * @returns 
 */
function findParentByChildren(id, dataResource = [], idProperty = "id", childrenProperty = "children", result = []) {
    for (let i = 0; i < dataResource.length; i += 1) {
        const item = dataResource[i]
        // 找到目标
        if (item[idProperty] === id) {
            // 加入到结果中
            result.push(item)
            // 因为可能在第一层就找到了结果，直接返回当前结果
            if (result.length === 1) return result
            return true
        }
        // 如果存在下级节点，则继续遍历
        if (item[childrenProperty] && item[childrenProperty].length) {
            // 预设本次是需要的节点并加入到最终结果result中
            result.push(item)
            const find = findParentByChildren(id, item[childrenProperty], idProperty, childrenProperty, result)
            // 如果不是false则表示找到了，直接return，结束递归
            if (find) {
                return result
            }
            // 到这里，意味着本次并不是需要的节点，则在result中移除
            result.pop()
        }
    }
    // 如果都走到这儿了，也就是本轮遍历children没找到，将此次标记为false
    return false
}

/**
 * 根据孩子节点uid获取递归链id集合
 * @param {*} id 
 * @param {递归数据源} dataResource 
 * @param {目标子节点id自定义名称} idProperty 
 * @param {自定义孩子节点集合名称} childrenProperty 
 * @returns 
 */
function getIdsByChildrenId(id, dataResource = [], idProperty = "id", childrenProperty = "children") {
    let result = findParentByChildren(id, dataResource, idProperty, childrenProperty);
    if (result) {
        return result.map(item => item[idProperty]);
    } else {
        throw new Error('目标不存在')
    }
}

/**
 * 返回对应递归层级索引值
 * @param {目标层级id标识} id 
 * @param {数据源} dataResource 
 * @param {id属性名} idProperty 
 * @param {孩子属性名} childrenProperty 
 * @returns 
 */
function findTargetZIndex(id, dataResource = [], idProperty = "id", childrenProperty = "children") {
    let result = findParentByChildren(id, dataResource, idProperty, childrenProperty);
    if (result) {
        let ids = result.map(item => (item[idProperty]));
        return ids.indexOf(id) + 1;
    }
    throw new Error('目标不存在')
}


/**
 * 递归数据，属性转换
 * @param {递归数据源} dataResource 
 * @param {属性转换对象} transformObj 
 * @param {孩子属性名} childrenProperty 
 * @returns 
 */
function deepPropertyTransform(dataResource, transformObj, childrenProperty = "children") {
    function transform(dataResource, transformObj, childrenProperty = "children") {
        let panelType = Object.prototype.toString.call(transformObj);
        if (!transformObj || (panelType === '[object Object]' && Object.keys(transformObj).length == 0)) {
            return dataResource;
        }
        if (dataResource.length > 0) {
            dataResource.forEach(item => {
                let children = item[childrenProperty];
                for (let prototypeItem in transformObj) {
                    item[transformObj[prototypeItem]] = item[prototypeItem];
                    delete item[prototypeItem];
                }
                if (children && children.length > 0) {
                    transform(children, childrenProperty, transformObj);
                }
            })
        }
    }
    transform(dataResource, transformObj, childrenProperty);
    return dataResource;
}

export { 
    deepPropertyTransform,
    findTargetZIndex,
    getIdsByChildrenId,
    findParentByChildren
}