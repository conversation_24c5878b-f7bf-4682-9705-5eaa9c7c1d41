<!-- create by lbin -->
<template>
  <div class="content-config">
    <div class="button-top">
      <el-button
        :type="isVersion ? 'primary' : ''"
        class="el-button-width"
        @click="
          isVersion = true;
          getConfigList();
          getEquityList();
        ">
        版本配置
      </el-button>
      <el-button :type="!isVersion ? 'primary' : ''" class="el-button-width" @click="isVersion = false">
        权益配置
      </el-button>
    </div>
    <div v-if="isVersion" class="add-tabs" style="right:60px">
      <el-button class="el-button-width" icon="el-icon-plus" @click="openTabDialog(true)">
        添加
      </el-button>
    </div>
    <el-tabs v-if="isVersion" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.name" :name="item.uid">
        <div class="config-header">
          <div class="header-left">
            <el-image :src="item.iconUrl" style="width: 60px; height: 60px" fit="contain" />
            <div class="header-main">
              <div class="header-main-top">
                <span>{{ item.name }}</span>
                <span>{{ item.configDescribe }}</span>
              </div>
              <div class="header-main-bottom">
                <span
                  v-if="
                    item.priceList == undefined ||
                      item.priceList == null ||
                      item.priceList == [] ||
                      tabParam.uid == '3ce4f7d6969e4c31aabbeb7c9926c890'
                  ">
                  <span style="color: #ff3f3f">
                    免费
                  </span>
                </span>
                <span v-for="(priceItem, priceIndex) in item.priceList" v-else :key="priceIndex" class="threePrice">
                  <span style="color: #ff3f3f">
                    {{
                      `￥${priceItem.price}
                    / ${priceItem.unit == 1 ? "" : priceItem.unit}
                    ${priceItem.time}`
                    }}
                  </span>
                </span>
              </div>
            </div>
          </div>
          <div class="right">
            <el-button class="el-button-width" type="text" icon="el-icon-edit" @click="openTabDialog(false)">
              编 辑
            </el-button>
            <el-button
              v-if="item.uid != '3ce4f7d6969e4c31aabbeb7c9926c890'"
              class="el-button-width"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete">
              删 除
            </el-button>
          </div>
        </div>
        <div class="config-main">
          <div class="config-main-header">
            <span>拥有权益</span>
            <div />
          </div>
          <div class="config-main-center">
            <div v-for="equityItem in versionEquityList" :key="equityItem.uid" class="center-common">
              <div class="title">{{ equityItem.name }}</div>
              <div
                v-if="
                  equityItem.equityDetailDtoList != null &&
                    equityItem.equityDetailDtoList.length != 0
                ">
                <el-checkbox-group v-model="item.configEquityUids">
                  <el-checkbox
                    v-for="equityDetaiItem in equityItem.equityDetailDtoList"
                    :label="equityDetaiItem.uid"
                    :key="equityDetaiItem.uid"
                    disabled>
                    {{ equityDetaiItem.detailName }}
                    <el-radio-group
                      v-if="
                        equityDetaiItem.flagList != undefined &&
                          equityDetaiItem.flagList != null &&
                          equityDetaiItem.flagList.length != 0
                      "
                      :value="
                        getIsFlag(equityDetaiItem.flagList, item.equityFlagList)
                      "
                      style="margin-left:10px">
                      <el-radio
                        v-for="flagItem in equityDetaiItem.flagList"
                        :key="flagItem.uid"
                        :label="flagItem.remark"
                        disabled>
                        {{ flagItem.remark }}
                      </el-radio>
                    </el-radio-group>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <ConfigEquity v-if="!isVersion" />

    <el-dialog :title="isAddTab ? '添加实验室版本' : '编辑实验室版本'" :visible.sync="addTabDialog" width="900px">
      <el-form ref="tabForm" :rules="tabFormRules" :model="tabForm" label-width="80px">
        <el-form-item label="名称" prop="name">
          <div style="width: 200px">
            <el-input v-model="tabForm.name" placeholder="请输入名称" />
          </div>
        </el-form-item>
        <!--        <el-form-item label="类型" prop="type">-->
        <!--          <el-radio-group v-model="tabForm.type">-->
        <!--            <el-radio-->
        <!--              v-for="item in configVersionList"-->
        <!--              :key="item.uid"-->
        <!--              :label="item.dictValue"-->
        <!--            >-->
        <!--              {{ item.dictLabel }}-->
        <!--            </el-radio>-->
        <!--          </el-radio-group>-->
        <!--        </el-form-item>-->
        <el-form-item label="描述" prop="configDescribe">
          <el-input v-model="tabForm.configDescribe" placeholder="添加描述" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="亮点描述" prop="highlightDescribe">
          <div style="display: flex; justify-content: space-between">
            <el-input v-model="tabForm.highlightDescribe1" placeholder="输入亮点" />
            <el-input v-model="tabForm.highlightDescribe2" style="margin: 0 20px" placeholder="输入亮点" />
            <el-input v-model="tabForm.highlightDescribe3" placeholder="输入亮点" />
          </div>
        </el-form-item>
        <el-form-item label="icon" prop="icon">
          <div class="icon-main">
            <div class="icon-left">
              <div v-if="showIcon" class="upload_img">
                <img
                  :src="tabForm.iconUrl"
                  style="width: 60px; height: 60px"
                  @mouseover="icon = true"
                  @mouseout="icon = false">
                <i v-show="icon" class="el-icon-error close-icon" @mouseover="icon = true" @click="showIcon = false" />
              </div>
              <div v-else class="uploadImgBody" @click="iconDialog = true">
                <i class="el-icon-plus avatar-uploader-icon" />
              </div>
            </div>
            <div class="icon-right">
              建议尺寸60px*60px，JPG、PNG、JEPG格式，图片小于2M
            </div>
          </div>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: flex-start;
            ">
            <span v-for="(item, index) in tabForm.price" :key="index" style="margin-right:20px">
              <el-input v-model="item.price" placeholder="价格" class="price-select" size="mini" clearable />
              <!-- <el-select
                class="price-select"
                size="mini"
                clearable
                filterable
                allow-create
                default-first-option
                v-model="item.price"
                placeholder="价格"
              >
                <el-option
                  v-for="item in priceOptions.price"
                  :key="item.uid"
                  :label="item.dictValue"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select> -->
              <el-select v-model="item.unit" class="price-select" size="mini" clearable placeholder="时间">
                <el-option
                  v-for="item in priceOptions.unit"
                  :key="item.uid"
                  :label="item.dictValue"
                  :value="item.uid" />
              </el-select>
              <el-select v-model="item.time" class="price-select" size="mini" clearable placeholder="单位">
                <el-option
                  v-for="item in priceOptions.time"
                  :key="item.uid"
                  :label="item.dictLabel"
                  :value="item.dictValue" />
              </el-select>
            </span>

            <el-button v-if="tabForm.price.length < 5" circle icon="el-icon-plus" @click="addPrice" />
            <el-button v-if="tabForm.price.length > 1" circle icon="el-icon-minus" @click="reducePrice" />
          </div>
        </el-form-item>
        <el-form-item label="排序值" prop="sort">
          <div style="width: 200px">
            <el-input v-model="tabForm.sort" placeholder="请输入排序值" />
          </div>
        </el-form-item>
        <el-form-item v-for="item in versionEquityList" :key="item.uid" :label="item.name" prop="configEquityUids">
          <div
            v-if="
              item.equityDetailDtoList != null &&
                item.equityDetailDtoList.length != 0
            ">
            <el-checkbox-group v-model="tabForm.configEquityUids">
              <el-checkbox v-for="(iItem, iIndex) in item.equityDetailDtoList" :label="iItem.uid" :key="iItem.uid">
                {{ iItem.detailName }}
                <span
                  v-if="
                    iItem.flagList != undefined &&
                      iItem.flagList != null &&
                      iItem.flagList.length != 0
                  ">
                  <span v-for="flagItem in iItem.flagList" :key="flagItem.uid" style="margin-left:10px">
                    <!-- <span> -->
                    <span v-if="tabForm.flagList[iItem.uid]">
                      <el-radio
                        :disabled="
                          tabForm.configEquityUids.indexOf(iItem.uid) == -1
                        "
                        :label="flagItem.uid"
                        v-model="tabForm.flagList[iItem.uid].value">
                        {{ flagItem.remark }}
                      </el-radio>
                    </span>
                  </span>
                </span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <div class="dialog-button">
          <el-button class="el-button-width" @click="addTabDialog = false">
            关 闭
          </el-button>
          <el-button class="el-button-width" type="primary" @click="submitTab">
            提 交
          </el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog :visible.sync="iconDialog" width="400px">
      <el-upload
        ref="upload"
        :action="uploadPictureHost"
        :data="otherData"
        :on-error="_uploadImgError"
        :before-upload="_beforeUpload"
        :on-success="_uploadImgSuccess"
        :show-file-list="false"
        :headers="{
          Authorization:$GetToken()
        }"
        name="file"
        drag>
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import ConfigEquity from '@/components/ConfigEquity'
import { getToken } from '@/utils/auth'
import {
  addConfig,
  configList,
  deleteConfig,
  editConfig,
  equityList
} from '@/api/upConfig'
import { getListByDictType, getListByDictTypeList } from '@/api/sysDictData'

export default {
  components: {
    ConfigEquity
  },
  data() {
    var validateHighlightDescribe = (rule, value, callback) => {
      if (
        !this.isNotEmpty(this.tabForm.highlightDescribe1) &&
        !this.isNotEmpty(this.tabForm.highlightDescribe3) &&
        !this.isNotEmpty(this.tabForm.highlightDescribe2)
      ) {
        callback(new Error('亮点描述不能为空'))
      } else {
        callback()
      }
    }
    var validatePrice = (rule, value, callback) => {
      // if (this.tabForm.type == 1) {
      //   callback();
      // }
      let index = 0
      // let regNum = /^[0-9]\d*$/;
      const regNum = /^(([1-9]{1}\d*)|(0{1}))(\.\d{0,2})?$/
      value.forEach(item => {
        if (
          this.isNotEmpty(item.price) &&
          this.isNotEmpty(item.unit) &&
          this.isNotEmpty(item.time)
        ) {
          index++
          if (!this.isAddTab && !item.configUid) {
            item.configUid = this.tabParam.uid
          }
          if (!regNum.test(item.price)) {
            callback(new Error('请为价格输入自然数或至小数点后两位'))
          }
          this.priceOptions.unit.forEach(uItem => {
            if (item.unit == uItem.uid) {
              item.unit = uItem.dictValue
              item.chinaUnit = uItem.dictLabel
            }
          })
        }
      })
      if (index != value.length) {
        callback(new Error('请补充完价格信息'))
      }
      callback()
    }
    return {
      icon: false,
      tabParam: {},
      showIcon: false,
      isVersion: true,
      activeName: '0',
      iconDialog: false,
      uploadPictureHost: null,
      addTabDialog: false,
      isAddTab: false,
      tabForm: {
        uid: null,
        name: null,
        configEquityUids: [],
        configDescribe: null,
        advantageDescribe: null,
        iconUrl: null,
        icon: null,
        sort: null,
        highlightDescribe1: null,
        highlightDescribe2: null,
        highlightDescribe3: null,
        price: [{ price: '', unit: '', time: '' }],
        halfAYearPrice: null,
        onePrice: null,
        twoPrice: null,
        threePrice: null,
        // flagList: [{ value: "" }, { value: "" }],
        flagList: {},
        type: null
      },
      configVersionList: [],
      addLeft: null, // 顶部tab距离左侧的长度
      versionEquityList: [],
      tabList: [],
      priceOptions: {},
      tabFormRules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        type: [
          {
            required: true,
            message: '类型不能为空',
            trigger: ['blur', 'change']
          }
        ],
        configDescribe: [
          { required: true, message: '描述不能为空', trigger: 'blur' }
        ],
        configEquityUids: [
          {
            required: true,
            message: '权益不能为空',
            trigger: ['blur', 'change']
          }
        ],
        highlightDescribe: [
          {
            required: true,
            trigger: 'blur',
            validator: validateHighlightDescribe
          }
        ],
        price: [
          {
            required: true,
            validator: validatePrice,
            trigger: ['blur', 'change']
          }
        ]
      }
    }
  },

  mounted() { },

  created() {
    // 图片上传地址
    this.uploadPictureHost = process.env.PICTURE_API + '/file/cropperPicture'
    this.otherData = {
      source: 'picture',
      userUid: 'uid00000000000000000000000000000000',
      adminUid: 'uid00000000000000000000000000000000',
      projectName: 'blog',
      sortName: 'admin',
      token: getToken()
    }
    this.getConfigList()
    this.getEquityList()
    this.getDictList()
  },

  methods: {
    getDictList() {
      var dictTypeList = [
        'config_price_time',
        'config_price_unit',
        'config_price_price',
        'config_version'
      ]

      getListByDictTypeList(dictTypeList).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          const dictMap = response.data
          if (dictMap.config_price_time) {
            this.priceOptions.time = dictMap.config_price_time.list
          }
          if (dictMap.config_price_unit) {
            this.priceOptions.unit = dictMap.config_price_unit.list
          }
          if (dictMap.config_price_price) {
            this.priceOptions.price = dictMap.config_price_price.list
          }
          if (dictMap.config_version) {
            dictMap.config_version.list.forEach(item => {
              if (item.isPublish != 0 && item.isPublish != '0') {
                this.configVersionList.push(item)
              }
            })
          }
        }
      })
    },

    // 获取版本类型
    getConfigVersionList() {
      getListByDictType({ dictType: 'config_version' }).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          response.data.list.forEach(item => {
            if (item.isPublish != 0 && item.isPublish != '0') {
              this.configVersionList.push(item)
            }
          })
        } else {
          this.$commonUtil.message.error(response.message)
        }
      })
    },
    // 拥有的权限
    getConfigList() {
      configList().then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          const list = response.data
          list.forEach(
            item => (item.configEquityUids = item.configEquityUids.split(','))
          )
          this.tabList = list
          if (response.data.length != 0) {
            this.activeName = list[0].uid
            this.handleClick()
          }

          this.getTapTopWidth(list.length)
        } else {
          this.$commonUtil.message.error(response.message)
        }
      })
    },

    getEquityList() {
      equityList().then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.versionEquityList = response.data
        } else {
          this.$commonUtil.message.error(response.message)
        }
      })
    },

    handleClick() {
      this.tabParam = Object.assign(
        {},
        this.tabList.find(item => item.uid == this.activeName)
      )
    },

    // 添加实验室版本按钮位置
    getTapTopWidth(length) {
      var addLeft = 20
      if (length == 1) {
        addLeft = 40 + 100
      } else if (length == 2) {
        addLeft = 40 + 100 + 100
      } else if (length >= 3) {
        addLeft = 40 + 100 + 100 + (length - 2) * 100
      }
      this.addLeft = addLeft
    },

    // 删除
    handleDelete() {
      this.$confirm('此操作将把该版本删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteConfig({
            configUid: this.tabList.find(item => item.uid == this.activeName)
              .uid
          }).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
              this.getConfigList()
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消删除')
        })
    },

    openTabDialog(flag) {
      this.addTabDialog = true
      this.isAddTab = flag
      this.setFormFlagList()
      if (flag) {
        this.tabForm.uid = null
        this.tabForm.name = null
        this.tabForm.iconUrl = null
        this.tabForm.icon = null
        this.tabForm.sort = null
        this.tabForm.configDescribe = null
        this.tabForm.highlightDescribe1 = null
        this.tabForm.highlightDescribe2 = null
        this.tabForm.highlightDescribe3 = null
        this.tabForm.type = null
        this.tabForm.configEquityUids = []
        this.tabForm.price = [{ price: '', unit: '', time: '' }]
        this.showIcon = false
      } else {
        const params = JSON.parse(JSON.stringify(this.tabParam))
        const describe = params.advantageDescribe.split(',')
        this.tabForm.uid = params.uid
        this.tabForm.name = params.name
        this.tabForm.iconUrl = params.iconUrl
        this.tabForm.icon = params.icon
        this.tabForm.sort = params.sort
        this.tabForm.configDescribe = params.configDescribe
        this.tabForm.highlightDescribe1 = describe[0]
        this.tabForm.highlightDescribe2 = describe[1]
        this.tabForm.highlightDescribe3 = describe[2]
        this.tabForm.configEquityUids = params.configEquityUids
        this.tabForm.type = params.type + ''
        this.tabForm.price = params.priceList
        this.tabForm.price.sort((a, b) => {
          return b.createTime < a.createTime ? 1 : -1
        })
        this.showIcon = !!params.icon
      }
    },

    setFormFlagList() {
      this.tabForm.flagList = {}
      const list = Object.assign([], this.versionEquityList)
      const equityFlagList = this.tabParam.equityFlagList
        ? this.tabParam.equityFlagList.split(',')
        : undefined
      list.forEach(item => {
        if (
          item.equityDetailDtoList != null &&
          item.equityDetailDtoList.length != 0
        ) {
          item.equityDetailDtoList.forEach(equityItem => {
            if (
              equityItem.flagList == undefined ||
              equityItem.flagList == null ||
              equityItem.flagList.length == 0
            ) {
              return
            } else {
              let value = ''
              if (equityFlagList && !this.isAddTab) {
                for (let i = 0; i < equityFlagList.length; i++) {
                  const temValue = equityItem.flagList.find(flagItem => {
                    return equityFlagList[i] == flagItem.uid
                  })
                  if (temValue) {
                    value = temValue ? temValue.uid : ''
                    break
                  }
                }
              }

              // 把包含3级 radio选项的数据存储起来，用ui做键名，在 dom 的循环渲染中直接用 遍历的 id  双向绑定this.tabForm.flagList对应的属性
              const n = {}
              n[equityItem.uid] = { detailUid: equityItem.uid, value }
              this.tabForm.flagList = { ...JSON.parse(JSON.stringify(this.tabForm.flagList)), ...n }
            }
          })
        }
      })
    },

    // 上传封面图片
    _uploadImgError(err, file, fileList) {
      this.$message.error('文件上传失败，请重试！')
    },
    _uploadImgSuccess(response, file) {
      if (response.code == this.$ECode.SUCCESS) {
        this.tabForm.icon = response.data[0].uid
        this.tabForm.iconUrl = response.data[0].url
        this.showIcon = true
        this.iconDialog = false
      } else {
        this.$alert(response.data, '提示', {
          confirmButtonText: '确定'
        })
      }
    },
    _beforeUpload(file) {
      const types = ['image/jpg', 'image/png', 'image/jpeg']
      const isJPG = types.includes(file.type)
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg或png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },

    isNotEmpty(value) {
      if (value == 0) {
        return true
      }
      return value != null && value != undefined && value != ''
    },

    submitTab() {
      this.$refs.tabForm.validate(valid => {
        if (!valid) return
        var params = {}
        params.uid = this.tabForm.uid
        params.name = this.tabForm.name
        params.type = this.tabForm.type
        params.sort = this.tabForm.sort
        params.configDescribe = this.tabForm.configDescribe

        // 亮点描述
        params.advantageDescribe = `${this.isNotEmpty(this.tabForm.highlightDescribe1)
          ? this.tabForm.highlightDescribe1
          : ''
        }${this.isNotEmpty(this.tabForm.highlightDescribe2)
          ? ',' + this.tabForm.highlightDescribe2
          : ''
        }${this.isNotEmpty(this.tabForm.highlightDescribe3)
          ? ',' + this.tabForm.highlightDescribe3
          : ''
        }`

        // 版本拥有的权益编号
        const uids = []
        this.tabForm.configEquityUids.forEach(item => {
          this.versionEquityList.forEach(equityItem => {
            if (
              equityItem.equityDetailDtoList != null &&
              equityItem.equityDetailDtoList.length != 0
            ) {
              equityItem.equityDetailDtoList.forEach(detailItem => {
                if (item == detailItem.uid) {
                  return uids.push(item)
                }
              })
            }
          })
        })
        params.configEquityUids = uids.toString()

        // 价格
        params.price = this.tabForm.price

        params.icon = this.tabForm.icon

        // 权益标识：时间等
        params.equityFlagList = Object.keys(this.tabForm.flagList).map(i => { return this.tabForm.flagList[i] })
          .filter(item => {
            if (
              item.value != '' &&
              params.configEquityUids.indexOf(item.detailUid) != -1
            ) {
              return item.value
            }
          })
          .map(i => i.value)
          .toString()
        if (params.equityFlagList == '') {
          params.equityFlagList = 'nothing'
        }

        if (this.isAddTab) {
          addConfig(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
              this.getConfigList()
              this.addTabDialog = false
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        } else {
          editConfig(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
              this.getConfigList()
              this.addTabDialog = false
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        }
      })
    },

    addPrice() {
      if (this.tabForm.price.length == 5) return
      this.tabForm.price.push({ price: '', unit: '', time: '' })
    },
    reducePrice() {
      this.tabForm.price.pop()
    },

    getIsFlag(list, equityFlagList) {
      const flagList = equityFlagList.split(',')
      const value = list.find(item => {
        const it = flagList.find(flagItem => flagItem == item.uid)
        return it == item.uid
      })
      return value ? value.remark : ''
    },

    setTabNull() { }
  }
}
</script>
<style lang="scss" scoped>
.content-config {
  padding: 0 20px 20px 20px;
  position: relative;

  .config-header {
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .header-main {
        margin-left: 20px;
        height: 60px;
        display: flex;
        justify-content: space-around;
        flex-direction: column;

        .header-main-top {
          span:nth-child(1) {
            font-weight: bolder;
            font-size: medium;
          }

          span:nth-child(2) {
            margin-left: 10px;
            color: #bbbbbb;
          }
        }

        .header-main-bottom {
          .onePrice {}

          .twoPrice {
            margin-left: 30px;
          }

          .threePrice {
            margin-right: 30px;
          }
        }
      }
    }
  }

  .config-main {
    .config-main-header {
      padding: 20px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: large;
        font-weight: bolder;
      }

      // /deep/.el-button {
      //   padding: 6px;
      //   width: 100px;
      //   height: 30px;
      // }
    }

    .config-main-center {
      .center-common {
        padding: 20px 20px 0 20px;

        .title {
          font-weight: bold;
        }

        .el-checkbox-group {
          margin-top: 20px;
        }
      }
    }
  }
}

/deep/.el-button-width {
  padding: 6px;
  width: 100px;
  height: 30px;
}

.add-tabs {
  position: absolute;
  top: 50px;
  z-index: 3;
}

.upload_img {
  width: 60px;
  height: 60px;
  position: relative;

  .close-icon {
    // display: none;
    right: 2px;
    top: 2px;
    position: absolute;
    font-size: 24px;
    display: inline-block;
    z-index: 8;
    cursor: pointer;
  }

  // img:hover + .close-icon {
  //   font-size: 24px;
  //   display: inline-block;
  //   z-index: 8;
  // }
}

.uploadImgBody {
  margin-left: 5px;
  width: 60px;
  height: 60px;
  border: dashed 1px #c0c0c0;
  float: left;
  position: relative;
}

.uploadImgBody :hover {
  border: dashed 1px #00ccff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
}

/deep/ .price-input {
  width: 130px;
  margin-right: 10px;
}

/deep/ .el-form-item {
  // margin-bottom: 10px;
}

.dialog-button {
  width: 100%;
  display: flex;
  justify-content: center;
}

.icon-main {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .icon-right {
    margin-left: 20px;
    color: #bbbbbb;
  }
}

/deep/ .el-dialog {
  // margin-top: 100px;
  .el-dialog__body {
    padding-top: 15px;
  }
}

.button-top {
  margin: 15px 0;
}

/deep/ .price-select {
  width: 80px;
}
</style>
