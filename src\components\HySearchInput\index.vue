<template>
    <div class="hySearchInputContainer">
        <div class="search" :style="versionWidth">
            <el-input @input="checkFn" :style="{ width: pxUnit(width) }" @focus="searchFocus" @blur="searchBlur"
                @clear="clearableFn" @keyup.enter.native="realSearch" clearable :placeholder="my_placeholder"
                v-model="content" :class="[searchFocusFlag ? 'rightBorderActive' : '']" class="searchInput">
                <!-- 未激活状态 -->
                <template v-if="!searchFocusFlag" slot="append">
                    <i class="el-icon-search"></i>
                </template>
                <!-- <el-button v-if="!searchFocusFlag" class="icon borderActive" slot="append" icon="el-icon-search">
                </el-button> -->
                <!-- 激活状态 -->
                <el-button v-else slot="append" @click.stop="realSearch">搜索</el-button>
            </el-input>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        width: {
            type: String | Number,
            default: '377px'
        },
        placeholder: {
            type: String,
            default: "搜索用户名、图片名称"
        },
        height: {
            type: String | Number,
            default: '46px'
        },
        inputVal: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            my_placeholder: '',
            content: "",
            timer: null,
            versionWidth: '--input-height: 46px',
            searchFocusFlag: false
        }
    },
    watch: {
        inputVal: {
            handler(newVal, oldVal) {
                this.content = newVal;
            },
            immediate: true
        },
        content(newVal, oldVal) {
            this.$emit("update:inputVal", newVal);
        },
        placeholder: {
            handler(newVal, oldVal) {
                this.my_placeholder = newVal;
            },
            immediate: true
        },
    },
    mounted() {
        this.my_placeholder = this.placeholder
        this.versionWidth = `--input-height: ${this.height}`
    },
    methods: {
        pxUnit(value) {
            value = new String(value);
            if (value.endsWith("px") || value.endsWith("PX")) {
                let numVal = value.replace(/px|PX/g, "");
                if (Number(numVal) || Number(numVal) == 0) {
                    return numVal + "px";
                } else {
                    throw new TypeError("请传入正确的数值");
                }
            } else if (value.endsWith("%")) {
                return value;
            }
            if (Number(value) || Number(value) == 0) {
                return value + "px";
            } else {
                throw new TypeError("请传入正确的数值");
            }
        },
        // 搜索框获得焦点事件
        searchFocus() {
            this.searchFocusFlag = true;
        },
        // 搜索框失去焦点事件
        searchBlur() {
            if (this.timer) clearTimeout(this.timer);
            //解决v-if过快销毁DOM元素导致onclick无法触发问题
            this.timer = setTimeout(() => {
                this.searchFocusFlag = false;
            }, 150);
        },
        realSearch() {
            this.$emit("realSearch");
        },
        clearableFn() {
            //输入框清空事件
            this.$emit("update:inputVal", '');
            this.$emit("clearable");
        },
        checkFn(v) {
            //去空格
            this.content = v.replace(/\s+/g, '')
            //去除下划线
            this.content = this.content.replace(/\_/g, '')
        }
    },
    beforeDestroy() {
        if (this.timer) clearTimeout(this.timer);
    }
}
</script>

<style lang="scss" scoped>
.hySearchInputContainer {

    .search {
        .rightBorderActive {
            /deep/ .el-input-group__append {
                border-color: #006eff;
                background: #006EFF !important;
                color: white !important;
            }

        }

        .searchInput {
            /deep/ .el-input__suffix {
                line-height: var(--input-height);
            }

            /deep/ .el-input__inner {
                border-right: none;
            }

            /deep/ .el-input-group__append {
                background: white;
            }

            /deep/ .el-input__inner {
                width: 100%;
                height: var(--input-height);
            }

            .icon {
                height: 100%;
                background: white;
            }

            &:hover {
                /deep/ .el-input-group__append {
                    background: white;
                    color: #006EFF;
                }
            }
        }
    }
}
</style>
