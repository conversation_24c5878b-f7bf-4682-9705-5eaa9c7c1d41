import request from "@/utils/request";

export function login(param) {
  return request({
    url: process.env.AUTH_API + "/admin/auth/login",
    method: "post",
    data: param
  });
}

export function getInfo(token) {
  return request({
    url: process.env.ADMIN_API + "/auth/info",
    method: "get",
    // params: { token }
  });
}

export function getMenu() {
  return request({
    url: process.env.ADMIN_API + "/auth/getMenu",
    method: "get"
  });
}

export function logout() {
  return request({
    url: process.env.AUTH_API + "/admin/auth/logout",
    method: "post"
  });
}



// 获取登录验证码
export function getLoginCaptcha() {
  return request({
    url: process.env.ADMIN_API + "/auth/getLoginCaptcha",
    method: "get"
  });
}

// 获取后台登录背景页
export function getAdminLoginPictureUrl(params) {
  return request({
    url: process.env.ADMIN_API + "/auth/getAdminLoginPictureUrl",
    method: "get",
    params
  });
}

// 后台登录页相关简介
export function getLoginIndexConfig() {
  return request({
    url: process.env.ADMIN_API + "/auth/getLoginIndexConfig",
    method: "get"
  });
}
