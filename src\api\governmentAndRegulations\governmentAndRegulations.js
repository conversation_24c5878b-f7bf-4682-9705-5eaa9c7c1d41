import request from '@/utils/request'

/**
 * 添加国家标准
 * @param params
 */
export function addInfo(params) {
    return request({
        url: process.env.ADMIN_API + '/regulation/add',
        method: 'post',
        data: params
    })
}

/**
 * 编辑国家标准
 * @param {*} params 
 * @returns 
 */
export function editInfo(params) {
    return request({
        url: process.env.ADMIN_API + '/regulation/edit',
        method: 'post',
        data: params
    })
}

/**
 * 删除国家标准
 * @param {*} params 
 * @returns 
 */
export function deleteInfo(params) {
    return request({
        url: process.env.ADMIN_API + '/regulation/delete',
        method: 'post',
        data: params
    })
}


/**
 * 获取国家或法规列表
 * @param {*} params 
 * @returns 
 */
export function getList(params) {
    return request({
        url: process.env.ADMIN_API + '/regulation/select',
        method: 'post',
        data: params
    })
}

///huanyu-admin/regulation/getList
