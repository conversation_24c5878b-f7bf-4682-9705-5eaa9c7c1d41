import request from '@/utils/request'

// 添加训练营
export function addCamp(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/addCamp',
        method: 'post',
        data: params
    })
}

// 编辑训练营
export function editCamp(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/editCamp',
        method: 'post',
        data: params
    })
}
// 删除训练营
export function delCamp(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/deleteCampBatchIds',
        method: 'post',
        data: params
    })
}

// 获取训练营列表
export function getCampList(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/getCampList',
        method: 'get',
        params
    })
}

// 添加营期
export function addCampPeriod(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/addCampPeriod',
        method: 'post',
        data:params
    })
}

// 查询营期列表
export function getCampPeriodList(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/getCampPeriodList',
        method: 'get',
        params:params
    })
}

// 编辑营期
export function editorCampPeriod(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/editCampPeriod',
        method: 'post',
        data:params
    })
}

//删除营期
export function delCampPeriod(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/deleteCampPeriodBatchIds',
        method: 'post',
        data:params
    })
}

//获取营期列表（用于营期复用）
export function getPeriodNameList(params = {}) { 
    return request({
        url: process.env.ADMIN_API + '/trainCamp/getPeriodNameList',
        method: 'get',
        params
    })
}

//复用营期
export function multiplexingCampPeriod(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/trainCamp/multiplexingCampPeriod',
        method: 'get',
        params
    })
}