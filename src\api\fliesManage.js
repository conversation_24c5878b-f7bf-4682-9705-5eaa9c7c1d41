// 后台 - 设置 -资源管理 - 文件管理

import request from '@/utils/request'

// 获取
export function getFileList(params) {
  return request({
    url: process.env.ADMIN_API + '/picture/getFileList',
    method: 'get',
    params: params
  })
}

// 上传文件
export function uploadFile(params) {
  return request({
    url: process.env.PICTURE_API + '/file/competitionUploadFile',
    method: 'post',
    data: params
  })
}

// 编辑文件

export function editFile(params) {
  return request({
    url: process.env.PICTURE_API + '/file/editFile',
    method: 'post',
    data: params
  })
}

// 删除文件

export function deleteFile(params) {
  return request({
    url: process.env.PICTURE_API + '/file/deleteFile',
    method: 'post',
    data: params
  })
}
