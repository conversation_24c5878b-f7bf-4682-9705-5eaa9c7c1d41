<template>
  <div class="slide-tab">
    <ul>
      <div ref="bottomLine" class="bottom-line"/>
      <li
        v-for="(item,index) in tabArr"
        :key="'bat'+index"
        :class="{'active': index === activeInd}"
        @click="_tabClick(index)"
      >{{ item }}</li>
      <slot :data="{index: activeInd}"/>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'SlideTab',
  props: {
    tabArr: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    },
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeInd: 0
    }
  },
  // ['tabArr'],
  watch: {
    // activeIndex: function (val) {
    //   // console.log('object132', val)
    //   this._tabClick(val)
    // },
  },
  mounted() {
    this._tabClick(this.activeIndex)
  },
  methods: {
    _tabClick(index) {
      this.activeInd = index
      this.tlef = 125 * index
      this.$refs.bottomLine.style.transform = `translateX(${this.tlef}px)`
      this.$emit('tabClick', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.slide-tab {
  width: 100%;
  height: 100%;
  ul {
    width: 100%;
    height: 70px;
    margin: 0;
    background-color: #fff;
    padding: 22px 21px 16px 32px;
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    // @include paddingBoxSizing(22px 21px 16px 32px);
    // @include flex(flex-start);
    position: relative;
    .el-button {
      position: absolute;
      top: 20px;
      right: 20px;
      span {
        margin-left: 0;
      }
    }
    li {
      color: #666;
      font-size: 16px;
      margin-right: 42px;
      width: 83px;
      height: 30px;
      list-style: none;
      cursor: pointer;
      transition: 1s;
      display: flex;
      align-items: center;
      justify-content: center;
      // @include flex(center, flex-start);
      &:hover,
      &.active {
        color: #328fe6;
      }
    }
    .bottom-line {
      width: 30px;
      height: 3px;
      background-color: #328fe6;
      position: absolute;
      bottom: 16px;
      left: 60px;
      transition: 0.5s;
    }
  }
}
</style>
