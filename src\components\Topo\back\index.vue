<template>
  <div v-loading="graphLoading" :element-loading-text="graphLoadingText" class="_tp_container">
    <div class="_tp_tools">
      <div v-if="showSave" class="_tp_operation_column">
        <div>
          <img src="@/assets/reset.png" alt="" title="居中" @click="moveView">
          <img v-if="enabled" src="@/assets/choicegrouping.png" alt="" title="分组" @click="enabledFn">
          <img v-else src="@/assets/grouping.png" alt="" title="分组" @click="enabledFn">
        </div>
        <div>
          <img v-if="currentArrow == '1'" src="@/assets/choicestraightline.png" alt="" title="直线">
          <img v-else src="@/assets/straightline.png" alt="" title="直线" @click="changeEdgeType('normal')">
          <img v-if="currentArrow == '2'" src="@/assets/choicecurve.png" title="曲线" alt="">
          <img v-else src="@/assets/curve.png" alt="" title="曲线" @click="changeEdgeType('smooth')">
          <img v-if="currentArrow == '3'" src="@/assets/choicebrokenline.png" title="直角" alt="">
          <img v-else src="@/assets/brokenline.png" alt="" title="直角" @click="changeEdgeType('')">
        </div>
        <div>
          <img src="@/assets/enlarge.png" alt="" title="放大" @click="setZoom(graph,0.1)">
          <img src="@/assets/narrow.png" alt="" title="缩小" @click="setZoom(graph,-0.1)">
        </div>
      </div>
      <el-button v-if="showSave" type="primary" size="mini" @click="handleSaveTopo">确定</el-button>
      <el-button type="danger" size="mini" @click="cancel">取消</el-button>
      <!-- <el-button v-if="showStart" type="primary" size="mini" @click="handleStartTopo">启动</el-button>
      <el-button v-if="showRelease" type="primary" size="mini" @click="handleReleaseTopo">释放</el-button> -->
    </div>
    <div v-if="showControl" ref="_tp_control" class="_tp_control" />
    <div ref="_tp_graph" class="_tp_graph"/>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      append-to-body
      title="选择您需要链接端口"
      width="50%">
      <div class="_port_choice">
        <div v-if="sourceNode">
          <div v-if="sourceNode.data.facilityCategory.indexOf(102187) != -1 || sourceNode.data.facilityCategory.indexOf(102188) != -1 || sourceNode.data.facilityCategory.indexOf(102364) != -1">
            <div v-if="sourceNode.data.facilityPhysicsDetailVoList.length != 0">
              <el-select v-model="value" :multiple="targetNode.data.sourceMultiple" collapse-tags placeholder="请选择">
                <el-option
                  v-for="item in sourceNode.data.facilityPhysicsDetailVoList"
                  :key="item.id"
                  :label="item.portName"
                  :value="item.id"
                  :disabled="item.disabled || value.length == targetNum"/>
              </el-select>
            </div>
            <div v-else>
              该节点没有链接端口
            </div>
          </div>
          <div v-else>
            该节点没有链接端口
          </div>
        </div>
        <div v-if="targetNode">
          <div v-if="targetNode.data.facilityCategory.indexOf(102187) != -1 || targetNode.data.facilityCategory.indexOf(102188) != -1 || targetNode.data.facilityCategory.indexOf(102364) != -1">
            <div v-if="targetNode.data.facilityPhysicsDetailVoList.length != 0">
              <el-select v-model="value1" :multiple="targetNode.data.targetMultiple" collapse-tags placeholder="请选择">
                <el-option
                  v-for="item in targetNode.data.facilityPhysicsDetailVoList"
                  :key="item.id"
                  :label="item.portName"
                  :value="item.id"
                  :disabled="item.disabled || value1.length == sourceNum"/>
              </el-select>
            </div>
            <div v-else>
              该节点没有链接端口
            </div>
          </div>
          <div v-else>
            该节点没有链接端口
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="portDetermine">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer
      :visible.sync="drawer"
      :direction="direction"
      :modal="false"
      size="23%"
      title="参数配置">
      <div class="_topo_info_">
        <editIntegration
          v-if="drawer"
          :form-data="formData"
          @preservation="_preservation"
        />
      </div>
    </el-drawer>
    <create-dialog
      v-if="parameterConfiguration"
      :dialog-visible="parameterConfiguration"
      title="选择参数配置"
      @obtainData="_obtainData"
      @deleteAddedNode="_deleteAddedNode"
    />
    <el-dialog
      :visible.sync="dialogVisibleSwitch"
      :before-close="_deleteAddedNode"
      append-to-body
      title="选择参数配置"
      width="30%">
      <div>
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="IP版本" prop="ipVersion">
            <el-select v-model="ruleForm.ipVersion" placeholder="请选择">
              <el-option label="4" value="4"/>
              <el-option label="6" value="6"/>
            </el-select>
          </el-form-item>
          <el-form-item label="地址池" prop="addressPool">
            <el-select v-model="ruleForm.addressPool" placeholder="请选择">
              <el-option-group v-for="group in typeOptions" :key="group.id" :label="group.networkName">
                <el-option
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="掩码" prop="networkMask">
            <el-select v-model="ruleForm.networkMask" placeholder="请选择">
              <el-option-group v-for="i in 32" :key="i" >
                <el-option
                  :key="i"
                  :label="i"
                  :value="i"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="cidr" prop="cidr">
            <el-input v-model="ruleForm.cidr" placeholder="请输入内容"/>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="_deleteAddedNode">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="dialogVisibleRouter"
      :before-close="_deleteAddedNode"
      title="选择参数配置"
      width="30%">
      <div>
        <el-form ref="interfaceForm" :model="interfaceForm" :rules="rules" label-width="100px" class="demo-ruleForm">
          <el-form-item label="接口" prop="subnetIdList">
            <el-select v-model="interfaceForm.subnetIdList" multiple collapse-tags placeholder="请选择">
              <el-option-group v-for="group in subnetList" :key="group.id" :label="group.networkName">
                <el-option
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="_deleteAddedNode">取 消</el-button>
        <el-button type="primary" @click="submitForm('interfaceForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { createPhysicalNodes, createVirtualNodes } from '../components/nodes'
import { Graph, Shape } from '@antv/x6'
import { getNetworks, addRouterV3, addSwitchV3, removeRouterV3, removeSwitchV3, getSubnetListV3 } from '@/api/virtualApi'
import { Group } from '../components/shape'
import createDialog from '@/components/dialogGroup/createDisposeDialog'
import editIntegration from '@/components/sleps/editIntegration'
import buildStencil from '../components/stencil'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Scroller } from '@antv/x6-plugin-scroller'
import { Selection } from '@antv/x6-plugin-selection'
import { Export } from '@antv/x6-plugin-export'

export default {
  components: {
    createDialog,
    editIntegration
  },
  props: {
    options: {
      type: Object
    }
  },
  data() {
    return {
      graph: null,
      cell: null,
      nodes: [],
      ruleForm: {
        ipVersion: '',
        addressPool: '',
        networkMask: '',
        cidr: ''
      },
      interfaceForm: {
        subnetIdList: []
      },
      rules: {
        ipVersion: [
          { required: true, message: '请选择IP版本', trigger: 'change' }
        ],
        addressPool: [
          { required: true, message: '请选择地址池', trigger: 'change' }
        ],
        networkMask: [
          { required: true, message: '请选择掩码', trigger: 'change' }
        ],
        cidr: [{
          required: true, message: '请输入cidr', trigger: 'blur'
        }],
        subnetIdList: [{ required: true, message: '请选择接口', trigger: 'change' }]
      },
      drawer: false,
      dialogVisibleSwitch: false,
      dialogVisibleRouter: false,
      direction: 'rtl',
      parameterConfiguration: false,
      value1: '',
      cellDom: null,
      formData: null,
      value: '',
      dialogVisible: false,
      showSave: false, // 显示保存按钮
      showStart: false, // 显示启动按钮
      showRelease: false, // 显示释放按钮
      showTools: false, // 显示删除等工具
      showContextmenu: false, // 右键菜单
      showControl: true, // 拓扑图标
      graphLoading: false,
      graphLoadingText: '',
      addedNode: null,
      sourceNum: 1,
      typeOptions: [],
      subnetList: [],
      targetNum: 1,
      currentArrow: 1,
      sourceNode: null, // 起点节点
      targetNode: null, // 终点节点
      portEdge: null, // 端口节点链接线
      enabled: false,
      connectEdgeType: { // 连线方式
        connector: 'normal',
        router: {
          name: ''
        }
      },
      edge: null,
      color: ['#B0E0E6', '#F5F5DC']
    }
  },
  watch: {
    'options.data'() {
      this.graph.fromJSON(this.options.data)
      this.nodes = this.options.data
      this.moveView()
    },
    'options.status'() {
      this.checkStatus()
      this.moveView()
    },
    'options.readonly'() {
      this.checkStatus()
      this.moveView()
    },
    'options.editable'() {
      this.checkStatus()
      this.moveView()
    },
    'options.scene'() {
      this.checkStatus()
      this.moveView()
    }
  },
  mounted() {
    this.buildGraph()
    this.initEvent()
    this.showControl && this.initStencil()
    this.buildMenu()
    this.checkStatus()
  },
  beforeDestroy() {
    this.graph.dispose()
    document.getElementById('contextMenu').remove()
  },
  methods: {
    // 保存拓扑
    handleSaveTopo() {
      const cells = this.graph.getCells()
      if (this.checkTopoGraph(cells)) {
        // 获取 拓扑图片
        this.graph.toPNG((dataUri) => {
          this.$emit('saveTopo', cells, dataUri)
        }, {
          padding: {
            top: 20,
            right: 30,
            bottom: 40,
            left: 50
          }
        })
      } else {
        this.$message.error('请检查拓扑图是否正确.')
      }
    },
    // 数据回显
    dataBackShow() { 
      if (this.graph) { 
        if (this.options.data) { 
          this.graph.fromJSON(this.options.data)
          this.nodes = this.options.data
        }
        this.moveView()
      }
    },
    cancel() { 
      this.$emit('cancelwindow',false);
    },
    async getNetwork() {
      await getNetworks()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.typeOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    async getSubnetList() {
      await getSubnetListV3()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.subnetList = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (formName == 'ruleForm') {
            addSwitchV3(this.ruleForm).then((res) => {
              this.addedNode.data = { ...this.addedNode.data, ...this.ruleForm }
              this.addedNode.data.tempId = res.data
            })
              .catch((err) => {
                console.log(err)
              })
              .finally(() => {
                this.$refs['ruleForm'].resetFields()
                this.dialogVisibleSwitch = false
              })
          } else {
            addRouterV3(this.interfaceForm).then((res) => {
              this.addedNode.data = { ...this.addedNode.data, ...this.interfaceForm }
              this.addedNode.data.tempId = res.data
            })
              .catch((err) => {
                if (err.code == 500) {
                  this._deleteAddedNode()
                }
              })
              .finally(() => {
                this.$refs['interfaceForm'].resetFields()
                this.dialogVisibleRouter = false
              })
          }
        } else {
          return false
        }
      })
    },
    _preservation() {
      this.cellDom.data = this.formData
      this.cellDom.updateAttrs({ title: { ...this.cellDom.attrs.title, text: this.formData.topoName }})
      this.drawer = false
    },
    _obtainData(formData) {
      this.addedNode.data = { ...this.addedNode.data, ...formData }
      // this.addedNode.setData({ ...this.addedNode.getData(), ...formData })
      this.parameterConfiguration = false
    },
    _deleteAddedNode() {
      this.dialogVisibleSwitch = false
      this.dialogVisibleRouter = false
      if (this.$refs['ruleForm']) {
        this.$refs['ruleForm'].resetFields()
      }
      if (this.$refs['interfaceForm']) {
        this.$refs['interfaceForm'].resetFields()
      }
      this.graph.removeCell(this.addedNode)
    },
    portDetermine() {
      if (this.sourceNode.data.facilityCategory.indexOf(102187) != -1 || this.sourceNode.data.facilityCategory.indexOf(102188) != -1 || this.sourceNode.data.facilityCategory.indexOf(102364) != -1) {
        if (this.sourceNode.data.facilityPhysicsDetailVoList.length != 0) {
          if (!this.value) {
            this.$message.error('请选择起始点端口连接点')
            return
          } else if (this.targetNum != this.value.length && this.targetNum != 1) {
            this.$message.error('请选择对应数量的端口')
            return
          }
        }
      }
      if (this.targetNode.data.facilityCategory.indexOf(102187) != -1 || this.targetNode.data.facilityCategory.indexOf(102188) != -1 || this.targetNode.data.facilityCategory.indexOf(102364) != -1) {
        if (this.targetNode.data.facilityPhysicsDetailVoList.length != 0) {
          if (!this.value1) {
            this.$message.error('请选择结束点端口连接点')
            return
          } else if (this.sourceNum != this.value1.length && this.sourceNum != 1) {
            this.$message.error('请选择对应数量的端口')
            return
          }
        }
      }
      const attr = {}
      attr.sourceNodePortList = []
      attr.sourceNodeIpList = []
      if (Array.isArray(this.value)) {
        for (let index = 0; index < this.sourceNode.data.facilityPhysicsDetailVoList.length; index++) {
          if (this.value.includes(this.sourceNode.data.facilityPhysicsDetailVoList[index].id)) {
            attr.sourceNodePortList.push(this.sourceNode.data.facilityPhysicsDetailVoList[index].portName)
            attr.sourceNodeIpList.push(this.sourceNode.data.facilityPhysicsDetailVoList[index].portIp)
          }
        }
      } else {
        for (let index = 0; index < this.sourceNode.data.facilityPhysicsDetailVoList.length; index++) {
          if (this.sourceNode.data.facilityPhysicsDetailVoList[index].id == this.value) {
            attr.sourceNodePortList.push(this.sourceNode.data.facilityPhysicsDetailVoList[index].portName)
            attr.sourceNodeIpList.push(this.sourceNode.data.facilityPhysicsDetailVoList[index].portIp)
          }
        }
      }
      attr.targetNodePortList = []
      attr.targetNodeIpList = []
      if (Array.isArray(this.value1)) {
        for (let index = 0; index < this.targetNode.data.facilityPhysicsDetailVoList.length; index++) {
          if (this.value1.includes(this.targetNode.data.facilityPhysicsDetailVoList[index].id)) {
            attr.targetNodePortList.push(this.targetNode.data.facilityPhysicsDetailVoList[index].portName)
            attr.targetNodeIpList.push(this.targetNode.data.facilityPhysicsDetailVoList[index].portIp)
          }
        }
      } else {
        for (let index = 0; index < this.targetNode.data.facilityPhysicsDetailVoList.length; index++) {
          if (this.targetNode.data.facilityPhysicsDetailVoList[index].id == this.value1) {
            attr.targetNodePortList.push(this.targetNode.data.facilityPhysicsDetailVoList[index].portName)
            attr.targetNodeIpList.push(this.targetNode.data.facilityPhysicsDetailVoList[index].portIp)
          }
        }
      }
      attr.sourceNodeType = this.sourceNode.attrs.type.type
      attr.sourceDeviceId = this.sourceNode.id
      attr.targetNodeType = this.targetNode.attrs.type.type
      attr.targetDeviceId = this.targetNode.id
      this.portEdge.data = attr
      this.portEdge.setLabels([
        // label1
        {
          markup: [
            {
              tagName: `${attr.sourceNodePortList[0] ? 'rect' : 'ellipse'}`,
              selector: 'labelBody'
            },
            {
              tagName: 'text',
              selector: 'labelText'
            }
          ],
          attrs: {
            labelText: {
              text: `${attr.sourceNodePortList[0] || ''}`,
              fontSize: 12,
              fill: '#000',
              textAnchor: 'middle',
              textVerticalAnchor: 'middle'
            },
            labelBody: {
              ref: 'labelText',
              refX: -8,
              stroke: '#000',
              refY: -5,
              refWidth: '100%',
              refHeight: '100%',
              refWidth2: 16,
              refHeight2: 10,
              strokeWidth: 2
            }
          },
          position: {
            distance: 0.3,
            args: {
              keepGradient: true,
              ensureLegibility: true
            }
          }
        },
        // label 2
        {
          markup: [
            {
              tagName: `${attr.targetNodePortList[0] ? 'rect' : 'ellipse'}`,
              selector: 'labelBody'
            },
            {
              tagName: 'text',
              selector: 'labelText'
            }
          ],
          attrs: {
            labelText: {
              text: `${attr.targetNodePortList[0] || ''}`,
              fontSize: 12,
              fill: '#000',
              textAnchor: 'middle',
              textVerticalAnchor: 'middle'
            },
            labelBody: {
              ref: 'labelText',
              refX: -8,
              stroke: '#000',
              refY: -5,
              refWidth: '100%',
              refHeight: '100%',
              refWidth2: 16,
              refHeight2: 10,
              strokeWidth: 2
            }
          },
          position: {
            distance: 0.7,
            args: {
              keepGradient: true,
              ensureLegibility: true
            }
          }
        }
      ])
      this.value1 = ''
      this.value = ''
      this.dialogVisible = false
    },
    handleClose() {
      this.portConnectCancel()
      this.dialogVisible = false
    },
    // 节点是否可以选择
    enabledFn() {
      // 控制显示样式
      this.enabled = !this.enabled
      // 判断是和否是可选择状态
      if (this.graph.isSelectionEnabled()) {
        this.graph.disableSelection()
      } else {
        this.graph.enableSelection()
      }
    },
    // 改变边形状
    changeEdgeType(e) {
      if (e === 'normal') {
        this.connectEdgeType = {
          connector: 'normal',
          router: { name: '' }
        }
        this.currentArrow = 1
      } else if (e === 'smooth') {
        this.connectEdgeType = {
          connector: 'smooth',
          router: { name: '' }
        }
        this.currentArrow = 2
      } else {
        this.connectEdgeType = {
          connector: 'rounded',
          router: { name: 'manhattan' }
        }
        this.currentArrow = 3
      }
    },
    checkStatus() {
      if (this.options.readonly) {
        this.graphLoading = false
        this.showSave = false
        this.showStart = false
        this.showRelease = false
        this.showContextmenu = true
        this.showTools = false
        this.showControl = false
        this.handleShowContextMenu()
        return
      }
      if (this.options.scene) {
        this.graphLoading = false
        this.showSave = true
        this.showStart = false
        this.showRelease = false
        this.showTools = true
        this.showContextmenu = true
        this.handleShowContextMenu()
        this.vncLoginNone()
        return
      }
      // 未启动
      if (this.options.status === 0) {
        this.graphLoading = false
        this.showSave = true
        this.showStart = true
        this.showRelease = false
        this.showTools = true
        this.showContextmenu = true
        this.handleShowContextMenu()
        this.vncLoginNone()
      }
      // 未启动
      if (this.options.status === -1) {
        // alert(1)
        this.graphLoading = false
        this.showSave = true
        this.showStart = true
        this.showRelease = false
        this.showTools = true
        this.showContextmenu = true
        this.handleShowContextMenu()
        this.vncLoginNone()
      }
      // 已启动
      if (this.options.status === 1) {
        this.graphLoading = false
        this.showSave = false
        this.showStart = false
        this.showRelease = true
        this.showTools = false
        this.showContextmenu = true
        this.handleShowContextMenu()
        this.vncLoginBlock()
      }
      // 启动中
      if (this.options.status === 2) {
        this.graphLoading = true
        this.graphLoadingText = '拓扑启动中...'
        this.showSave = false
        this.showStart = false
        this.showRelease = false
        this.showTools = false
        this.showContextmenu = true
        this.handleHiddenContextMenu()
      }
      // 释放中
      if (this.options.status === 3) {
        this.graphLoading = true
        this.graphLoadingText = '拓扑资源释放中...'
        this.showSave = false
        this.showStart = false
        this.showRelease = false
        this.showTools = false
        this.showContextmenu = true
        this.handleHiddenContextMenu()
      }
      // 启动失败
      if (this.options.status === 11) {
        this.graphLoading = false
        this.showSave = true
        this.showStart = true
        this.showRelease = false
        this.showTools = true
        this.showContextmenu = true
        this.handleHiddenContextMenu()
      }
      // 创建资源中
      if (this.options.status === 12) {
        this.graphLoading = true
        this.graphLoadingText = '拓扑资源创建中...'
        this.showSave = true
        this.showStart = true
        this.showRelease = false
        this.showTools = true
        this.showContextmenu = true
        this.handleHiddenContextMenu()
      }
      // 不可编辑
      if (!this.options.editable) {
        this.showSave = false
        this.showControl = false
        this.showTools = false
        this.showStart = true
      }
    },
    buildMenu() {
      const spanEl4 = document.createElement('span')
      spanEl4.innerText = '启动'
      spanEl4.style.display = 'block'
      spanEl4.style.padding = '6px 20px'
      spanEl4.style.cursor = 'pointer'
      spanEl4.className = 'start-up'
      spanEl4.id = 'start-up'

      spanEl4.addEventListener('click', this.startUp)
      const spanEl = document.createElement('span')
      spanEl.innerText = 'VNC登录'
      spanEl.style.display = 'block'
      spanEl.style.padding = '6px 20px'
      spanEl.style.cursor = 'pointer'
      spanEl.id = 'vnc-login'
      spanEl.className = 'vnc-login'

      spanEl.addEventListener('click', this.vncLogin)

      const spanEl2 = document.createElement('span')
      spanEl2.innerText = '设备详情'
      spanEl2.style.display = 'block'
      spanEl2.style.padding = '6px 20px'
      spanEl2.style.cursor = 'pointer'
      spanEl2.className = 'equipment-info'
      spanEl2.id = 'equipment-info'

      spanEl2.addEventListener('click', this.equipmentInfo)

      const spanEl3 = document.createElement('span')
      spanEl3.innerText = '删除'
      spanEl3.style.display = 'block'
      spanEl3.style.padding = '6px 20px'
      spanEl3.style.cursor = 'pointer'
      spanEl3.id = 'delete-info'

      spanEl3.addEventListener('click', this.deleteInfo)

      const divEl = document.createElement('div')
      divEl.id = 'contextMenu'
      divEl.style.position = 'absolute'
      divEl.style.zIndex = '9999'
      divEl.style.display = 'none'
      divEl.style.backgroundColor = '#FFFFFF'
      divEl.style.border = '1px solid #EEEEEE'
      divEl.style.flexDirection = 'column'
      divEl.style.alignItems = 'center'
      divEl.style.boxShadow = '4px 4px 5px #888888'

      // divEl.appendChild(spanEl4)
      divEl.appendChild(spanEl)
      divEl.appendChild(spanEl2)
      divEl.appendChild(spanEl3)

      document.body.appendChild(divEl)

      document.body.addEventListener('click', event => {
        divEl.style.display = 'none'
      })
    },
    // 右键菜单
    handleShowContextMenu() {
      this.nodes.forEach(n => {
        const nodeEl = document.querySelector(`[data-cell-id='${n.id}']`)
        if (nodeEl) {
          nodeEl.addEventListener('contextmenu', this.handleContextmenu)
        }
        const vncLoginEl = document.getElementById('vnc-login')
        const equipmentEl = document.getElementById('equipment-info')
        const deleteEl = document.getElementById('delete-info')
        const startEl = document.getElementById('start-up')
        if (vncLoginEl) {
          vncLoginEl.dataset.id = n.id
        }
        if (equipmentEl) {
          equipmentEl.dataset.id = n.id
        }
        if (deleteEl) {
          deleteEl.dataset.id = n.id
        }
        if (startEl) {
          startEl.dataset.id = n.id
        }
      })
    },
    vncLoginNone() {
      const vncLoginElList = document.getElementsByClassName('vnc-login')
      Array.prototype.forEach.call(vncLoginElList, function(item) {
        item.style.display = 'none'
      })
    },
    vncLoginBlock() {
      const vncLoginElList = document.getElementsByClassName('vnc-login')
      Array.prototype.forEach.call(vncLoginElList, function(item) {
        item.style.display = 'block'
      })
    },
    deleteCellDom(cellDom) {
      if (cellDom.children) {
        cellDom.setChildren(null)
        this.graph.removeCell(cellDom)
      } else {
        this.graph.removeCell(cellDom)
      }
    },
    startUp(event) {
      console.log(event.target.dataset.id)
    },
    vncLogin(event) {
      this.$emit('vncLogin', this.cellDom.id)
    },
    equipmentInfo(event) {
      this.drawer = true
    },
    deleteInfo(event) {
      if (this.cellDom.attrs.type) {
        if (this.cellDom.attrs.type.name == '虚拟') {
          if (this.cellDom.data.facilityCategoryId.indexOf('102208') != -1) {
            removeSwitchV3(this.cellDom.data.tempId).then((res) => {
              console.log(res)
            })
              .catch((err) => {
                console.log(err)
              })
              .finally(() => {
              })
          } else if (this.cellDom.data.facilityCategoryId.indexOf('102380') != -1) {
            removeRouterV3(this.cellDom.data.tempId).then((res) => {
              console.log(res)
            })
              .catch((err) => {
                console.log(err)
              })
              .finally(() => {
              })
          }
        }
      }
      if (this.cellDom.children) {
        this.cellDom.setChildren(null)
        this.graph.removeCell(this.cellDom)
      } else {
        this.graph.removeCell(this.cellDom)
      }
    },
    moveView() {
      this.graph.centerContent()
    },
    setZoom(event, number) {
      event.zoom(number)
    },
    // 隐藏右键菜单
    handleHiddenContextMenu() {
      this.nodes.forEach(n => {
        const nodeEl = document.querySelector(`[data-cell-id='${n.id}']`)
        nodeEl.removeEventListener('contextmenu', this.handleContextmenu)
        const vncLoginEl = document.getElementById('vnc-login')
        vncLoginEl.dataset.id = n.id
      })
    },
    handleContextmenu(event) {
      const contextMenuEl = document.getElementById('contextMenu')
      contextMenuEl.style.display = 'flex'
      contextMenuEl.style.top = event.clientY + 'px'
      contextMenuEl.style.left = event.clientX + 'px'
    },
    // 启动
    handleStartTopo() {
      this.$emit('startTopo')
    },
    // 释放
    handleReleaseTopo() {
      this.$emit('releaseTopo')
    },
    // 检查拓扑图连线
    checkTopoGraph(cells) {
      // 找出所有边
      const edgeList = cells.filter(c => c.shape === 'edge')
      // 找出所有和边相连的nodeId
      const nodeIdList = [...edgeList.map(c => c.source.cell), ...edgeList.map(c => c.target.cell)]
      const nodeList = cells.filter(c => c.shape === 'node-image')
      let flag = true
      // 判断和边相连的节点是否包含节点。如果不包含，则说明节点没有连线
      for (let i = 0; i < nodeList.length; i++) {
        if (!nodeIdList.includes(nodeList[i].id)) {
          flag = false
          break
        }
      }
      return flag
    },
    // 保存拓扑
    handleSaveTopo() {
      const cells = this.graph.getCells()
      // if (this.checkTopoGraph(cells)) {
      // 获取 拓扑图片
      this.graph.toPNG((dataUri) => {
        // this.$emit('saveTopo', cells, dataUri)
        this.$emit('realTopo',cells, dataUri);
      }, {
        padding: {
          top: 20,
          right: 30,
          bottom: 40,
          left: 50
        }
      })
      // } else {
      //   this.$message.error('请检查拓扑图是否正确.')
      // }
    },
    // 构建画布
    buildGraph() {
      var _that = this
      this.graph = new Graph({
        container: this.$refs._tp_graph,
        grid: {
          visible: true,
          type: 'doubleMesh',
          args: [
            {
              color: '#eee', // 主网格线颜色
              thickness: 1 // 主网格线宽度
            },
            {
              color: '#ddd', // 次网格线颜色
              thickness: 1, // 次网格线宽度
              factor: 4 // 主次网格线间隔
            }
          ]
        },
        translating: {
          restrict(view) {
            if (view) {
              const cell = view.cell
              if (cell.isNode()) {
                const parent = cell.getParent()
                if (parent) {
                  return parent.getBBox()
                }
              }
            }
            return null
          }
        },
        // panning: true, // 画布是否可以拖动
        mousewheel: { // 鼠标滚轮缩放
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 3
        },
        connecting: {
          allowNode: false,
          allowLoop: false,
          connectionPoint: 'anchor',
          createEdge() {
            return new Shape.Edge({
              attrs: {
                line: {
                  targetMarker: null
                }
              },
              connector: _that.connectEdgeType.connector,
              router: { // 路由
                name: _that.connectEdgeType.router.name
              },
              zIndex: 0
            })
          }
        }
      })
      this.graph.use(
        new Keyboard({
          enabled: true,
          global: true
        }),
      )
      this.graph.use(
        new Selection({
          enabled: false,
          rubberband: true
        })
      )
      this.graph.use(
        new Scroller({
          enabled: true
        }),
      )
      this.graph.use(new Export())
      // #endregion
    },
    // 根据 元素 的x,y轴跟 宽高 来生成 父节点
    createGroup(x, y, width, height, fill, zIndex) {
      if (width < 0) {
        width = width * -1
      }
      if (height < 0) {
        height = height * -1
      }
      height = height + 50
      width = width + 40
      const group = new Group({
        x,
        y,
        zIndex: zIndex,
        width,
        height,
        attrs: {
          body: {
            fill: fill,
            stroke: '#000' },
          label: { text: '' }
        },
        data: {
          facilityCategory: ''
        }
      })
      this.graph.addNode(group)
      return group
    },
    // 分组
    groupChild(group, element) {
      group.addChild(element)
    },
    portConnectCancel() {
      this.graph.removeEdge(this.portEdge)
      this.value1 = []
      this.value = []
      this.dialogVisible = false
    },
    // 初始化事件
    initEvent() {
      // ctrl+c事件
      this.graph.bindKey('ctrl+c', () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.copy(cells)
        }
        return false
      })
      // ctrl+v事件
      this.graph.bindKey('ctrl+v', () => {
        if (!this.graph.isClipboardEmpty()) {
          const cells = this.graph.paste({ offset: 32 })
          this.graph.cleanSelection()
          this.graph.select(cells)
        }
        return false
      })
      // 控制连接桩显示/隐藏
      const showPorts = (ports, show) => {
        for (let i = 0, len = ports.length; i < len; i = i + 1) {
          ports[i].style.visibility = show ? 'visible' : 'hidden'
        }
      }
      this.graph.on('node:mouseenter', () => {
        const ports = this.$refs._tp_graph.querySelectorAll('.x6-port-body')
        showPorts(ports, true)
      })
      this.graph.on('node:mouseleave', () => {
        const ports = this.$refs._tp_graph.querySelectorAll('.x6-port-body')
        showPorts(ports, false)
      })
      this.graph.on('cell:mouseenter', ({ cell }) => {
        this.nodes = this.graph.getCells()
        if (this.options.status === 1) {
          this.vncLoginBlock()
        } else {
          this.vncLoginNone()
        }
        this.handleShowContextMenu()
      })
      this.graph.on('node:added', ({ node }) => {
        if (node.attrs.type) {
          if (node.attrs.type.name == '虚拟') {
            if (node.data.facilityCategoryId.indexOf('102207') != -1) {
              this.parameterConfiguration = true
            } else if (node.data.facilityCategoryId.indexOf('102208') != -1) {
              this.dialogVisibleSwitch = true
              this.getNetwork()
            } else if (node.data.facilityCategoryId.indexOf('102380') != -1) {
              this.dialogVisibleRouter = true
              this.getSubnetList()
            }
            this.addedNode = node
          }
        }
      })
      this.graph.on('cell:contextmenu', ({ cell }) => {
        this.cellDom = cell
        if (cell.attrs.type) {
          if (cell.attrs.type.name == '虚拟') {
            this.formData = JSON.stringify(this.cellDom.data)
            this.formData = JSON.parse(this.formData)
            this.formData.topoName = this.cellDom.attrs.title.text
          }
        }
        if (this.cellDom.children || (Object.getPrototypeOf(cell).toString().indexOf('Edge') != -1)) {
          const equipmentInfoElList = document.getElementsByClassName('equipment-info')
          Array.prototype.forEach.call(equipmentInfoElList, function (item) {
            item.style.display = 'none'
          })
          const vncLoginElList = document.getElementsByClassName('vnc-login')
          Array.prototype.forEach.call(vncLoginElList, function (item) {
            item.style.display = 'none'
          })
          const startUpElList = document.getElementsByClassName('start-up')
          Array.prototype.forEach.call(startUpElList, function (item) {
            item.style.display = 'none'
          })
        } else {
          const equipmentInfoElList = document.getElementsByClassName('equipment-info')
          Array.prototype.forEach.call(equipmentInfoElList, function (item) {
            item.style.display = 'block'
          })
          if (this.options.status == 1) {
            const vncLoginElList = document.getElementsByClassName('vnc-login')
            Array.prototype.forEach.call(vncLoginElList, function (item) {
              item.style.display = 'block'
            })
          }
          const startUpElList = document.getElementsByClassName('start-up')
          Array.prototype.forEach.call(startUpElList, function (item) {
            item.style.display = 'block'
          })
        }
      })
      this.graph.on('cell:mouseenter', ({ cell }) => {
        this.showTools && this.handlShowTools(cell)
      })
      this.graph.on('cell:mouseleave', ({ cell }) => {
        this.showTools && cell.removeTools()
      })
      this.graph.on('cell:click', ({ cell }) => {
        console.log(cell)
      })
      this.graph.on('edge:mouseup', ({ edge }) => {
        if (edge.labels.length != 0) {
          return
        }
        this.sourceNode = edge.getSourceNode()
        this.targetNode = edge.getTargetNode()
        // 判断如果他是交换机 multiple 将不是交换机的那端设置为 true 反之则是false
        // 获取交换机 的接口数量 链接数量足够后将其禁用掉 不是交换机 择只可以连接一个 写个判断 赋值成1
        // 下面是否被 占用的判断需要判断交换机的那一根线上 是否是多个
        const arr = []
        this.graph.getEdges().forEach(item => {
          if (item.data) {
            if (item.data.sourceNodePortList.length > 0) {
              item.data.sourceNodePortList.forEach(element => {
                arr.push(element + item.data.sourceDeviceId)
              })
            } else if (item.data.targetNodePortList.length > 0) {
              item.data.targetNodePortList.forEach(element => {
                arr.push(element + item.data.targetDeviceId)
              })
            }
          }
        })
        if (this.sourceNode && this.targetNode) {
          this.targetNode.data.targetMultiple = false
          this.targetNode.data.sourceMultiple = false
          if (this.sourceNode.data.num) {
            this.targetNode.data.targetMultiple = true
            this.sourceNum = this.sourceNode.data.num
          } else {
            this.targetNode.data.targetMultiple = false
            this.sourceNum = 1
          }
          if (this.targetNode.data.num) {
            this.targetNode.data.sourceMultiple = true
            this.targetNum = this.targetNode.data.num
          } else {
            this.targetNode.data.sourceMultiple = false
            this.targetNum = 1
          }
          if (this.sourceNode.data.facilityCategory.indexOf(102187) != -1 || this.sourceNode.data.facilityCategory.indexOf(102188) != -1 || this.sourceNode.data.facilityCategory.indexOf(102364) != -1) {
            if (this.sourceNode.data.facilityPhysicsDetailVoList.length != 0) {
              this.portEdge = edge
              this.sourceNode.data.facilityPhysicsDetailVoList.forEach(element => {
                if (arr.includes(element.portName + this.sourceNode.id)) {
                  element.disabled = true
                } else {
                  element.disabled = false
                }
              })
              this.dialogVisible = true
            }
          }
          if (this.targetNode.data.facilityCategory.indexOf(102187) != -1 || this.targetNode.data.facilityCategory.indexOf(102188) != -1 || this.targetNode.data.facilityCategory.indexOf(102364) != -1) {
            if (this.targetNode.data.facilityPhysicsDetailVoList.length != 0) {
              this.portEdge = edge
              this.targetNode.data.facilityPhysicsDetailVoList.forEach(element => {
                if (arr.includes(element.portName + this.targetNode.id)) {
                  element.disabled = true
                } else {
                  element.disabled = false
                }
              })
              this.dialogVisible = true
            }
          }
        }
      })
      this.graph.on('node:change:size', ({ cell }) => {
        if (!cell.children) {
          cell.attrs.image.width = cell.size().width
          cell.attrs.image.height = cell.size().height
          cell.attrs.title.fontSize = cell.size().height / 5
        }
      })
      this.graph.on('node:added', ({ node }) => {
        let num = 0
        if (node.attrs.type) {
          if (node.attrs.type.name === '物理') {
            this.graph.getNodes().forEach(item => {
              if (item.attrs.title && node.attrs.title) {
                if (item.attrs.title.text === node.attrs.title.text && item.attrs.title.text !== '红队' && item.attrs.title.text !== '蓝队') {
                  num++
                  if (num > 1) {
                    this.graph.removeNode(node)
                  }
                }
              }
            })
          }
        }
      })
      this.graph.on('edge:mouseenter', ({ cell }) => {
        // cell.addTools([
        //   'source-arrowhead',
        //   {
        //     name: 'target-arrowhead',
        //     args: {
        //       attrs: {
        //         fill: 'red'
        //       }
        //     }
        //   }
        // ])
      })

      this.graph.on('edge:mouseleave', ({ cell }) => {
        cell.removeTools()
      })
      // 选择两个以上 节点 并且 该节点没有父节点 根据节点的属性来生成 父节点的大小 与所在位置
      this.graph.on('selection:changed', ({ added, removed, selected, options }) => {
        const arr = []
        let num = 0
        let zIndex = -10
        selected.forEach(element => {
          if (!element.parent) {
            arr.push(element)
          }
          if (element._children || element.children) {
            num++
            zIndex--
          }
        })
        if (arr.length > 1) {
          const xObj = []
          const yObj = []
          arr.forEach(item => {
            xObj.push(item.position().x, (item.position().x + item.size().width))
            yObj.push(item.position().y, (item.position().y + item.size().height))
          })
          const x = xObj.sort((a, b) => { return a - b })
          const y = yObj.sort((a, b) => { return a - b })
          let height = ''
          let width = ''
          if (x[x.length - 1] > 0 && x[0] < 0) {
            width = x[x.length - 1] + (x[0] * -1)
          } else {
            width = x[x.length - 1] - x[0]
          }
          if (y[y.length - 1] > 0 && y[0] < 0) {
            height = y[y.length - 1] + (y[0] * -1)
          } else {
            height = y[y.length - 1] - y[0]
          }
          const a = this.createGroup(
            x[0] - 20,
            y[0] - 25,
            width,
            height, this.color[ (num % 2) ], zIndex)
          arr.forEach(item => {
            a.addChild(item)
          })
        }
      })
      // 控制该节点的展开与收起
      // this.graph.on('node:collapse', ({ node }) => {
      //   node.toggleCollapse()
      //   const collapsed = node.isCollapsed()
      //   const collapse = (parent) => {
      //     const cells = parent.getChildren()
      //     if (cells) {
      //       cells.forEach((cell) => {
      //         if (collapsed) {
      //           cell.hide()
      //         } else {
      //           cell.show()
      //         }
      //         if (cell instanceof Group) {
      //           if (!cell.isCollapsed()) {
      //             collapse(cell)
      //           }
      //         }
      //       })
      //     }
      //   }
      //   collapse(node)
      // })
      // 双击 节点 编辑 节点名称 或者标识
      this.graph.on('node:dblclick', ({ node, e }) => {
        // const name = node.isNode() ? 'node-editor' : 'edge-editor'
        // node.addTools({
        //   name,
        //   args: {
        //     event: e,
        //     attrs: {
        //       backgroundColor: '#EFF4FF'
        //     }
        //   }
        // })
      })
    },
    async initStencil() {
      if (!this.$refs._tp_control) {
        return
      }
      const pNodes = await createPhysicalNodes(this.graph)
      const vNodes = await createVirtualNodes(this.graph)
      // 初始化 stencil
      const stencil = buildStencil(this.graph, pNodes.length, vNodes.length)
      this.$refs._tp_control.appendChild(stencil.container)
      stencil.load(pNodes, 'group1')
      stencil.load(vNodes, 'group2')
      this.$emit("stencilinited")
    },
    handlShowTools(cell) {
      if (cell.isNode()) {
        cell.addTools([
          {
            name: 'boundary',
            args: {
              attrs: {
                fill: '#7c68fc',
                stroke: '#333',
                'stroke-width': 1,
                'fill-opacity': 0.2
              }
            }
          }
          // {
          //   name: 'button-remove',
          //   args: {
          //     x: 0,
          //     y: 0,
          //     offset: { x: 8, y: 8 },
          //     onClick() {
          //       if (cell.children) {
          //         cell.setChildren(null)
          //         this.graph.removeCell(cell)
          //       } else {
          //         console.log(cell)
          //         this.graph.removeCell(cell)
          //       }
          //     }
          //   }
          // }
        ])
      } else {
        // '', 调整顶点
        // cell.addTools(['vertices'])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
._tp_container {
  display: flex;
  position: relative;
  height: 100vh;
  width: 100%;
  ._tp_tools {
    position: absolute;
    display: flex;
    align-items: center;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 1;
    ._tp_operation_column{
      padding: 10px 25px;
      display: flex;
      background: #fff;
      margin: 0 40px 0 80px;
      border-radius: 8px;
      box-shadow: 0px 0px 6px rgba(40, 143, 239,0.5);
      div{
        margin: 0 15px;
      }
      img{
        height: 25px;
        width: 25px;
        cursor: pointer;
        margin: 0 5px;
      }
    }
  }
  ._tp_control {
    width: 260px;
    position: relative;
    border: 1px solid #f0f0f0;
  }
  ._tp_graph{
    flex: 1;
    min-width: 1800px !important;
    min-height: 1200px !important;
  }
}
::v-deep{
  .x6-graph-scroller{
    // width: 83% !important;
    flex: 1;
    height: 100% !important;
    .x6-graph-scroller-content{
      min-width: 1800px !important;
      min-height: 1200px !important;
    }
  }
}
._port_choice{
  display: flex;
  align-items: center;
  justify-content: space-around;
  ::v-deep{
    .el-select{
      width: 250px;
    }
  }
}
._topo_info_{
  ::v-deep{
    ._radio_item_{
      display: flex;
      flex-direction: column;
      .el-form-item__label{
        width: 200px !important;
        text-align: left;
        margin-left: 35px;
      }
    }
  }
}
</style>
