import request from '@/utils/request'

export function getSystemNews(params) {
    return request({
        url: process.env.ADMIN_API + '/sysMessage/getList',
        method: 'post',
        data: params
    })
}

export function addSystemNews(params) {
    return request({
        url: process.env.ADMIN_API + '/sysMessage/add',
        method: 'post',
        data: params
    })
}


export function editSystemNews(params) {
    return request({
        url: process.env.ADMIN_API + '/sysMessage/edit',
        method: 'post',
        data: params
    })
}

export function delSystemNews(params) {
    return request({
        url: process.env.ADMIN_API + '/sysMessage/deleteBatch',
        method: 'post',
        data: params
    })
}