import request from '@/utils/request'

/**
 * 知识点列表
 */
export function searchAPI(data) {
  return request({
    url: '/training/pjtKnowledgePoint/backSearchKnowledge',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 知识点新增
 */
export function addAPI(data) {
  return request({
    url: '/training/pjtKnowledgePoint/insertKnowledge',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 知识点编辑
 */
export function editAPI(data) {
  return request({
    url: '/training/pjtKnowledgePoint/updateKnowledge',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 知识点删除
 */
export function dropAPI(data) {
  return request({
    url: '/training/pjtKnowledgePoint/deleteKnowledge',
    method: 'delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 知识点查询课程
 */
export function queryCourseByKnowledge(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/backSearchKnowledgeCurriculums',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 知识点查询任务
 */
export function queryTaskByKnowledge(data) {
  return request({
    url: '/training/pjtTeachingTask/backSearchKnowledgeTask',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 知识点查询题目
 */
export function queryQuestionByKnowledge(data) {
  return request({
    url: '/training/pjtExamQuestion/backSearchKnowledgequestion',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
