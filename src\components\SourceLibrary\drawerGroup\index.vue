<template>
  <el-drawer
    v-if="drawer"
    ref="drawer"
    :visible.sync="drawer"
    :direction="direction"
    :before-close="handleClose"
    :with-header="false"
    destroy-on-close
    size="50%"
    class="drawer_index"
  >
    <el-button
      class="close_button"
      type="text"
      icon="el-icon-close"
      size="medium"
      @click="handleClose"
    />
    <!-- 内容区域 -->
    <slot/>
  </el-drawer>
</template>
<script>
export default {
  name: 'DrawerGroup',
  props: {
    title: {
      type: String,
      default: ''
    },
    drawer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      direction: 'rtl'
    }
  },
  created() {},
  methods: {
    handleClose(done) {
      // done()
      // this.$refs.drawer.closeDrawer()
      this.$emit('closeDrawer')
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer_index {
  &::v-deep {
    .el-drawer__body {
      position: relative;
      overflow: hidden;
      .close_button {
        position: absolute;
        right: 20px;
        top: 20px;
        z-index: 1;
      }
    }
  }
}
</style>
