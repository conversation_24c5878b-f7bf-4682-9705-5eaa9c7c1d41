<template>
  <div v-loading="uploadFlag" element-loading-text="文件上传中...">
    <el-upload
      :show-file-list="false"
      :http-request="uploadImgFun"
      :on-error="handleUploadError"
      :headers="{Authorization:$GetToken()}"
      :accept="transformImgArray"
      action="fafafafa"
      class="img-uploader"
      style="display: none"/>
    <el-upload
      :http-request="uploadVideoFun"
      :show-file-list="false"
      :on-error="handleUploadError"
      :headers="{Authorization:$GetToken()}"
      :accept="transformVideoArray"
      action="fafafafa"
      class="video-uploader"
      style="display: none"/>
    <div ref="editor" :style="styles" class="editor"/>
  </div>
</template>

<script>
import { RichTextUpload } from '@/api/sourceLibrary/admin/file.js'
// import Quill from 'quill'
import { Quill } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { getToken } from '@/utils/auth'

// toolbar标题
const titleConfig = [
  { Choice: '.ql-insertMetric', title: '跳转配置' },
  { Choice: '.ql-bold', title: '加粗' },
  { Choice: '.ql-italic', title: '斜体' },
  { Choice: '.ql-underline', title: '下划线' },
  { Choice: '.ql-header', title: '段落格式' },
  { Choice: '.ql-strike', title: '删除线' },
  { Choice: '.ql-blockquote', title: '块引用' },
  { Choice: '.ql-code', title: '插入代码' },
  { Choice: '.ql-code-block', title: '插入代码段' },
  { Choice: '.ql-font', title: '字体' },
  { Choice: '.ql-size', title: '字体大小' },
  { Choice: '.ql-list[value="ordered"]', title: '编号列表' },
  { Choice: '.ql-list[value="bullet"]', title: '项目列表' },
  { Choice: '.ql-direction', title: '文本方向' },
  { Choice: '.ql-header[value="1"]', title: 'h1' },
  { Choice: '.ql-header[value="2"]', title: 'h2' },
  { Choice: '.ql-align', title: '对齐方式' },
  { Choice: '.ql-color', title: '字体颜色' },
  { Choice: '.ql-background', title: '背景颜色' },
  { Choice: '.ql-image', title: '图像' },
  { Choice: '.ql-video', title: '视频' },
  { Choice: '.ql-link', title: '添加链接' },
  { Choice: '.ql-formula', title: '插入公式' },
  { Choice: '.ql-clean', title: '清除字体格式' },
  { Choice: '.ql-script[value="sub"]', title: '下标' },
  { Choice: '.ql-script[value="super"]', title: '上标' },
  { Choice: '.ql-indent[value="-1"]', title: '向左缩进' },
  { Choice: '.ql-indent[value="+1"]', title: '向右缩进' },
  { Choice: '.ql-header .ql-picker-label', title: '标题大小' },
  { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: '标题一' },
  { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: '标题二' },
  { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: '标题三' },
  { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: '标题四' },
  { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: '标题五' },
  { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: '标题六' },
  { Choice: '.ql-header .ql-picker-item:last-child', title: '标准' },
  { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: '小号' },
  { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: '大号' },
  { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: '超大号' },
  { Choice: '.ql-size .ql-picker-item:nth-child(2)', title: '标准' },
  { Choice: '.ql-align .ql-picker-item:first-child', title: '居左对齐' },
  { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: '居中对齐' },
  { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: '居右对齐' },
  { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: '两端对齐' }
]

export default {
  name: 'Editor',
  props: {
    /* 编辑器的内容 */
    value: {
      type: String,
      default: ''
    },
    /* 高度 */
    height: {
      type: Number,
      default: null
    },
    /* 最小高度 */
    minHeight: {
      type: Number,
      default: null
    },
    /* 只读 */
    readOnly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    /* 上传图片限制的类型 */
    imageUploadTypes: {
      type: Array,
      default: () => {
        return ['.png', '.jpeg', '.jpg']
      }
    },
    /* 上传视频限制的类型 */
    videoUploadTypes: {
      type: Array,
      default: () => {
        return ['.mp4']
      }
    },
    /* 是否展示上传视频按钮 */
    isVideoFlag: {
      type: Boolean,
      default: true
    },
    /* 是否展示上传图片按钮 */
    isImageFlag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      Quill: null,
      currentValue: '',
      uploadFlag: false, // 上传文件时加载状态
      options: {
        theme: 'snow',
        bounds: document.body,
        debug: 'warn',
        modules: {
          // 工具栏配置
          toolbar: {
            container: [ // 工具栏
              ['bold', 'italic', 'underline', 'strike'], // 加粗，斜体，下划线，删除线
              ['blockquote', 'code-block'], // 引用，代码块
              [{ 'header': 1 }, { 'header': 2 }], // 几级标题
              [{ 'list': 'ordered' }, { 'list': 'bullet' }], // 有序列表，无序列表
              [{ 'script': 'sub' }, { 'script': 'super' }], // 下角标，上角标
              [{ 'indent': '-1' }, { 'indent': '+1' }], // 缩进
              [{ 'size': ['small', false, 'large', 'huge'] }], // 字体大小
              [{ 'header': [1, 2, 3, 4, 5, 6, false] }], // 标题
              [{ 'color': [] }, { 'background': [] }], // 字体颜色、字体背景颜色
              [{ 'align': [] }], // // 对齐方式
              ['clean'], // 清除样式,
              ['link'] // 链接
            ],
            handlers: {
              image: function(value) { // 自定义图片上传事件
                if (value) {
                  document.querySelector('.img-uploader input').click()
                } else {
                  this.quill.format('image', false)
                }
              },
              video: function(value) { // 自定义视频上传事件
                if (value) {
                  document.querySelector('.video-uploader input').click()
                } else {
                  this.quill.format('video', false)
                }
              }
            }
          }
        },
        placeholder: this.placeholder,
        readOnly: this.readOnly
      }
    }
  },
  computed: {
    styles() {
      const style = {}
      if (this.minHeight) {
        style.minHeight = `${this.minHeight}px`
      }
      if (this.height) {
        style.height = `${this.height}px`
      }
      return style
    },
    transformImgArray() {
      return this.imageUploadTypes.join(',')
    },
    transformVideoArray() {
      return this.videoUploadTypes.join(',')
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.currentValue) {
          this.currentValue = val === null ? '' : val
          if (this.Quill) {
            this.Quill.pasteHTML(this.currentValue)
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.init()
    this.initTitle()
  },
  beforeDestroy() {
    this.Quill = null
  },
  methods: {
    init() {
      const editor = this.$refs.editor
      if (this.isImageFlag) this.options.modules.toolbar.container.push(['image'])
      if (this.isVideoFlag) this.options.modules.toolbar.container.push(['video'])
      this.Quill = new Quill(editor, this.options)
      this.Quill.pasteHTML(this.currentValue)
      this.Quill.on('text-change', (delta, oldDelta, source) => {
        const html = this.$refs.editor.children[0].innerHTML
        const text = this.Quill.getText()
        const quill = this.Quill
        this.currentValue = html
        this.$emit('input', html)
        this.$emit('on-change', { html, text, quill })
      })
      this.Quill.on('text-change', (delta, oldDelta, source) => {
        this.$emit('on-text-change', delta, oldDelta, source)
      })
      this.Quill.on('selection-change', (range, oldRange, source) => {
        this.$emit('on-selection-change', range, oldRange, source)
      })
      this.Quill.on('editor-change', (eventName, ...args) => {
        this.$emit('on-editor-change', eventName, ...args)
      })
    },
    initTitle() {
      for (const item of titleConfig) {
        const tip = document.querySelector(item.Choice)
        if (!tip) continue
        tip.setAttribute('title', item.title)
      }
    },
    uploadImgFun(params) {
      const [...newArray] = this.imageUploadTypes
      const Arr = newArray.map((item) => {
        return String(item).substring(1)
      })
      const suffix = String(params.file.name).substring(String(params.file.name).lastIndexOf('.'))
      if (!this.imageUploadTypes.includes(suffix)) {
        this.$message.warning(`图片格式错误,支持${Arr.join(',')}格式`)
        return
      }
      this.uploadFlag = true
      // 获取富文本组件实例
      const quill = this.Quill
      const formData = new FormData()
      formData.append('file', params.file)
      RichTextUpload(formData).then((res) => {
        if (res.code == 0) {
          // 获取光标所在位置
          const length = quill.getSelection().index
          // 插入图片  res.data.url为服务器返回的图片地址
          quill.insertEmbed(length, 'image', res.data.url)
          // 调整光标到最后
          quill.setSelection(length + 1)
          this.uploadFlag = false
        } else {
          this.$message.error('图片插入失败')
          this.uploadFlag = false
        }
      }).finally(() => {
        this.uploadFlag = false
      })
    },
    uploadVideoFun(params) {
      const [...newArray] = this.videoUploadTypes
      const Arr = newArray.map((item) => {
        return String(item).substring(1)
      })
      const suffix = String(params.file.name).substring(String(params.file.name).lastIndexOf('.'))
      if (!this.videoUploadTypes.includes(suffix)) {
        this.$message.warning(`视频格式错误,支持${Arr.join(',')}格式`)
        return
      }
      this.uploadFlag = true
      // 获取富文本组件实例
      const quill = this.Quill
      const formData = new FormData()
      formData.append('file', params.file)
      RichTextUpload(formData).then((res) => {
        if (res.code == 0) {
          // 获取光标所在位置
          const length = quill.getSelection().index
          // 插入图片  res.data.url为服务器返回的图片地址
          quill.insertEmbed(length, 'video', res.data.url)
          // 调整光标到最后
          quill.setSelection(length + 1)
          this.uploadFlag = false
        } else {
          this.$message.error('视频插入失败')
          this.uploadFlag = false
        }
      }).finally(() => {
        this.uploadFlag = false
      })
    },
    handleUploadError() {
      this.$message.error('文件上传失败')
    }
  }
}
</script>

<style>
.editor, .ql-toolbar {
  white-space: pre-wrap !important;
  line-height: normal !important;
}
.quill-img {
  display: none;
}
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: "标准字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
  content: "衬线字体";
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
  content: "等宽字体";
}
</style>
