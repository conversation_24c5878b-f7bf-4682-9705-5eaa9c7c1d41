import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}

export function reUseTrainClassList() {
  return request({
    url: process.env.ADMIN_API + "/trainClass/reUseTrainClassList",
    method: "get"
  });
}

export function reUseTrainClass(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/reUseTrainClass",
    method: "post",
    data: params
  });
}

export function notOverClass(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/notOverClass",
    method: "get",
    params
  });
}

export function getAllClass() {
  return request({
    url: process.env.ADMIN_API + "/trainClass/getAllClass",
    method: "get",
  });
}

//培训班上下架
export function upOrDown(params = {}) {
  return request({
    url: process.env.ADMIN_API + `/trainClass/updateStatus?status=${params.status}`,
    method: "post",
    data:params.uids
  });
}

export function downloadAll(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/downloadFile",
    method: "post",
    data: params,
    responseType: 'blob'
  });
}

export function updateTableUid(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/updateTableUid",
    method: "post",
    data: params,
  });
}

export function getTrainAreaList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClass/getTrainAreaList",
    method: "get",
    params
  });
}

// 多文件图片上传
export function webPicture (data = {}) {
  return request({
    url:  process.env.PICTURE_API + '/file/webPicture',
    method: "post",
    data: data,
  });
}

export function getLearnStudentList(data) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/getLearnStudentList",
    method: "post",
    data
  });
}

export function upDown(data) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/upDownLearnStudent",
    method: "post",
    data
  });
}

export function getSingUpRelationList(data) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/getSingUpRelationList",
    method: "post",
    data
  });
}

export function getExamList(data) {
  return request({
    url: process.env.ADMIN_API + "/trainExamSignUp/getList",
    method: "post",
    data
  });
}

export function updateExamTableUid(params) {
  return request({
    url: process.env.ADMIN_API + "/trainExamSignUp/updateTableUid",
    method: "post",
    data: params,
  });
}