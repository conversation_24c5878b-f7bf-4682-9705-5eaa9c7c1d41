<template>
  <div class="dialog-confi">
    <el-dialog
      v-if="dialogFlag"
      :title="title"
      :visible.sync="dialogFlag"
      :before-close="_beforeClose"
      center
      width="40%"
    >
      <!-- 内容区域 -->
      <div v-loading="loading" class="dialog_content">
        <p
          class="_p"
          style="color:rgba(0,0,0,0.5)"
        >此操作将无法撤销，确定删除下列共{{ virtualList.length }}个虚拟模板</p>
        <!-- 此操作将无法撤销，确定删除下列共{{ virtualList.length }}个云主机 , 同时删除云主机快照链（即其所有快照） -->
        <!--        <p class="_p">
          <svg-icon icon-class="warningmsg" class-name="card-panel-icon" />(默认全部级联删除根磁盘，点击磁盘图标可相应取消)
          <span class="_span">
            <el-checkbox-group v-model="checked">
              <el-checkbox
                v-for="item in checkedList"
                :label="item.name"
                :key="item.id"
              >{{ item.name }}</el-checkbox>
            </el-checkbox-group>
            &lt;!&ndash; <el-checkbox v-model="checked">全部级联删除根磁盘</el-checkbox> &ndash;&gt;
          </span>
        </p>-->
        <div v-if="virtualList.length" class="disVirtualList">
          <div v-for="(item, index) in virtualList" :key="index" class="disVirtual_content">
            {{ item.facilityName }}
            <span class="status_class">
              <svg-icon
                v-if="!item.chooseDriveFlag"
                icon-class="cloudDrive"
                class-name="card-panel-icon"
                @click="chooseDrive(item, index)"
              />
              <svg-icon
                v-if="item.chooseDriveFlag"
                icon-class="cloudDriveClick"
                class-name="card-panel-icon"
                @click="chooseDrive(item, index)"
              />
              <svg-icon icon-class="true" class-name="card-panel-icon" style="margin-left: 10px" />
            </span>
          </div>
        </div>
        <div class="dialog-footer _button">
          <el-button @click="cancal">取 消</el-button>
          <el-button :disabled="disFlag" type="primary" style="margin-left: 120px" @click="sure">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { delVirtual } from '@/api/sourceLibrary/virtualApi'
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      disFlag: false,
      dialogFlag: true,
      checked: '',
      virtualList: [],
      checkedList: [{ id: '1', name: '全部级联删除根磁盘' }],
      loading: false
    }
  },
  created() {
    this.virtualList = this.list
    for (const item of this.virtualList) {
      item.chooseDriveFlag = false
    }
  },
  methods: {
    chooseDrive(item, index) {
      this.virtualList[index].chooseDriveFlag = !item.chooseDriveFlag
      const arr = this.virtualList.filter((ele) => !ele.chooseDriveFlag)
      if (!arr || !arr.length) {
        this.checked = true
      } else {
        this.checked = false
      }
      this.$forceUpdate()
    },
    _beforeClose() {
      this.$emit('cancelDialog', true)
    },
    cancal() {
      this.dialogFlag = false
      this.$emit('cancelDialog', true)
    },
    sure() {
      const list = []
      const arr = this.virtualList.filter((item) => item.occupy > 0)
      if (arr.length) {
        const str = arr.map((item) => item.facilityName).join(',')
        this.$message.error(`设备:${str}占用中，不可删除`)
      } else {
        this.disFlag = true
        this.loading = true
        this.virtualList.forEach((item) => {
          const flag = delVirtual(item.id)
            .then((res) => {
              console.log('删除----------------->', res)
              res.name = '删除' + item.facilityName + '， '
              this.$parent.msgList = res
              this.$emit('msgShow')
              return res
            })
            .catch((err) => {
              console.log(err)
            })
          list.push(flag)
        })
        Promise.all(list).then((res) => {
          this.loading = false
          this.$emit('updateList')
          this.cancal()
        })
      }

      // for (let item of this.virtualList) {
      //   delVirtual(item.id)
      //     .then((res) => {
      //       if (res.code == 200) {
      //         this.disFlag = false
      //         this.$message.success(res.msg)
      //         setTimeout(() => {
      //           this.$emit('updateList')
      //           this.loading = false
      //           this.cancal()
      //         }, 3000)
      //       } else {
      //         this.$message.error(res.msg)
      //       }
      //     })
      //     .catch((err) => {
      //       console.log(err)
      //     })
      // }
      // setTimeout(() => {
      //   this.$emit('updateList')
      //   this.cancal()
      // }, 3000)
    }
  }
}
</script>
<style lang="scss"  scoped>
.dialog-confi ::v-deep {
  .el-dialog__body {
    padding: 0 0 50px 30px;
    height: 80%;
    .dialog_content {
      ._p {
        margin: 0;
        ._span {
          float: right;
          margin-right: 20px;
        }
      }
      ._button {
        text-align: center;
        padding-top: 110px;
      }
      .disVirtualList {
        display: flex;
        width: 100%;
        height: auto;
        flex-wrap: wrap;
        margin-top: 20px;

        .disVirtual_content {
          width: 32.3%;
          height: 40px;
          line-height: 40px;
          padding: 0 5px;
          margin: 0 1% 5px 0;
          box-sizing: border-box;
          background: #f5f5f5;
          position: relative;

          .status_class {
            position: absolute;
            right: 10px;
            // top: 13px;
            color: #294261;
          }
        }

        .disVirtual {
          background: #f5f5f5;
        }
      }
    }
  }
}
</style>
