import Vue from "vue";
import Vuex from "vuex";
import app from "./modules/app";
import tagsView from "./modules/tagsView";
import user from "./modules/user";
import router from "./modules/router";
import requestNum from "./modules/requestNum";
import pageParams from "./modules/pageParams";
import operationButton from "./modules/operationButton";
import getters from "./getters";
import networkDisk from "./modules/networkDisk";
import categoryTree from "./modules/categoryTree";
import trainingCamp from "./modules/trainingCamp";
import column from "./modules/column";
import exam from "./modules/exam";
import course from "./modules/course";
Vue.use(Vuex);

const state = {
  defaultAvater: require("@/assets/images/chat/default-avatar.png")
};

const store = new Vuex.Store({
  modules: {
    app,
    tagsView,
    user,
    networkDisk,
    router,
    requestNum,
    pageParams,
    operationButton,
    categoryTree,
    trainingCamp,
    column,
    exam,
    course
  },
  state,
  getters
});

export default store;
