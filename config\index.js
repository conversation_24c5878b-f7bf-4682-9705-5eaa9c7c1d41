"use strict";
// Template version: 1.2.6
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require("path");
const webpackObfuscator = require("webpack-obfuscator");
function pluginsEnvPanel() {
  let plugins = [
    new webpackObfuscator(
      {
        compact: true,
        controlFlowFlattening: false,
        deadCodeInjection: true,
        debugProtection: true,
        debugProtectionInterval: 30,
        disableConsoleOutput: false,
        identifierNamesGenerator: "hexadecimal",
        log: false,
        renameGlobals: false,
        rotateStringArray: false,
        selfDefending: false,
        stringArray: false,
        stringArrayEncoding: ["none"],
        stringArrayThreshold: 0.5,
        unicodeEscapeSequence: false
      },
      []
    )
  ];
  return [plugins[0]];
}
module.exports = {
  //开发环境配置
  dev: {
    // Paths
    assetsSubDirectory: "static",
    assetsPublicPath: "/hy-admin/",
    proxyTable: {},

    // Various Dev Server settings
    host: "localhost", // can be overwritten by process.env.HOST
    //开发服务端口
    port: 9528, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: false,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: true,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    /**
     *  webpack配置选项，用于生成source map文件，方便在调试时定位代码问题。具体来说，它会在打包后的代码中生成一个映射文件，
     *  将打包后的代码与源代码进行映射，从而可以在浏览器控制台中看到源代码的错误信息，而不是打包后的代码。这个选项会影响打包速度和文件大小，
     *  一般在开发环境中使用，生产环境中不建议使用。 开发时如需定位明确可放开，热部署会慢，3-5s左右,cheap-source-map轻量些
     */
    // devtool: 'cheap-source-map',
    // devtool: "source-map",

    // CSS Sourcemaps off by default because relative paths are "buggy"
    // with this option, according to the CSS-Loader README
    // (https://github.com/webpack/css-loader#sourcemaps)
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: false
  },
  //生产环境配置
  build: {
    // Template for index.html
    index: path.resolve(__dirname, "../dist/index.html"),

    // Paths  打包后的文件路径
    assetsRoot: path.resolve(__dirname, "../dist"),
    // 输出子目录
    assetsSubDirectory: "static",

    /**
     * You can set by youself according to actual condition
     * You will need to set this if you plan to deploy your site under a sub path,
     * for example GitHub pages. If you plan to deploy your site to https://foo.github.io/bar/,
     * then assetsPublicPath should be set to "/bar/".
     * In most cases please use '/' !!!
     */
    //静态资源访问路径前缀
    assetsPublicPath: "/hy-admin/",
    plugins: pluginsEnvPanel(),
    /**
     * Source Maps
     */
    //是否生成生产环境的源码映射
    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: "source-map",

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin@5.0.1
    //是否压缩
    productionGzip: true,
    productionGzipExtensions: ["js", "css"],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report || false,

    // `npm run build:prod --generate_report`
    generateAnalyzerReport: process.env.npm_config_generate_report || false
  }
};
