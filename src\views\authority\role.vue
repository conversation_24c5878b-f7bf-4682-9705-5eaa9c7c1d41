<template>
  <div class="app-container">
    <div class="left-menu">
      <div class="left-menu-top">
        <h3>角色</h3>
        <el-button v-if="(isSuperAdmin() &&
          roleItem.roleName !== '超级管理员') || roleItem.uid === '434994947c5a4ee3a710cd277357c7c3'" type="primary"
          size="mini" :disabled="isEdit" @click="handleOpenAdd" v-permission="'/role/add'">
          添加角色
        </el-button>
      </div>
      <div class="left-menu-center">
        <div class="title" @click="defaultRole = !defaultRole">
          <span>默认角色</span>
          <i v-if="!defaultRole" class="el-icon-arrow-right"></i>
          <i v-else class="el-icon-arrow-down"></i>
        </div>
        <div class="list" :style="{ display: defaultRole ? 'block' : 'none' }">
          <div class="list-item" :key="index" v-for="(item, index) in tableData" @click="changeRole(item)" :style="{
            color: roleItem.uid == item.uid ? '#006eff' : '',
            display: item.roleType == 1 ? '' : 'none'
          }">
            <i class="el-icon-user" style="margin-right:10px"></i>
            {{ item.roleName }}
          </div>
        </div>
      </div>
      <div class="left-menu-bottom">
        <div class="title" @click="customRole = !customRole">
          <span>自定义角色</span>
          <i v-if="!customRole" class="el-icon-arrow-right"></i>
          <i v-else class="el-icon-arrow-down"></i>
        </div>
        <div class="list" :style="{ display: customRole ? 'block' : 'none' }">
          <div class="list-item" :key="index" v-for="(item, index) in tableData" @click="changeRole(item)" :style="{
            color: roleItem.uid == item.uid ? '#006eff' : '',
            display: item.roleType == 2 ? '' : 'none'
          }">
            <i class="el-icon-user" style="margin-right:10px"></i>
            {{ item.roleName }}
          </div>
        </div>
      </div>
    </div>
    <div class="right-main">
      <div class="right-main-top">
        <div class="msg">
          <div class="title">
            <i class="el-icon-user" style="margin-right:10px"></i>
            <span class="title-item">
              <h4>{{ roleItem.roleName }}</h4>
            </span>
            <span style="color: #606266;margin-left:5px"></span>
          </div>
          <div class="describe" style="color: #606266;margin-top:10px;font-size:80%">
            {{ roleItem.summary }}
          </div>
        </div>
        <div class="operation">
          <el-button type="text" @click="showPeople">
            查看
          </el-button>
          <el-divider v-if="(isSuperAdmin() &&
            roleItem.roleName !== '超级管理员') || roleItem.uid === '434994947c5a4ee3a710cd277357c7c3'
            " direction="vertical"></el-divider>
          <el-button v-if="(isSuperAdmin()) || roleItem.uid === '434994947c5a4ee3a710cd277357c7c3'
            " :disabled="isEdit" @click="setIsEdit" type="text">
            编辑
          </el-button>
          <el-divider v-if="roleItem.roleType === 2 &&
            (isSuperAdmin() || roleItem.uid === '434994947c5a4ee3a710cd277357c7c3')
            " direction="vertical"></el-divider>
          <el-button v-if="roleItem.roleType == 2 &&
            (isSuperAdmin() || roleItem.uid === '434994947c5a4ee3a710cd277357c7c3')
            " :disabled="isEdit" style="color: #F56C6C;" @click="handleDelete(roleItem)" v-permission="'/role/delete'" type="text">
            删除
          </el-button>
        </div>
      </div>
      <div class="right-main-center" :class="isEdit ? 'isEdit' : ''">
        <el-form :model="form" :rules="rules" ref="form">
          <el-form-item v-if="isEdit">
            <div class="top-item">
              <el-form-item label="角色名称" :label-width="formLabelWidth" prop="roleName">
                <el-input v-model="form.roleName" placeholder="请输入角色名称" auto-complete="off"></el-input>
              </el-form-item>

              <el-form-item label="角色类型" :label-width="formLabelWidth" prop="roleType">
                <el-select v-if="isSuperAdmin()" v-model="form.roleType" placeholder="请选择角色类型">
                  <el-option label="默认角色" :value="1"> </el-option>
                  <el-option label="自定义角色" :value="2"> </el-option>
                </el-select>
                <span v-else>
                  {{ isEdit && form.roleType == 1 ? "默认角色" : "自定义角色" }}
                </span>
              </el-form-item>
            </div>
          </el-form-item>

          <el-form-item v-if="isEdit" label="角色介绍" :label-width="formLabelWidth">
            <el-input type="textarea" v-model="form.summary" placeholder="请输入角色介绍" auto-complete="off"></el-input>
          </el-form-item>

          <el-form-item class="form-button" v-if="isEdit" label=" " :label-width="formLabelWidth">
            <el-button @click="handleClose">取 消</el-button>
            <el-button @click="submitForm" plain type="primary">
              保 存
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-show="showMenus">
        <div class="flex ai-center jc-between">
          <h3>拥有权限</h3>
          <el-button v-if="(isSuperAdmin()) || roleItem.uid === '434994947c5a4ee3a710cd277357c7c3'" :disabled="setRole"
            type="text" @click="setRoleForm">编辑</el-button>
        </div>
        <div class="flex flex-wrap">
          <div v-for="(item, index) in getGroupMenu" :key="index" class="tree-item">
            <div>
              <el-checkbox v-model="selectAllParam[item.index]" :disabled="!setRole" @change="selectGroup(item.index)">
                {{ item.group }}
              </el-checkbox>
            </div>
            <el-tree :check-strictly="checkStrictly" :ref="'tree' + item.index" :data="item.list" node-key="uid"
              @check-change="handleCheckChange" :default-checked-keys="form.categoryMenuUids" :props="defaultProps"
              show-checkbox>
              <span class="custom-tree-node flex jc-between" slot-scope="{ node, data }">
                <span> {{ node.label }} </span>
                <span style="margin-left:20px" v-if="data.menuLevel == 3">
                  <el-radio-group :disabled="!setRole" @change="functionTypeChange(data)" v-model="data.functionType"
                    size="small">
                    <el-radio :label="1">前台</el-radio>
                    <el-radio :label="0">后台</el-radio>
                  </el-radio-group>
                </span>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="flex ai-center jc-center form-button" v-if="setRole">
          <el-button @click="setRole = false">取 消</el-button>
          <el-button @click="submitForm" plain type="primary">
            保 存
          </el-button>
        </div>
      </div>
    </div>

    <el-dialog center :title="`${roleItem.roleName}列表`" :visible.sync="isShow">
      <el-table :data="roleItem.userList">
        <el-table-column prop="nickName" align="center" label="姓名">
        </el-table-column>
        <el-table-column prop="userName" align="center" label="账号">
        </el-table-column>
        <el-table-column label="状态" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0" style="color:#FF3F3F">
              停用
            </span>
            <span v-else-if="scope.row.status == 1" style="color:#15B564">
              正常
            </span>
            <span v-else style="color:#FF3F3F"> 异常 </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="dialog-button">
        <el-button @click="isShow = false">关闭</el-button>
        <el-button v-if="roleItem.roleType == 2 &&
          isSuperAdmin()
          " type="danger" @click="clearUser">
          清空
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNewRoleList,
  getRoleList,
  addRole,
  editRole,
  deleteRole,
  clearRoleUser
} from "@/api/role";

import { mapGetters } from 'vuex'
import { getAllMenu } from "@/api/categoryMenu";
import { getSysDictDataList } from "@/api/sysDictData";
// import { getListByDictTypeList } from '@/api/sysDictData'
import { formatData } from "@/utils/webUtils";
export default {
  beforeDestroy () {
    clearTimeout(this.timer);
  },
  data () {
    return {
      timer: null,
      checkStrictly: false,
      defaultRole: true,
      defaultStyle: "",
      customRole: true,
      customStyle: "",
      roleItem: {},
      defaultProps: {
        // 给树形控件指定属性
        children: "childCategoryMenu",
        label: "name",
        disabled: this.isEdit
      },
      isEdit: false,
      isShow: false,
      tableData: [],
      keyword: "",
      currentPage: 1,
      pageSize: 10,
      total: 0, //总数量
      title: "增加角色",
      dialogFormVisible: false, //控制弹出框
      formLabelWidth: "120px",
      isEditForm: false,
      form: {
        uid: null,
        roleName: "",
        summary: "",
        roleType: 1,
        categoryMenuUids: []
      },
      //分类菜单列表
      categoryMenuList: {},
      // tree配置项
      defaultProps: {
        children: "childCategoryMenu",
        label: "name"
      },
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1到20个字符" }
        ],
        roleType: [
          { required: true, message: "请选择角色类型", trigger: "blur" }
        ]
      },
      setRole: false,
      showMenus: true,
      selectAllParam: {}
    };
  },
  computed: {
    ...mapGetters(['categoryList']),
    groupList () {
      return this.categoryList
    },
    getGroupMenu () {
      let list = []
      const groupList = this.groupList
      groupList.forEach(item => {
        if (this.categoryMenuList[item.dictValue] && this.categoryMenuList[item.dictValue].length > 0) {
          list.push({
            index: item.dictValue,
            list: this.categoryMenuList[item.dictValue],
            group: item.dictLabel
          })
        }
      })
      return list
    }
  },
  created () {
    this.allMenuList();
    this.roleList();
  },
  watch: {
    setRole () {
      for (let key in this.categoryMenuList) {
        this.categoryMenuList[key] = this.setTreeDisabled(this.categoryMenuList[key])
      }

      this.timer = setTimeout(() => {
        this.checkStrictly = false;
      }, 0)
    }
  },
  methods: {
    // 编辑权限
    setRoleForm () {
      this.checkStrictly = true;
      this.setRole = true
      this.isEditForm = true;
    },
    // 编辑角色
    setIsEdit () {
      this.checkStrictly = true;
      this.isEdit = !this.isEdit;
      this.isEditForm = true;
    },
    isSuperAdmin () {
      return this.$store.getters.roles.some(item => {
        return item.roleName === '超级管理员';
      });
    },
    allMenuList: function () {
      getAllMenu().then(response => {
        // console.log(response);
        if (response.code == this.$ECode.SUCCESS) {
          let data = response.data;
          data = this.setTreeDisabled(data);

          data.forEach(item => {
            if (this.categoryMenuList.hasOwnProperty(item.menuCategory)) {
              this.categoryMenuList[item.menuCategory].push(item)
            } else {
              this.$set(this.categoryMenuList, item.menuCategory, [])
              this.categoryMenuList[item.menuCategory].push(item)

              this.$set(this.selectAllParam, item.menuCategory, false)
            }
          })
        }
      });
    },

    // 设为禁用
    setTreeDisabled (list) {
      list.forEach(item => {
        item.disabled = !this.setRole;
        if (item.childCategoryMenu && item.childCategoryMenu.length != 0) {
          item.childCategoryMenu = this.setTreeDisabled(item.childCategoryMenu);
        }
      });
      return list;
    },
    handleFind: function () {
      this.roleList();
    },
    roleList: function () {
      getNewRoleList().then(response => {
        var data = [];
        response.data.forEach((item, index, arr) => {
          if (item.status == 1) {
            data.push(item);
          }
          if (item.roleName === "超级管理员") {
            this.changeRole(item);
          }
        });
        this.tableData = data;
      });
    },
    getFormObject: function () {
      var formObject = {
        uid: null,
        roleName: null,
        summary: null,
        categoryMenuUids: []
      };
      return formObject;
    },

    handleAdd: function () {
      this.title = "增加角色";
      this.dialogFormVisible = true;
      this.form = this.getFormObject();
      setTimeout(() => {
        this.$refs.tree.setCheckedKeys(this.form.categoryMenuUids);
      }, 100);
      this.isEditForm = false;
    },

    handleEdit: function (row) {
      this.title = "编辑角色";
      this.dialogFormVisible = true;
      this.isEditForm = true;
      this.form = row;
      setTimeout(() => {
        this.$refs.tree.setCheckedKeys(this.form.categoryMenuUids);
      }, 100);
    },

    handleDelete: function (row) {
      if (row.userList.length > 0) {
        return this.$alert("该角色仍有员工，无法删除！", "提示", {
          confirmButtonText: "确定"
        });
      }
      this.$confirm("此操作将把分类删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          var params = {};
          params.uid = row.uid;
          deleteRole(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message);
            } else {
              this.$commonUtil.message.error(response.message);
            }
            this.roleList();
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消删除");
        });
    },
    handleCurrentChange: function (val) {
      this.currentPage = val;
      this.roleList();
    },
    submitForm () {
      this.$refs.form.validate(valid => {
        if (!valid) { 
        } else {
          //得到选中树的UID(getCheckedKeys获取非半选  getHalfCheckedKeys获取半选)
          if (this.setRole) {
            this.form.categoryMenuUids = [] 
            this.getGroupMenu.forEach(item => { 
              this.form.categoryMenuUids = [
                ...this.form.categoryMenuUids,
                ...this.$refs[`tree` + item.index][0].getCheckedKeys(),
                ...this.$refs[`tree` + item.index][0].getHalfCheckedKeys()
              ]
            })
          }
          let data = this.$commonUtil.deepClone(this.form);
          data.categoryMenuUids = JSON.stringify(data.categoryMenuUids);

          // console.log(this.form);
          if (this.isEditForm) {
            editRole(data).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message);
                this.dialogFormVisible = false;
                this.handleClose();
                this.roleList();
              } else {
                this.$commonUtil.message.error(response.message);
              }
            });
          } else {
            addRole(data).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message);
                this.dialogFormVisible = false;
                this.handleClose();
                this.roleList();
              } else {
                this.$commonUtil.message.error(response.message);
              }
            });
          }
        }
      });
    },

    // 点击添加
    handleOpenAdd () {
      this.showMenus = false
      this.isEdit = !this.isEdit;
      this.isEditForm = false;
      this.changeRole({
        uid: null,
        roleName: "",
        summary: "",
        categoryMenuUids: [],
        roleType:
          this.isSuperAdmin() ? null : 2
      });
    },

    //勾选树形控件
    handleCheckChange (data, checked, indeterminate) {
      // console.log(indeterminate);
    },
    // 切换角色
    changeRole (item) {
      this.isShow = false;
      let list =
        item.categoryMenuUids != 0 ? JSON.parse(item.categoryMenuUids) : [];
      this.roleItem = item;
      this.form.uid = item.uid;
      this.form.roleName = item.roleName;
      this.form.roleType = item.roleType;
      this.form.summary = item.summary;
      this.form.categoryMenuUids = list;
      this.$nextTick(() => {
        for (let key in this.categoryMenuList) {
          const ref = this.$refs[`tree` + this.categoryMenuList[key][0].menuCategory]
          if (ref) ref[0].setCheckedKeys(list)
        }

        for (let key in this.selectAllParam) {
          this.selectAllParam[key] = false
        }
      })
    },

    // 关闭编辑框
    handleClose () {
      this.showMenus = true
      this.setRole = false
      this.isEdit = false;
      if (this.form.uid == null) {
        this.changeRole(this.tableData[0]);
      }
    },

    //点击查看
    showPeople () {
      this.isShow = !this.isShow;
    },
    functionTypeChange (data) {
      // console.log(data);
    },

    // 清空角色下的用户
    clearUser () {
      clearRoleUser(this.roleItem.userList).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.roleItem.userList = [];
          let list = this.tableData;
          list.forEach((item, index, arr) => {
            if (item.roleName === this.roleItem.roleName) {
              item.userList = [];
            }
          });
          this.tableData = list;
          this.isShow = false;
          this.$commonUtil.message.success(response.message);
        } else {
          this.$commonUtil.message.error(response.message);
        }
      });
    },

    // 选择模块下全部权限
    selectGroup (val) {
      this.$nextTick(() => {
        const menus = this.getGroupMenu
        const param = menus.find(item => item.index == val)
        let list = param ? param.list : []
        list = list.map(item => item.uid)
        this.$refs['tree' + val][0].setCheckedKeys(this.selectAllParam[val] ? list : [])
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-item {
  // min-width: 250px;
  padding: 10px;
  margin: 10px;
  // border: 1px solid #666;
}

.app-container {
  display: flex;
  justify-content: flex-start;

  .left-menu {
    min-width: 170px;
    width: 200px;

    .left-menu-top {
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dcdfe6;
    }

    .title {
      padding: 10px;
      padding-bottom: 0;
      color: #606266;
      font-weight: 400;
      cursor: pointer;
    }

    .list {
      padding: 0 10px;

      .list-item {
        margin: 20px 0;
        cursor: pointer;
      }
    }
  }

  .right-main {
    margin-left: 20px;

    .right-main-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 10px;
      height: 60px;

      .msg {
        .title {
          .title-item {
            position: relative;

            h4 {
              display: inline;
            }
          }
        }
      }

      .operation {
        margin-right: 20px;
      }
    }

    .right-main-center {
      transition: padding 1s, margin 1s, border-radius 1s, border 1s;
      -webkit-transition: padding 1s, margin 1s, border-radius 1s, border 1s;
    }
  }
}

/deep/ .el-divider {
  background-color: #8181ef;
}

.isEdit {
  padding: 30px 50px 20px 20px;
  margin: 20px;
  border-radius: 5px;
  border: 1px solid #606266;
  transition: padding 1s, margin 1s, border-radius 1s, border 1s;
  -webkit-transition: padding 1s, margin 1s, border-radius 1s, border 1s;
}

/deep/ .top-item {
  display: flex;
  justify-content: flex-start;

  .el-input {
    width: 300px;
  }
}

/deep/ .form-button {
  button {
    width: 120px;
    height: 30px;
    padding: 0 20px;
    background: white;
  }

  .el-button--primary {
    border: 1px solid #006eff;

    span {
      color: #006eff;
    }
  }

  button:hover {
    background: #006eff;

    span {
      color: white;
    }
  }

  .el-form-item__content {
    display: flex;
    justify-content: center;
  }
}

/deep/ .dialog-button {
  margin-top: 20px;
  display: flex;
  justify-content: center;

  .el-button {
    padding: 6px;
    width: 100px;
    height: 30px;
  }
}
</style>
