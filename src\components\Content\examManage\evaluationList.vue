<template>
  <div class="evaluation-list">
    <el-page-header content="返回考试管理首页" @back="goBack"/>
    <div class="evaluation-content">
      <el-tabs v-model="reviewStatus" @tab-click="tabClick">
        <el-tab-pane label="全部" name="1"/>
        <el-tab-pane label="缺考" name="4"/>
        <el-tab-pane label="已批阅" name="3"/>
      </el-tabs>
      <!-- 温馨提示 -->
      <el-tag type="warning"><i class="el-icon-info"/>提示：该考试中部分题目修改过正确选项，修改前已提交的答案仍按修改前的正确选项判断正误。</el-tag>
      <!-- 搜索表单区域 -->
      <template>
        <div class="search">
          <span>{{ examInfo.examName }}</span>
          <el-input v-model="searchForm.keyword" placeholder="请输入内容" class="input-with-select">
            <el-select slot="prepend" v-model="searchForm.type" placeholder="请选择">
              <el-option :value="5" label="身份证号码"/>
              <el-option :value="2" label="真实姓名"/>
              <el-option :value="3" label="手机号"/>
            </el-select>
            <el-button slot="append" icon="el-icon-search" @click="search"/>
          </el-input>
        </div>
      </template>
      <!-- 表格区域 -->
      <template>
        <el-table :cell-style="{ textAlign: 'center' }" :header-cell-style="{textAlign:'center'}" :data="reviewTableData" class="table" style="width: 100%">
          <el-table-column type="index" align="center" label="序号" width="60" />
          <el-table-column prop="userName" label="姓名" width="180"/>
          <el-table-column prop="idCard" label="身份证号码" width="180"/>
          <el-table-column prop="phone" label="手机号" width="180"/>
          <el-table-column prop="finalScore" label="成绩">
            <template slot-scope="scope">
              <span>{{ scope.row.isReview == 2 ? '-' : scope.row.finalScore }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="passExamScore" label="及格线"/>
          <el-table-column prop="commitTime" label="提交时间">
            <template slot-scope="scope">
              <span>{{ scope.row.isReview == 2 ? '-' : scope.row.commitTime }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isReview" label="状态">
            <template slot-scope="scope">
              <span v-if="scope.row.isReview == 0">未批阅</span>
              <span v-if="scope.row.isReview == 1">已批阅</span>
              <span v-if="scope.row.isReview == 2">缺考</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="address" label="批阅人">
            <template slot-scope="scope">
              <span>-</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="certificateStatusLabel" label="证书">
            <template slot-scope="scope">
              <span>{{ scope.row.isReview == 2 ? '-' : scope.row.certificateStatusLabel }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作">
            <template slot-scope="scope">
              <el-button :disabled="scope.row.certificateStatusLabel !== '待审核' || examInfo.examType === 0 || scope.row.isReview == 2" type="text" class="watch" @click="examine(scope.row)">审核</el-button>
              <el-button :disabled="scope.row.isReview == 2" type="text" class="watch" @click="watch(scope.row)">查看</el-button>
              <el-button v-if="scope.row.isReview == 0" type="text" class="watch">批阅</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页区域 -->
      <template>
        <div class="pagination">
          <el-pagination
            :current-page="page"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"/>
        </div>
      </template>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      title="审核"
      width="30%">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
        <el-form-item label="审核类型" prop="pass">
          <el-radio-group v-model="ruleForm.pass">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="0">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.pass === 1" label="证书编号" prop="noType">
          <el-radio-group v-model="ruleForm.noType">
            <el-radio :label="0">自动生成</el-radio>
            <el-radio :label="1">手动配置</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.noType === 1 && ruleForm.pass === 1" required>
          <el-row>
            <el-col :span="11"><div :title="ruleForm.certificateNo" class="ellipsis">{{ ruleForm.certificateNo }}</div></el-col>
            <el-col :span="2"><i class="el-icon-plus"/></el-col>
            <el-col :span="11">
              <el-form-item prop="codeNum">
                <el-input v-model="ruleForm.codeNum" :placeholder="`请输入五位数字`" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item v-if="ruleForm.pass === 0" label="备注原因">
          <el-input v-model="ruleForm.remark" :rows="6" :height="'65px'" type="textarea" maxlength="255" show-word-limit/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('ruleForm')">取消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import { getReviewList, getReviewDetail, getNoConfig, certificateAudit } from '@/api/content/exam'
export default {
  inject: ['examParentThis'],
  computed: {
    ...mapGetters(['examInfo'])
  },
  data() {
    return {
      dialogVisible: false,
      reviewStatus: '1',
      reviewTableData: [],
      searchForm: {
        type: 5,
        keyword: ''
      },
      ruleForm: {
        pass: '',
        noType: '',
        certificateNo: '',
        codeNum: '',
        remark: ''
      },
      rules: {
        pass: [{ required: true, message: `请选择审核类型`, trigger: ['blur', 'change'] }],
        noType: [{ required: true, message: `请选择证书编号`, trigger: ['blur', 'change'] }],
        codeNum: [{ required: true, message: `请输入五位数字`, trigger: ['blur', 'change'] },
          { pattern: /^\d{1,5}$/, message: '请输入一个5位的正整数', trigger: ['blur', 'change'] }]
      },
      examineeItem: {},
      page: 1,
      pageSize: 10,
      total: 0
    }
  },
  mounted() {
    this.initReviewList()
  },
  methods: {
    ...mapMutations({
      setRevieDetail: 'exam/setRevieDetail'
    }),
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          const params = {
            examUid: this.examineeItem.examUid,
            examRecordUid: this.examineeItem.uid,
            remark: this.ruleForm.remark,
            userUid: this.examineeItem.userUid,
            certificateNo: this.ruleForm.certificateNo + this.ruleForm.codeNum,
            noType: this.ruleForm.noType,
            pass: this.ruleForm.pass
          }
          const result = await certificateAudit(params)
          if (result.code === 200) {
            this.$message.success('审核成功')
            this.resetForm('ruleForm')
            this.initReviewList()
          }
        }
      })
    },
    resetForm(formName) {
      this.dialogVisible = false
      this.$refs[formName].resetFields()
    },
    search() {
      this.initReviewList()
    },
    async examine(data) {
      this.ruleForm = {
        pass: '',
        noType: '',
        codeNum: '',
        remark: ''
      }
      this.dialogVisible = true
      this.examineeItem = data
      const params = {
        examUid: data.examUid
      }
      const result = await getNoConfig(params)
      if (result.code === 200) {
        this.ruleForm.certificateNo = result.data
      }
    },
    async watch(data) {
      const params = {
        examRecordUid: data.uid
      }
      const result = await getReviewDetail(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.setRevieDetail(result.data)
      }
      this.examParentThis.flag = 4
    },
    async initReviewList() {
      const params = {
        trainExamUid: this.examInfo.uid,
        isReview: this.reviewStatus - 2 == -1 ? null : this.reviewStatus - 2,
        currentPage: this.page,
        pageSize: this.pageSize,
        type: this.searchForm.type,
        keyword: this.searchForm.keyword
      }
      const result = await getReviewList(params)
      if (result.code == this.$ECode.SUCCESS) {
        console.warn(result)
        this.total = result.data.total
        this.reviewTableData = result.data.records
      }
    },
    // 返回考试管理首页
    goBack() {
      this.examParentThis.flag = 2
    },
    dataReset() {
      this.searchForm = {
        type: 5,
        keyword: ''
      }
      this.page = 1
    },
    tabClick(val) {
      this.dataReset()
      this.initReviewList()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.initReviewList()
    },
    handleCurrentChange(val) {
      this.page = val
      this.initReviewList()
    }
  }
}
</script>
<style scoped lang="scss">
.evaluation-list{
    .evaluation-content{
        margin-top: 20px;
        .table{
            // span{
            //     color: #1472ff;
            //     cursor: pointer;
            // }
        }
        .pagination{
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .search{
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .input-with-select{
                width: 380px;
                .el-select{
                    width: 102px;
                }
            }
        }
    }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
