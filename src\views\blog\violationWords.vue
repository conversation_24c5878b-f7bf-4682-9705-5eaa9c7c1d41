<!--违规词管理-->
<template>
  <div class="violationWords-box p20">
    <div class="f violationWords-header mb10 p10">
      <div class="f fd-r f-s-14 f-c-1 f-w-2 ai-c">
        <div class="mr10">违规词管理</div>

        <el-date-picker
          v-model="keyword.params.timeRanges"
          class="mr10"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="timeRangeChange" />
        <el-select v-model="keyword.params.status" placeholder="请选择审核状态" @change="selectChange">
          <el-option v-for="i in status" :key="i.id" :label="i.label" :value="i.id" />
        </el-select>
      </div>
    </div>
    <div class="f fd-c f1">
      <el-table :data="keyword.list" class="f1" header stripe style="width: 100%;" height="auto">
        <el-table-column :index="indexMethod" type="index" />
        <el-table-column prop="keyword" label="词条" />
        <el-table-column prop="passKeyword" label="通过的词条" />
        <el-table-column prop="updateTime" label="时间" width="180" />
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <span :class="[scope.row.status===1?'f-c-n':'f-c-s']"> {{ scope.row.status===1?'未审':'已审' }}</span>
          </template>
        </el-table-column>
        <el-table-column width="120">
          <template slot="header" slot-scope="scope">
            <el-button size="mini" @click="exportTxT.show=true">导出白名单</el-button>
          </template>
          <template slot-scope="scope">
            <el-button type="primary" @click="checkFn(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="p20">
        <el-pagination
          :current-page="keyword.params.currentPage"
          :page-sizes="[10,20, 40, 80, 100]"
          :page-size="keyword.params.pageSize"
          :total="keyword.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
    <el-dialog :visible.sync="check.show" title="违规词编辑" width="70%" show-close>
      <el-transfer v-model="transfer.value" :data="transfer.data" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="check.show = false">取 消</el-button>
        <el-button type="primary" @click="submitCheck">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="exportTxT.show"
      title="白名单导出"
      width="500px"
      @open="setTimeRange"
    >
      <div class="mb10">请选择导出参数</div>
      <div class="f fd-c jc-c">
        <el-date-picker
          v-model="exportTxT.params.timeRanges"
          class="mb10"
          style="width:100%"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="txtTimeRangeChange"
        />
        <!-- <el-select v-model="exportTxT.params.status" style="width:100%" placeholder="请选择审核状态">
          <el-option v-for="i in status" :key="i.id" :label="i.label" :value="i.id" />
        </el-select> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="exportTxT.show = false">取 消</el-button>
        <el-button type="primary" @click="exportWordsTxt">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {checkKeyword, exportWords, getKeywordList} from '@/api/violationWords'

export default {
  name: 'ViolationWords',
  data: () => ({
    keyword: {
      params: {
        adminUid: '',
        currentPage: 1,
        pageSize: 20,
        total: 0,
        startDate: '',
        endDate: '',
        status: '',
        timeRanges: []
      },
      list: []
    },
    check: {
      show: false,
      params: {}
    },
    item: null,
    transfer: {
      data: [],
      value: []
    },
    exportTxT: {
      show: false,
      params: {
        timeRanges: [],
        startDate: '',
        endDate: '',
        status: ''
      }
    },
    status: [
      { id: '', label: '全部' },
      { id: 1, label: '未审核' },
      { id: 2, label: '已审核' }
    ]

  }),
  created() {
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return (this.keyword.params.currentPage - 1) * this.keyword.params.pageSize + index + 1
    },
    getList() {
      getKeywordList(this.keyword.params).then(res => {
        if (res.code === this.$ECode.SUCCESS) {
          this.keyword.list = res.data.records
          this.keyword.total = res.data.total
        }
      })
    },
    selectChange() {
      this.keyword.params.currentPage = 1
      this.getList()
    },
    setTimeRange() {
      // this.exportTxT.params.startDate = this.keyword.params.timeRanges[0] || ''
      // this.exportTxT.params.endDate = this.keyword.params.timeRanges[1] || ''
    },
    timeRangeChange() {
      this.keyword.params.startDate = this.keyword.params.timeRanges[0] || ''
      this.keyword.params.endDate = this.keyword.params.timeRanges[1] || ''
      this.keyword.params.currentPage = 1
      this.getList()
    },
    setTransfer(v) {
      this.transfer.data = []
      this.transfer.value = []
      this.transfer.data = v.keyword.split(',').map((i, idx) => {
        return { key: idx, label: i }
      })
      v.passKeyword.split(',').forEach(i => {
        const index = this.transfer.data.findIndex((l, ldx) => { return l.label === i })
        index > -1 && this.transfer.value.push(this.transfer.data[index].key)
      })
      console.log('this.transfer', this.transfer)
    },
    handleSizeChange(v) {
      this.keyword.params.pageSize = v
      this.getList()
    },
    handleCurrentChange(v) {
      this.keyword.params.currentPage = v
      this.getList()
    },
    handleClose() {
      this.item = null
    },
    checkFn(v) {
      // 保存 单挑要修改的记录
      this.item = JSON.parse(JSON.stringify(v)) || null
      // 初始化穿梭框数据
      this.setTransfer(this.item)
      this.check.show = true
    },
    submitCheck() {
      const pass = this.transfer.value.map(i => {
        return [this.transfer.data[i].label]
      }).join(',')
      this.$confirm(pass, '是否放行以下词语', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        checkKeyword({ words: pass, uid: this.item.uid }).then(res => {
          if (res.code === this.$ECode.SUCCESS) {
            this.$message.success('审核成功')
            this.check.show = false
            this.getList()
          }
        })
      }).catch(() => {
      })
    },
    txtTimeRangeChange() {
      this.exportTxT.params.startDate = this.exportTxT.params.timeRanges[0] || ''
      this.exportTxT.params.endDate = this.exportTxT.params.timeRanges[1] || ''
      // this.exportWordsTxt()
    },
    exportWordsTxt() {
      // 导出白名单txt
      exportWords(this.exportTxT.params).then(res => {
        if (res.code === this.$ECode.SUCCESS) {
          if (!res.data) return this.$message.warning('找不到符合条件的白名单结果')
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const a = document.createElement('a')
          a.href = url
          a.download = `违规词白名单_${this.exportTxT.params.startDate ? `${this.exportTxT.params.timeRanges.join('_')}` : '全部'}.txt`
          a.click()
        }
      })
    }
  }
}
</script>
<style lang='scss' scoped>
.violationWords-box {
  >div:first-child {
    background-color: #f4f4f4;
  }

  >div+div {
    overflow-y: auto;
    height: calc(100vh - 220px);
  }
}
</style>
