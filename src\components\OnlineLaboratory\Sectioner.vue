<!---->
<template>
  <div class="sectioner">
    <div class="normal-item-box">
      <div v-for="(i, idx) in list" :key="i.id" :class="{ 'is-active': active === i.id }" class="normal-item"
        @click="selectFn(i, idx)">
        <span style="white-space: nowrap;">{{ i.label }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Sectioner',
  props: {
    select: {
      type: Number | String
    },
    height: {
      type: Number | String,
      default: 34
    },
    list: {
      type: Array,
      default: () => {
        return [
          { id: 0, label: '选一' },
          { id: 1, label: '选二' },
          { id: 2, label: '选三' }
        ]
      }
    }
  },
  data() {
    return {
      active: 0,
      domWidth: 600
    }
  },
  mounted() {
    let w = 0
    const domArr = this.$el.querySelectorAll('.normal-item')
    domArr.forEach(i => {
      w += i.offsetWidth * 1
    })
    this.domWidth = w + 12 + (domArr.length - 1) * 6 // 12是左右的padding
  },
  created() {
    this.active = this.list[0].id
  },
  methods: {
    selectFn(i, idx, event) {
      //   const domArr = this.$el.querySelectorAll('.normal-item')
      //   this.$el.querySelector('.active-item').style.left = domArr[idx].offsetLeft + 'px'
      this.active = i.id
      this.$emit('update:select', i.id)
      this.$emit('change', i.id)
    }
  }
}
</script>
<style lang='scss' scoped>
.sectioner {
  padding: 3px 3px;
  height: 34px;
  display: inline-block;
  background-color: #F2F3F5;
  border-radius: 3px;

  >div {

    width: 100%;
    height: 100%;
  }

  >.normal-item-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;
    z-index: 3;

    >div {
      // min-width: 64px;
      padding: 3px 12px;
      height: inherit;
      margin-right: 3px;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &.is-active {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #006eff;
        background-color: #fff;
      }

      &:hover {
        background-color: #fff;
      }
    }

    >div:last-child {
      margin-right: 0px;
    }
  }
}
</style>
