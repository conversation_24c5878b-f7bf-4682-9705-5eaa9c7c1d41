import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeChannel/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeChannel/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeChannel/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeChannel/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeChannel/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeChannel/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
