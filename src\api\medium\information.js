import request from '@/utils/request'

// 分页列表
export function getList (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/list',
    method: 'post',
    data: params
  })
}

// 新增栏目
export function addColumn (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/add',
    method: 'post',
    data: params
  })
}

// 更新栏目
export function editColumn (params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/update',
    method: 'post',
    data: params
  })
}