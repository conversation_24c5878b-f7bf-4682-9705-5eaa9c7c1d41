import request from '@/utils/request'

// process.env.ADMIN_API
const api = process.env.ADMIN_API

// 列表
export function curriculumList(data) {
  return request({
    url: api + '/curriculum/getList',
    method: 'post',
    data
  })
}

export function curriculumAdd(data) {
  return request({
    url: api + '/curriculum/add',
    method: 'post',
    data
  })
}

export function curriculumEdit(data) {
  return request({
    url: api + '/curriculum/edit',
    method: 'post',
    data
  })
}

export function curriculumDel(data) {
  return request({
    url: api + '/curriculum/deleteBatch',
    method: 'post',
    data
  })
}

export function curriculumUpdateStatus(data) {
  return request({
    url: api + '/curriculum/updateStatus',
    method: 'get',
    params: data
  })
}

export function getCurriculumCategoryList(data) {
  return request({
    url: api + '/curriculum/getCurriculumCategoryList',
    method: 'get',
    params: data
  })
}

// 获取课程详情
export function getCurriculumInfo(data) {
  return request({
    url: api + '/curriculum/getInfo',
    method: 'get',
    params: data
  })
}

//删除检查
export function deleteCheck(data) {
  return request({
    url: api + '/curriculum/deleteCheck',
    method: 'post',
    data
  })
}