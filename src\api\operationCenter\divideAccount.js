import request from "@/utils/request";

export function getSettingList(params) {
  return request({
    url: process.env.ADMIN_API + "/ledgerParam/getList",
    method: "post",
    data: params
  });
}

export function addSetting(params) {
  return request({
    url: process.env.ADMIN_API + "/ledgerParam/add",
    method: "post",
    data: params
  });
}

export function editSetting(params) {
  return request({
    url: process.env.ADMIN_API + "/ledgerParam/edit",
    method: "post",
    data: params
  });
}

export function delSetting(params) {
  return request({
    url: process.env.ADMIN_API + "/ledgerParam/delete",
    method: "post",
    data: params
  });
}