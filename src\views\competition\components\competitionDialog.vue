<template>
    <div>
        <el-dialog :visible="comDialog" width="70%" :title="infoItem.title" :show-close="false">
            <div class="creat_time">
                <div class="time">发布时间:{{ infoItem.createTime }}</div>
                <div class="div_info">{{ infoItem.competitionType | filterType }}</div>
                <div class="div_info">{{ infoItem.status | filterStatus }}</div>
                <div class="clone_btn" @click="comDialog = false">
                    <i class="el-icon-close"></i>
                </div>
            </div>

            <div class="match_info">

                <div class="info_div">
                    <div class="div_1">报名时间</div>
                    <div class="div_2" v-if="infoItem.signUpStartTime">{{ infoItem.signUpStartTime
                    }}至{{ infoItem.signUpEndTime }}</div>
                    <div class="div_2" v-else>暂无</div>
                </div>

                <div class="info_div">
                    <div class="div_1">比赛时间</div>
                    <div class="div_2" v-if="infoItem.competitionStartTime">{{ infoItem.competitionStartTime
                    }}至{{ infoItem.competitionEndTime }}</div>
                    <div class="div_2" v-else>暂无</div>
                </div>
                <div class="info_div">
                    <div class="div_1">主办方</div>
                    <div class="div_2">{{ infoItem.createBy }}</div>
                </div>
                <div class="info_div">
                    <div class="div_1">比赛链接</div>
                    <div class="div_2">{{ infoItem.competitionLink }}</div>
                </div>
                <div class="info_div">
                    <div class="div_1">比赛方式</div>
                    <div class="div_3">{{ infoItem.competitionWay }}
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        infoItem: {
            type: Object,
            default: () => { return {} }
        }
    },
    filters: {
        filterType: function (val) {
            if (val == 1) {
                return '国赛';
            } else if (val == 2) {
                return '省赛';
            } else if (val == 3) {
                return '行业赛';
            } else if (val == 4) {
                return '企业赛';
            }
        },

        filterStatus: function (val) {
            if (val == 4) {
                return '报名中';
            } else if (val == 3) {
                return '待开赛';
            } else if (val == 2) {
                return '进行中';
            } else if (val == 1) {
                return '已结束';
            } else if (val == 0) {
                return '未开始';
            }
        }
    },
    data() {
        return {
            comDialog: false,
        }
    },
}
</script>

<style lang="scss" scoped>
/deep/.el-dialog__body {
    padding: 5px 20px;
    position: relative;

    .creat_time {
        height: 40px;
        border-bottom: 1px solid #ccc;
        display: flex;
        flex-direction: row;
        align-items: center;

        .time {
            font-size: 14px;
            color: #666;
        }

        .div_info {
            margin: -3px 8px;
            border: 1px solid#006eff !important;
            padding: 5px 6px;
            border-radius: 5px;
            line-height: 14px;
            color: #006eff !important;
            height: 25px;

        }

        .clone_btn {
            cursor: pointer;
            flex: 1;
            justify-content: end;
            display: flex;
            margin-bottom: 105px;
        }
    }



    .match_info {
        margin-top: 20px;

        .info_div {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 15px;

            .div_1 {
                min-width: 62px;
                text-align: right;
                color: black;
            }

            .div_2 {
                margin-left: 15px;
                color: #999;
                padding-left: 5px;
                height: 25px;
                width: 50%;
                line-height: 25px;
                background: #F4F6F7;
            }

            .div_3 {
                margin-left: 19px;
                color: #999;
                padding-left: 5px;
                height: 55px;
                width: 100%;
                line-height: 25px;
                background: #F4F6F7;
            }
        }
    }
}
</style>