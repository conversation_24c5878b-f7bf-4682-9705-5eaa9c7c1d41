// class Socket {
//     static singleObj = null;
//     static getObj = wsUrl => {
//         if (!Socket.singleObj) Socket.singleObj = new Socket(wsUrl);
//         return Socket.singleObj;
//     };
//     static RECONNECT_NUM = 5; // 最多重试次数, 为0表示不重连
//     static RECONNECT_DELAY = 3000 // 断线重连间隔
//     constructor(wsUrl) {
//         this.socket = null; // 原生websocket连接
//         this.wsUrl = wsUrl; // ws地址
//         this.isSupport = true; // 是否支持ws
//         this.recNum = 0; // 重连次数
//         this.eventHandle = {}
//         if (typeof WebSocket === "undefined") {
//             this.isSupport = false;
//             alert("你的浏览器不支持WebSocket!请更新浏览器!");
//         }
//     }
//     // 根据与后台协商的协议解析数据
//     messageToObject(messageData) {
//         const messageObj = typeof messageData === 'string' && JSON.parse(messageData)
//         const { eventName, data } = messageObj
//         return { eventName, data }
//     }
//     // 根据与后台协商的协议解析数据
//     messageObjToString(eventName, dataObj) {
//         return JSON.stringify({ eventName, data: dataObj })
//     }
//     connect() {
//         if (!this.isSupport) return Promise.reject("Do not support Websocket!");
//         if (this.socket) return Promise.resolve(this);
//         console.log('connect...')
//         const socket = new WebSocket(this.wsUrl);
//         socket.onclose = e => {
//             console.log("close触发", e);
//             // 默认规定可监听'wsClose'事件
//             this.eventHandle.wsClose && this.eventHandle.wsClose(e)
//             this.socket = null;
//             // 1000 表示正常关闭; 无论为何目的而创建, 该链接都已成功完成任务。
//             if (e.code !== 1000 && this.recNum < Socket.RECONNECT_NUM) {
//                 setTimeout(() => {
//                     this.connect();
//                     this.recNum++;
//                     console.warn('断线自动重连', this.recNum)
//                     if (this.recNum >= Socket.RECONNECT_NUM) console.error('重连次数达到上限!')
//                 }, Socket.RECONNECT_DELAY);
//             }
//         };
//         socket.onmessage = websocketData => {
//             const messageData = websocketData.data
//             const { eventName, data } = this.messageToObject(messageData)
//             if (this.eventHandle[eventName]) this.eventHandle[eventName](data)
//         };
        
//         return new Promise((resolve, reject) => {
//             socket.onopen = () => {
//                 console.log(this.wsUrl, "connect successfully!");
//                 this.recNum = 0; // 重置重连次数
//                 this.socket = socket;
//                 // 默认规定可监听'wsOpen'事件
//                 this.eventHandle.wsOpen && this.eventHandle.wsOpen()
//                 resolve(this);
//             };
//             socket.onerror = e => {
//                 console.log(this.wsUrl, "connection failed!", e);
//                 this.socket = null;
//                 reject(e);
//                 // 默认规定可监听'wsError'事件
//                 this.eventHandle.wsError && this.eventHandle.wsError(e)
//             };
//         });
//     }
//     on(eventName, callBack = () => { }) {
//         this.eventHandle[eventName] = callBack
//     }
//     off(eventName) {
//         this.eventHandle[eventName] = null
//     }
//     emitEmptyEvent(data) {
//         if (!this.socket || this.socket.readyState !== 1) {
//             console.error(eventName, 'emit失败!')
//             return
//         }
//         this.socket.send(JSON.stringify(data));
//     }
//     emit(eventName, dataObj) {
//         if (!this.socket || this.socket.readyState !== 1) {
//             console.error(eventName, 'emit失败!')
//             return
//         }
//         this.socket.send(this.messageObjToString(eventName, dataObj))
//     }
//     disconnect() {
//         this.socket && this.socket.close(1000)
//         this.socket = null
//     }
// }
// const socket = Socket.getObj("ws://127.0.0.1:3000");
// // 可以暴露整个Socket类, 而不是实例


class SocketIo {
    static pingObj = {
        "fromUserUid": "",
        "toUserUid": "",
        "content": "",
        "type": 4
    }
    static Socket = null;
    static setIntervalWesocketPush = null;
    constructor(wsUrl, ...protocols) {
        this.oprationFlag = 1;//正常操作
        this.wsUrl = wsUrl;
        this.protocols = protocols;
    }
    connection() {
        this.Socket && this.Socket.close()
        if (!this.Socket) {
            console.log('建立websocket连接')
            if ('WebSocket' in window) {
                this.Socket = new WebSocket(this.wsUrl, this.protocols);
            } else if ('MozWebSocket' in window) {
                this.Socket = new MozMozWebSocket(this.wsUrl);
            } else {
                console.log("当前浏览器不支持WebSocket");
            }
            if (this.Socket) {
                this.Socket.onopen = this.onopenWS.bind(this);
                this.Socket.onmessage = this.onmessageWS.bind(this);
                this.Socket.onerror = this.onerrorWS.bind(this);
                this.Socket.onclose = this.oncloseWS.bind(this);
            }
        } else {
            console.log('websocket已连接')
        }
    }
    // 销毁websocket
    destroyWebsocket() {
        // 主动关闭标识
        this.oprationFlag = 2;
        this.Socket && this.Socket.close();
        clearInterval(this.setIntervalWesocketPush);
        this.Socket = null;
    }
    /**打开WS之后发送心跳 */
    onopenWS() { 
        this.sendPing();
    }
    /**连接失败重连 */
    onerrorWS() { 
        this.Socket.close()
        clearInterval(this.setIntervalWesocketPush)
        console.log('连接失败重连中')
        if (this.Socket.readyState !== 3) {
            this.Socket = null
            this.connection()
        }
    }
    /**WS数据接收统一处理 */
    onmessageWS(e) { 
        window.dispatchEvent(new CustomEvent('onmessageWS', {
            detail: {
                data: e.data
            }
        }))
    }
    /**
     * 发送数据但连接未建立时进行处理等待重发
     * @param {any} message 需要发送的数据
     */
    connecting(message) { 
        setTimeout(() => {
            if (this.Socket.readyState === 0) {
                this.connecting(message)
            } else {
                this.Socket.send(JSON.stringify(message))
            }
        }, 1000)
    }
    /**
     * 发送数据
     * @param {any} message 需要发送的数据
     */
    sendWSPush(message) { 
        if (this.Socket !== null && this.Socket.readyState === 3) {
            this.Socket.close()
            this.connection()
        } else if (this.Socket.readyState === 1) {
            this.Socket.send(JSON.stringify(message))
        } else if (this.Socket.readyState === 0) {
            this.connecting(message)
        }
    }
    oncloseWS(event) { 
        if (this.oprationFlag == 2) {
            console.log("主动关闭");
        } else { 
            console.log("非正常关闭了", event)
            clearInterval(this.setIntervalWesocketPush)
            console.log('websocket已断开....正在尝试重连')
            if (this.Socket.readyState !== 2) {
                this.Socket = null
                this.connection()
            }
        }
    }
    /**发送心跳
     * @param {number} time 心跳间隔毫秒 默认5000
     * @param {string} ping 心跳名称 默认字符串ping
     */
    sendPing(time = 20000) { 
        clearInterval(this.setIntervalWesocketPush)
        // this.Socket.send(JSON.stringify(SocketIo.pingObj))
        this.setIntervalWesocketPush = setInterval(() => {
            this.Socket.send(JSON.stringify(SocketIo.pingObj))
        }, time)
    }
}
export default SocketIo;