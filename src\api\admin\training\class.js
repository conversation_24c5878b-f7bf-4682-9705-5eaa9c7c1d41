import request from '@/utils/request'

/**
 * 专业/班级列表
 */
export function searchAPI(data) {
  return request({
    url: '/training/pjtMajorClass/backSearchMajorClass',
    method: 'post'
  })
}

/**
 * 专业/班级新增
 */
export function addAPI(data) {
  return request({
    url: '/training/pjtMajorClass/insertMajorClass',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 专业/班级编辑
 */
export function editAPI(data) {
  return request({
    url: '/training/pjtMajorClass/updateMajorClass',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 专业/班级删除
 */
export function dropAPI(data) {
  return request({
    url: '/training/pjtMajorClass/deletedMajorClass',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
