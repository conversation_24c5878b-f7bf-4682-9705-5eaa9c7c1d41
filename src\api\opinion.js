// 反馈管理-意见
import request from '@/utils/request'

// 获取意见列表
export function getList(param) {
  return request({
    url: process.env.ADMIN_API + '/opinion/pageInfo',
    method: 'post',
    data: param
  })
}

// 删除意见列表
export function deleteOpinion(param) {
  return request({
    url: process.env.ADMIN_API + '/opinion/deleteBatch',
    method: 'post',
    data: param
  })
}

// 查询反馈意见详情
export function getInfoByUid(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/opinion/getInfoByUid',
    method: 'get',
    params
  })
}
