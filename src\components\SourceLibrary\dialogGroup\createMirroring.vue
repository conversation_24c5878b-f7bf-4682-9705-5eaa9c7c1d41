<template>
  <div class="dialog">
    <dialogCom
      :title="title"
      :dialog-visible.sync="dialogFlag"
      :cancel="_closeDialog"
      :dis-flag="disFlag"
      :submit="_submit"
    >
      <template>
        <div class="dialog_item">
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="镜像名称" prop="name">
              <el-input v-model="form.name" type="text"/>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import { cloudHostCreateImage } from '@/api/sourceLibrary/virtualApi'
import dialogCom from '@/components/SourceLibrary/dialogGroup/index'
export default {
  components: {
    dialogCom
  },
  props: {
    title: {
      type: String,
      default: null
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    createData: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      disFlag: false,
      dialogFlag: false,
      form: {
        name: ''
      },
      rules: {
        name: { required: true, message: '请输入镜像名称', trigger: 'blur' }
        // name:[{validator: "validatePass", trigger: 'blur' }]
      }
    }
  },
  watch: {
    dialogVisible: {
      deep: true,
      immediate: true,
      handler(val) {
        this.dialogFlag = val
      }
    }
  },
  created() {
    this.dialogFlag = this.dialogVisible
    this.getList()
  },
  methods: {
    // 获取数据列表
    getList() {},
    _closeDialog() {
      this.$parent.dialogCreateFlag = false
    },
    _submit() {
      this.$refs.form.validate(async(valid) => {
        if (valid) {
          this.disFlag = true
          const paramas = {
            cloudHostId: this.createData[0].id,
            imageName: this.form.name
          }
          await cloudHostCreateImage(paramas)
            .then((res) => {
              if (res.code == 200) {
                // this.$message.success(res.msg)
                res.name = this.createData[0].facilityName + '生成镜像， '
                this.$parent.msgList = res
                this.$emit('msgShow')
                this._closeDialog()
              } else {
                this.$message.error(res.msg)
              }
              this.disFlag = false
            })
            .catch((err) => {
              this.disFlag = false
              console.log(err)
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
