import request from '@/utils/request'

// 查询素材库图片分组
export function selectPictureGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/pictureSort/selectPictureGroup',
    method: 'get',
    params: params
  })
}


// 添加素材库图片分组
export function addPictureGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/pictureSort/addPictureGroup',
    method: 'post',
    data: params
  })
}


// 图片分组拖拽排序
export function exchangeGroupSort (params) {
  return request({
    url: process.env.ADMIN_API + '/pictureSort/exchangeGroupSort',
    method: 'get',
    params: params
  })
}


// 修改素材库图片分组名称
export function updateGroupName (params) {
  return request({
    url: process.env.ADMIN_API + '/pictureSort/updateGroupName',
    method: 'get',
    params: params
  })
}


// 删除素材库图片分组
export function deletePictureGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/pictureSort/deletePictureGroup',
    method: 'get',
    params: params
  })
}


// 素材库图片上传
export function uploadPicturePath () {
  return process.env.PICTURE_API + '/file/uploadPictureAndCheck'
}

// 查询分组下的图片素材
export function selectPictureList (params) {
  return request({
    url: process.env.ADMIN_API + '/picture/selectPicture',
    method: 'post',
    data: params
  })
}

// 批量删除素材库图片
export function deletePictureBatch (params) {
  return request({
    url: process.env.ADMIN_API + '/picture/deletePictureBatch',
    method: 'post',
    data: params
  })
}


// 重命名素材库图片
export function renamePicture (params) {
  return request({
    url: process.env.ADMIN_API + '/picture/renamePicture',
    method: 'get',
    params: params
  })
}

// 移动素材库图片分组
export function movePictureGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/picture/movePictureGroup',
    method: 'get',
    params: params
  })
}


// 添加视频分组
export function addVideoGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/group/addGroup',
    method: 'post',
    data: params
  })
}

// 删除分组
export function delVideoGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/group/deleteGroup',
    method: 'get',
    params: params
  })
}


// 修改分组
export function updateVideoGroup (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/group/updateGroup',
    method: 'post',
    data: params
  })
}


// 查询分组列表
export function selectGroupList (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/group/selectGroupList',
    method: 'get',
    params: params
  })
}

// 分组拖拽排序
export function exchangeSort (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/group/exchangeSort',
    method: 'get',
    params: params
  })
}

// 上传视频(支持多个)
export function uploadVideoPath () {
  return process.env.PICTURE_API + '/file/uploadVideoAndCheck'
}

// 编辑(修改)视频
export function updateMaterialVideo (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/updateMaterialVideo',
    method: 'post',
    data: params
  })
}

// 根据uid删除视频
export function deleteMaterialVideo (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/deleteMaterialVideo',
    method: 'get',
    params: params
  })
}

// 查询素材库视频
export function selectMaterialVideo (params) {
  return request({
    url: process.env.ADMIN_API + '/media/material/video/selectMaterialVideo',
    method: 'post',
    data: params
  })
}