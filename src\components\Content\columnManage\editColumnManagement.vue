<template>
  <div class="add-column-management">
    <el-form ref="editColumnManagementForm" :rules="editColumnManagementFormRule" :model="editColumnManagementForm" label-position="right" class="editColumnManagementForm" label-width="100px">
      <div class="model-title">
        <span>编辑专栏信息</span>
      </div>
      <el-form-item label="名称" prop="name">
        <el-input v-model="editColumnManagementForm.name"/>
      </el-form-item>
      <el-form-item label="封面" prop="pictureUid">
        <el-upload
          :auto-upload="false"
          :on-change="changeCover"
          :show-file-list="false"
          class="avatar-uploader"
          action="">
          <img v-if="editColumnManagementForm.imageUrl" :src="editColumnManagementForm.imageUrl" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"/>
        </el-upload>
      </el-form-item>
      <el-form-item label="简介" prop="summary">
        <el-input v-model="editColumnManagementForm.summary" type="textarea" rows="6"/>
      </el-form-item>
      <el-form-item label="详情" prop="content">
        <el-input v-model="editColumnManagementForm.content" type="textarea" rows="6"/>
      </el-form-item>
      <!-- <div class="model-title">
                <span>课程内容</span>
            </div> -->
      <!-- <el-form-item label="上传电子证书">
                <el-upload class="upload-demo" action=""
                    :on-change="eBookFileCover"
                    :on-remove="eBookRemove"  multiple :limit="1"
                    :file-list="editColumnManagementForm.eBookFileList">
                    <el-button size="small" type="primary">选择文件</el-button>
                </el-upload>
            </el-form-item> -->
      <!-- <el-form-item label="内容保护设置">
                <el-checkbox
                :true-label="1"
                :false-label="0"
                v-model="editColumnManagementForm.contentSafe">
                    开启水印
                    <span>&nbsp;&nbsp;开启后，学员在浏览课程时会显示用户名及用户ID</span>
                </el-checkbox>
            </el-form-item> -->
      <!-- <el-form-item label="相关资料">
                <el-upload class="upload-demo" action="" :on-change="eBookFileCover" :on-remove="eBookRemove" multiple :limit="1"
                    :file-list="editColumnManagementForm.eBookFileList">
                    <el-button size="small" type="primary">添加资料</el-button>
                </el-upload>
            </el-form-item> -->
      <!-- <div class="model-title">
        <span>所属组合课</span>
      </div> -->
      <!-- <el-form-item label="所属组合课">
        <el-button @click="addCombinationClass">添加视频/训练营</el-button>
      </el-form-item> -->
      <!-- 所选择的文件组合课 -->
      <!-- <div class="column-and-trainList">
        <div v-for="item in columnAndTrainList" :key="item.uid" class="course-item">
          <span>{{ item.name }}</span>
          <el-tag>{{ item.type == 1 ? '训练营' : item.type == 2?'视频' : '专栏' }}</el-tag>
        </div>
      </div> -->
      <div class="model-title">
        <span>商品信息</span>
      </div>
      <el-form-item label="排序规则">
        <el-radio-group v-model="editColumnManagementForm.updateOrder">
          <el-radio :label="0">最新添加的在前</el-radio>
          <el-radio :label="1">最新添加的在后</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="售卖方式">
                <el-checkbox :true-label="1" :false-label="0" v-model="editColumnManagementForm.seleWay">
                    开启水印
                    <span>&nbsp;&nbsp;客户可以通过店铺或链接的方式单独购买该商品</span>
                </el-checkbox>
            </el-form-item> -->
      <!-- <el-form-item label="">
                <el-radio-group v-model="editColumnManagementForm.seleWayType">
                    <el-radio :label="0">免费</el-radio>
                    <el-radio :label="1">付费</el-radio>
                    <el-radio :label="2">加密</el-radio>
                    <el-radio :label="3">指定学院</el-radio>
                </el-radio-group>
            </el-form-item> -->
      <!-- <el-form-item label="" class="express-box">
                <div class="express">
                    <el-form-item label="有效期:">
                        <el-radio-group v-model="editColumnManagementForm.expressType">
                            <el-radio :label="0">长期有效</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
            </el-form-item> -->
      <!-- <el-form-item label="商品分组" class="shop-group">
                <el-select v-model="editColumnManagementForm.shopCateId" placeholder="请选择">
                    <el-option label="区域一" value="shanghai"></el-option>
                </el-select>
                <span class="refresh">刷新</span>
            </el-form-item> -->
      <div class="model-title">
        <span>状态设置</span>
      </div>
      <el-form-item label="上架设置">
        <el-radio-group v-model="editColumnManagementForm.putWayStatus">
          <el-radio :label="1">立即上架</el-radio>
          <el-radio :label="0">暂不上架</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="价格">
        <el-radio-group v-model="editColumnManagementForm.sellingprice">
          <el-input v-model="editColumnManagementForm.sellingprice"/>
        </el-radio-group>
      </el-form-item> -->
      <!--            <el-form-item label="" class="more-set" v-if="editColumnManagementForm.putawaySetType==0">-->
      <!--                <div class="more-set-box">-->
      <!--                    <span>更多设置</span>-->
      <!--                    &lt;!&ndash; <el-radio-group class="more-set-radio" v-model="editColumnManagementForm.putWayStatus">-->
      <!--                        <el-radio :label="0">定时下架</el-radio>-->
      <!--                        <el-radio :label="1">暂不下架</el-radio>-->
      <!--                    </el-radio-group> &ndash;&gt;-->
      <!--                    <el-checkbox :true-label="1" :false-label="0" v-model="editColumnManagementForm.putWayStatus">-->
      <!--                        定时下架-->
      <!--                    </el-checkbox>-->
      <!--                    <el-form-item label="" v-if="(editColumnManagementForm.putWayStatus ==1)">-->
      <!--                        <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="editColumnManagementForm.timingSoldOutTime" type="datetime" placeholder="选择日期时间">-->
      <!--                        </el-date-picker>-->
      <!--                    </el-form-item>-->
      <!--                </div>-->
      <!--            </el-form-item>-->
      <el-form-item label="隐藏设置">
        <el-checkbox :true-label="1" :false-label="0" v-model="editColumnManagementForm.hideSet">
          隐藏
          <span>&nbsp;&nbsp;不可通过搜索或列表进行访问，仅可通过链接方式访问</span>
        </el-checkbox>
      </el-form-item>
      <el-form-item label="销售状态">
        <el-checkbox :true-label="1" :false-label="0" v-model="editColumnManagementForm.saleStatus">
          暂停销售
        </el-checkbox>
      </el-form-item>
      <!-- <el-form-item label="是否推荐">
        <el-radio-group v-model="editColumnManagementForm.level">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否试听">
        <el-radio-group v-model="editColumnManagementForm.hasTrySee">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关联证书" prop="certificateUid">
        <el-select v-model="editColumnManagementForm.certificateUid" size="small" placeholder="关联证书">
          <el-option v-for="item in trainCertificateList" :key="item.uid" :label="item.shortName" :value="item.uid" />
        </el-select>
      </el-form-item> -->
    </el-form>
    <div :style="{ width: sidebar.opened?'calc(100% - 180px)':'calc(100% - 35px)'}" class="btn">
      <el-button @click="cencel">取消</el-button>
      <el-button @click="realAdd">{{ columnManagementThis.oprationFlag==1?'确认添加':'确认编辑' }}</el-button>
    </div>
    <!-- 添加专栏/训练营弹窗 -->
    <template>
      <el-dialog :title="addType==2?'添加视频':'添加训练营'" :visible.sync="columnAndTrainDialogVisible" class="columnAndTrainDialog" width="38%" center>
        <el-tabs v-model="addType" @tab-click="addColumnType">
          <!-- <el-tab-pane label="专栏" name="0">
                        <el-form :inline="true" size="mini" :model="columnModel.searchForm">
                            <el-form-item label="">
                                <el-input v-model="columnModel.searchForm.keyword" placeholder="专栏名称"></el-input>
                            </el-form-item>
                            <el-form-item label="">
                                <el-button @click="columnSearch">搜索</el-button>
                            </el-form-item>
                        </el-form>
                        <el-table v-loadMore="columnLoad" row-key="uid" ref="columnTable" tooltip-effect="dark" @selection-change="columnSelectionChange" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" :data="columnModel.columnList" height="300" style="width: 100%">
                            <el-table-column :reserve-selection="true" type="selection" width="55">
                            </el-table-column>
                            <el-table-column prop="title" label="标题" width="180">
                            </el-table-column>
                            <el-table-column prop="summary" label="简介" width="180">
                            </el-table-column>
                            <el-table-column prop="content" label="详情">
                            </el-table-column>
                            <el-table-column v-if="false" prop="uid" label="主键">
                            </el-table-column>
                        </el-table>
                    </el-tab-pane> -->
          <el-tab-pane label="视频" name="2">
            <el-table
              v-loadMore="videoLoad"
              ref="videoTable"
              :cell-style="{ textAlign: 'center' }"
              :header-cell-style="{ textAlign: 'center' }"
              :data="videoModel.videoList"
              row-key="uid"
              tooltip-effect="dark"
              height="300"
              style="width: 100%"
              @selection-change="videoSelectionChange">
              <el-table-column :reserve-selection="true" type="selection" width="55"/>
              <el-table-column show-overflow-tooltip="" prop="uid" label="uid" width="180"/>
              <el-table-column prop="enrollment" label="封面">
                <template slot-scope="scope">
                  <img :src="scope.row.fileUrl" width="100" alt="">
                </template>
              </el-table-column>
              <el-table-column prop="title" label="名称" width="180"/>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="训练营" name="1">
            <!-- 搜索表单 -->
            <el-form :inline="true" :model="trainingModel.searchForm" size="mini">
              <el-form-item label="">
                <el-input v-model="trainingModel.searchForm.campName" placeholder="训练营名称"/>
              </el-form-item>
              <el-form-item label="">
                <el-button @click="trainingSearch">搜索</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loadMore="trainingLoad" ref="trainingTable" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" :data="trainingModel.trainingList" row-key="uid" tooltip-effect="dark" height="300" style="width: 100%" @selection-change="trainingSelectionChange">
              <el-table-column :reserve-selection="true" type="selection" width="55"/>
              <el-table-column prop="campName" label="名称" width="180"/>
              <el-table-column prop="campPeriodNumber" label="营期数" width="180"/>
              <el-table-column prop="enrollment" label="报名数"/>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
          <el-button @click="columnAndTrainDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="columnAndTrainDialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { videoList } from '@/api/content/video'
import { debounce } from '@/utils/commonUtil'
import { getColumnPageList, editColumn } from '@/api/content/column'
import { getCampList } from '@/api/content/trainingCamp'
import { getToken } from '@/utils/auth'
import { uploadFile } from '@/api/upload'
import { getTrainCertificateList } from '@/api/authentication'
export default {
  inject: ['columnManagementThis'],
  data() {
    return {
      trainCertificateList: [],
      videoModel: {
        total: 0,
        videoPage: 1,
        videoPageSize: 5,
        videoList: []
      },
      videoSelect: [],
      columnSelect: [],
      trainingSelect: [],
      addType: '2',
      columnAndTrainDialogVisible: false,
      columnModel: {
        total: 0,
        columnPage: 1,
        columnPageSize: 5,
        columnList: [],
        searchForm: {
          keyword: ''
        }
      },
      trainingModel: {
        total: 0,
        trainingPage: 1,
        trainingPageSize: 5,
        trainingList: [],
        searchForm: {
          campName: ''
        }
      },
      editColumnManagementForm: {
        sellingprice: '',
        level: 0,
        certificateUid: '',
        uid: '',
        updateOrder: 0,
        saleStatus: 0,
        imageUrl: '',
        pictureUid: '',
        eBookFileList: [],
        contentSafe: 0,
        summary: '',
        content: '',
        seleWay: 0,
        seleWayType: 0,
        expressType: 0,
        putWayStatus: 0,
        putawaySetType: 1,
        moreSetStatus: 0,
        hideSet: 0,
        hasTrySee: 0,
        name: '',
        timingSoldOutTime: '',
        campIds: '',
        bigColumnIds: ''
      },
      editColumnManagementFormRule: {
        uid: [{ required: true, message: 'uid不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '请输入训练营名称', trigger: 'blur' }],
        pictureUid: [{ required: true, message: '请上传封面图片', trigger: 'blur' }],
        summary: [{ required: true, message: '请输入训练营简介', trigger: 'blur' }],
        content: [{ required: true, message: '请输入训练营详情', trigger: 'blur' }]
      }
    }
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar
    },
    ...mapGetters(['columnInfo']),
    // 所属组合课列表
    columnAndTrainList: ({ columnSelect, trainingSelect, videoSelect }) => {
      console.log(columnSelect, trainingSelect, videoSelect)
      return [...columnSelect, ...trainingSelect, ...videoSelect]
    }
  },
  watch: {
    columnAndTrainList(newVal, oldVal) {
      this.editColumnManagementForm.bigColumnIds = newVal.filter(item => {
        return item.type == 3
      }).map(item => {
        return item.uid
      }).join(',')
      this.editColumnManagementForm.campIds = newVal.filter(item => {
        return item.type == 1
      }).map(item => {
        return item.uid
      }).join(',')
    }
  },
  async mounted() {
    this.dataReSet()
    this.getCertificateList()
    await this.intiColumnData()
    await this.initTrainingData()
    await this.initVideoInfo()
    await this.initEditorInfo()
    // this.initUnFindData();
  },
  methods: {
    // 关联认证列表
    getCertificateList() {
      getTrainCertificateList().then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.trainCertificateList = res.data
        }
      })
    },
    initUnFindData() {
      const unFindDatas = []
      const columnIds = this.columnModel.columnList.map(item => item.uid)
      const videoIds = this.videoModel.videoList.map(item => item.uid)
      const trainingIds = this.trainingModel.trainingList.map(item => item.uid)
      unFindDatas.push(
        ...this.trainingSelect.filter(item => {
          return !trainingIds.includes(item.contentUid)
        })
      )
      unFindDatas.push(
        ...this.columnSelect.filter(item => {
          return !columnIds.includes(item.contentUid)
        })
      )
      unFindDatas.push(
        ...this.videoSelect.filter(item => {
          return !videoIds.includes(item.contentUid)
        })
      )
      console.log('筛选未加载的数据', unFindDatas)
    },
    // 滑到底部进行加载
    columnLoad() {
      debounce(async() => {
        if (this.columnModel.columnList.length >= this.columnModel.total) return
        this.columnModel.columnPage++
        const params = {
          currentPage: this.columnModel.columnPage,
          pageSize: this.columnModel.columnPageSize,
          keyword: this.columnModel.searchForm.keyword
        }
        const result = await getColumnPageList(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.columnModel.total = result.data.total
          this.columnModel.columnList.push(...result.data.records)
        }
      })()
    },
    videoLoad() {
      debounce(async() => {
        if (this.videoModel.videoList.length >= this.videoModel.total) return
        this.videoModel.videoPage++
        const params = {
          currentPage: this.videoModel.videoPage,
          pageSize: this.videoModel.videoPageSize,
          keyword: ''
        }
        const result = await getColumnPageList(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.videoModel.total = result.data.total
          this.videoModel.videoList.push(...result.data.records)
        }
      })()
    },
    trainingLoad() {
      debounce(async() => {
        if (this.trainingModel.trainingList.length >= this.trainingModel.total) return
        this.trainingModel.columnPage++
        const params = {
          currentPage: this.trainingModel.trainingPage,
          pageSize: this.trainingModel.trainingPageSize,
          keyword: this.trainingModel.searchForm.campName
        }
        const result = await getColumnPageList(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.trainingModel.total = result.data.total
          this.trainingModel.trainingList.push(...result.data.records)
        }
      })()
    },
    // 视频
    videoSelectionChange(data) {
      if (this.addType == 0) return
      console.log(data)
      this.videoSelect = data.map(item => {
        return {
          name: item.title,
          ...item,
          type: 2
        }
      })
      console.log('this.videoSelect', this.videoSelect)
    },
    // 加载视频库信息
    async initVideoInfo() {
      const params = {
        pageSize: this.videoModel.videoPageSize,
        currentPage: this.videoModel.videoPage
      }
      const result = await videoList(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.videoModel.total = result.data.total
        this.videoModel.videoList = result.data.records
      } else {
        this.$commonUtil.message.error(res.message)
      }
    },
    // 初始化编辑信息
    initEditorInfo() {
      // 编辑操作
      if (this.columnManagementThis.oprationFlag == 2) {
        this.editColumnManagementForm.level = this.columnInfo.level
        this.editColumnManagementForm.sellingprice = this.columnInfo.sellingprice
        this.editColumnManagementForm.certificateUid = this.columnInfo.certificateUid
        this.editColumnManagementForm.name = this.columnInfo.title
        this.editColumnManagementForm.uid = this.columnInfo.uid
        this.editColumnManagementForm.summary = this.columnInfo.summary
        this.editColumnManagementForm.content = this.columnInfo.content
        this.editColumnManagementForm.imageUrl = this.columnInfo.coverUrl
        this.editColumnManagementForm.pictureUid = this.columnInfo.fileUid
        this.editColumnManagementForm.updateOrder = this.columnInfo.updateOrder
        this.editColumnManagementForm.bigColumnIds = this.columnInfo.bigColumn
        this.editColumnManagementForm.campIds = this.columnInfo.camp
        this.editColumnManagementForm.stopSelling = this.columnInfo.stopSelling
        this.editColumnManagementForm.hideSet = this.columnInfo.hide
        this.editColumnManagementForm.putWayStatus = this.columnInfo.status
        this.editColumnManagementForm.timingSoldOutTime = this.columnInfo.timerDownTime
        this.$set(this.editColumnManagementForm, 'hasTrySee', this.columnInfo.hasTrySee)

        const trainModuleContentList = this.columnInfo.trainModuleContentList
        // 筛选
        trainModuleContentList.forEach(item => {
          // 训练营
          if (item.contentType == 1) {
            this.trainingSelect.push({ ...item, uid: item.contentUid, type: item.contentType })
          } else if (item.contentType == 2) {
            // 视频
            this.videoSelect.push({ ...item, uid: item.contentUid, type: item.contentType })
          } else if (item.contentType == 3) {
            // 专栏
            this.columnSelect.push({ ...item, uid: item.contentUid, type: item.contentType })
          }
        })
      }
    },
    columnSelectionChange(data) {
      this.columnSelect = data.map(item => {
        return {
          name: item.title,
          ...item,
          type: 3
        }
      })
    },
    trainingSelectionChange(data) {
      if (this.addType == 0) return
      console.log('data.filter(item=> item)', data.filter(item => item))
      this.trainingSelect = data.map(item => {
        return {
          name: item.campName,
          ...item,
          type: 1
        }
      })
    },
    async columnSearch() {
      this.columnModel.columnPage = 1
      await this.intiColumnData()
      this.showSelected()
    },
    async trainingSearch() {
      this.trainingModel.columnPage = 1
      await this.initTrainingData()
      this.showSelected()
    },
    addColumnType(tab, event) {
      // 专栏
      if (tab.name == 0) {
        console.log('专栏')
        this.showSelected()
      } else if (tab.name == 1) {
        // 训练营
        console.log('训练营')
        this.showSelected()
      } else if (tab.name == 2) {
        // 视频
        console.log('视频')
        this.showSelected()
      }
    },
    showSelected() {
      this.columnAndTrainList.forEach(item => {
        this.$nextTick(() => {
          // 训练营
          if (item.contentType == 1 && this.addType == 1) {
            const targetObject = this.trainingModel.trainingList.find(targetItem => {
              return targetItem.uid === item.uid
            })

            if (targetObject) {
              this.$refs.trainingTable.toggleRowSelection(targetObject, true)
            } else {
              this.$refs.trainingTable.toggleRowSelection(item, true)
            }
          }
          // 视频
          if (item.contentType == 2 && this.addType == 2) {
            // 勾选
            const targetObject = this.videoModel.videoList.find(targetItem => {
              return targetItem.uid === item.uid
            })

            if (targetObject) {
              this.$refs.videoTable.toggleRowSelection(targetObject, true)
            } else {
              this.$refs.videoTable.toggleRowSelection(item, true)
            }
          }
          // 专栏
          if (item.contentType == 3 && this.addType == 0) {
            console.log('this.$refs.videoTable', this.$refs.videoTable)
            // 勾选
            const targetObject = this.columnModel.columnList.find(targetItem => {
              console.log(item.contentUid, targetItem.uid)
              return targetItem.uid === item.uid
            })
            if (targetObject) {
              this.$refs.columnTable.toggleRowSelection(targetObject, true)
            } else {
              this.$refs.columnTable.toggleRowSelection(item, true)
            }
          }
        })
      })
    },
    cencel() {
      this.columnManagementThis.flag = 2
    },
    async realAdd() {
      const trainModuleContentList = this.columnAndTrainList.map(item => {
        return {
          contentType: item.type,
          contentUid: item.uid,
          uid: '',
          name: item.name
        }
      })
      const params = {
        level: this.editColumnManagementForm.level,
        sellingPrice: this.editColumnManagementForm.sellingprice ? this.editColumnManagementForm.sellingprice : '',
        certificateUid: this.editColumnManagementForm.certificateUid,
        uid: this.editColumnManagementForm.uid,
        title: this.editColumnManagementForm.name,
        summary: this.editColumnManagementForm.summary,
        fileUid: this.editColumnManagementForm.pictureUid,
        content: this.editColumnManagementForm.content,
        updateOrder: this.editColumnManagementForm.updateOrder,
        bigColumn: this.editColumnManagementForm.bigColumnIds,
        camp: this.editColumnManagementForm.campIds,
        stopSelling: this.editColumnManagementForm.stopSelling,
        hide: this.editColumnManagementForm.hideSet,
        status: this.editColumnManagementForm.putWayStatus,
        timerDownTime: this.editColumnManagementForm.timingSoldOutTime,
        hasTrySee: this.editColumnManagementForm.hasTrySee,
        trainModuleContentList
      }
      this.$refs['editColumnManagementForm'].validate(async(valid) => {
        if (valid) {
          const result = await editColumn(params)
          if (result.code === this.$ECode.SUCCESS) {
            this.columnManagementThis.flag = 2
            this.$message.success('编辑专栏成功')
            this.intiColumnData()
          } else {
            this.$message.warning(result.message)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async intiColumnData() {
      const params = {
        currentPage: this.columnModel.columnPage,
        pageSize: this.columnModel.columnPageSize,
        keyword: this.columnModel.searchForm.keyword
      }
      const result = await getColumnPageList(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.columnModel.total = result.data.total
        this.columnModel.columnList = result.data.records
      }
    },
    async initTrainingData() {
      const params = {
        currentPage: this.trainingModel.columnPage,
        pageSize: this.trainingModel.columnPageSize,
        keyword: this.trainingModel.searchForm.campName
      }
      const result = await getCampList(params)
      if (result.code == 200) {
        this.trainingModel.total = result.data.total
        this.trainingModel.trainingList = result.data.records
      }
    },
    // 数据初始化
    dataReSet() {
      this.columnModel.total = 0
      this.columnModel.columnPage = 1
      this.columnModel.columnPageSize = 5
      this.trainingModel.total = 0
      this.trainingModel.columnPage = 1
      this.trainingModel.columnPageSize = 5
      this.trainingModel.searchForm.campName = ''
      this.columnModel.searchForm.keyword = ''
      this.editColumnManagementForm = {
        sellingprice: '',
        level: 0,
        certificateUid: '',
        uid: '',
        updateOrder: 0,
        saleStatus: 0,
        imageUrl: '',
        pictureUid: '',
        eBookFileList: [],
        contentSafe: 0,
        summary: '',
        content: '',
        seleWay: 0,
        seleWayType: 0,
        expressType: 0,
        putWayStatus: 0,
        putawaySetType: 1,
        moreSetStatus: 0,
        hideSet: 0,
        name: '',
        timingSoldOutTime: '',
        campIds: '',
        bigColumnIds: ''
      }
    },
    addCombinationClass() {
      this.columnAndTrainDialogVisible = true
      this.showSelected()
    },
    eBookRemove() {

    },
    eBookFileCover(file, fileList) {

    },
    // 封面上传
    changeCover(file, fileList) {
      console.warn(file)
      const whiteList = ['image/jpeg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.warning('图片大小不能超过2M')
        return
      }
      if (!whiteList.includes(file.raw.type)) {
        this.$message.warning('请上传指定文件格式')
        return
      }
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('token', getToken())
      formData.append('source', 'picture')
      formData.append('projectName', 'blog')
      formData.append('sortName', 'admin')
      formData.append('file', file.raw)
      uploadFile(formData).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.editColumnManagementForm.pictureUid = res.data[0].uid
          this.editColumnManagementForm.imageUrl = res.data[0].url
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    border: 1px dashed #d9d9d9;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
.add-column-management{
    padding: 20px 20px 60px 20px;
    .columnAndTrainDialog{
        /deep/ .el-dialog__body{
            padding: 0 25px;
        }
    }
    .btn{
        transition: all .5s;
        z-index: 99;
        width: calc(100% - 180px);
        right: 0;
        position: fixed;
        bottom: 0;
        background: white;
        box-shadow: 0 0 10px rgb(209, 209, 209);
        align-items: center;
        display: flex;
        justify-content: center;
        height: 60px;
    }
    .editColumnManagementForm{
        .column-and-trainList{
            .course-item{
                justify-content: space-between;
                border-radius: 5px;
                display: flex;
                align-items: center;
                margin: 10px 0;
                width: 400px;
                height: 50px;
                padding: 20px;
                background: #f7f5f5;
            }
        }
        .more-set{
            .more-set-box{
                width:  700px;
                height: 180px;
                padding: 20px 0 20px 20px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                background: #f5f5f5;
                span {
                    color: rgb(150, 150, 150);
                    font-size: 18px;
                    margin-bottom: 10px;
                }
                .more-set-radio{
                    display: flex;
                    flex-direction: column;
                    .el-radio{
                        margin-bottom: 15px;
                    }
                }
            }
        }
        .shop-group{
            .refresh{
                cursor: pointer;
                color: #409EFF;
                font-size: 14px;
                margin-left:15px;
            }
        }
        .express-box{
            .express{
                width: 450px;
                height: 80px;
                display: flex;
                align-items: center;
                background: #f5f5f5;
            }
        }
        .model-title {
            font-size: 19px;
            margin-bottom: 40px;
            color: #525252;
        }
    }
}
</style>
