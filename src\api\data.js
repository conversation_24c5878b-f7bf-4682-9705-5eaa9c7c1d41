import request from '@/utils/request'
// 数据总览
export function getIndexData(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/count/getIndexData',
    method: 'get',
    params: params
  })
}

// 创作趋势数据
export function getTrend(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/count/getTrend',
    method: 'post',
    data: params
  })
}

// 文章贡献榜
export function getTopList(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/count/topList',
    method: 'post',
    data: params
  })
}

// 技术类文章数据浏览量排行分页列表

export function getblogTopList(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/count/blog/top/pageList',
    method: 'post',
    data: params
  })
}

// 批量标记或取消标记技术类文章
export function technologyTag(params) {
  return request({
      url: process.env.ADMIN_API + '/blog/count/tag',
      method: 'post',
      data: params
  })
}
