<template>
    <div class="addCamp-period-container">
        <!-- 创建完成 -->
        <template v-if="finish">
            <div class="finish">
                <i class="el-icon-success"></i>
                <p class="title">{{ parentThis.oprationFlag == 1 ? '训练营营期创建成功!' :'训练营营期修改成功!' }}</p>
                <!-- <p class="desc">为了给参与者带来更好的体验，建议您进行结课证书设置</p> -->
                <el-button @click="goBackList">返回列表</el-button>
            </div>
        </template>
        <template v-else>
            <!-- 步骤条 -->
            <div class="step">
                <el-steps :active="active" simple>
                    <el-step v-for="(item,index) in stepArr" :key="index" :title="item" :icon="index+1==active?'el-icon-edit':'el-icon-tickets'"></el-step>
                </el-steps>
            </div>
            <!-- form表单区域 -->
            <div class="form-box">
                <el-form :rules="addCampPeriodRule" class="form" ref="addCampPeriodForm" label-position="right" label-width="100px" :model="addCampPeriodForm">
                    <!-- 营销基础信息 -->
                    <template v-if="active==1">
                        <div class="base-info">
                            <span>基本信息</span>
                        </div>
                        <el-form-item label="营期名称" prop="campPeriodName">
                            <el-input v-model="addCampPeriodForm.campPeriodName"></el-input>
                        </el-form-item>
                        <el-form-item label="营期简介" prop="campPeriodDesc">
                            <el-input rows="6" type="textarea" v-model="addCampPeriodForm.campPeriodDesc"></el-input>
                        </el-form-item>
                        <el-form-item label="营期封面" prop="campPeriodCover">
                            <el-upload
                            :auto-upload="false"
                            :on-change="changeCover"
                            :show-file-list="false"
                            class="avatar-uploader"
                            action=""
                            >
                            <img v-if="addCampPeriodForm.campPeriodCover" :src="addCampPeriodForm.campPeriodCover" class="avatar">
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="营期详情" prop="campPeriodDetail">
                            <el-input rows="6" type="textarea" v-model="addCampPeriodForm.campPeriodDetail"></el-input>
                        </el-form-item>
                        <div class="open-class-info">
                            <span>开课信息</span>
                        </div>
                        <el-form-item label="招生时间" prop="admissionsTime">
                            <el-date-picker
                            v-model="addCampPeriodForm.admissionsTime"
                            type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="开课时间" prop="curriculumTime">
                            <el-date-picker
                            v-model="addCampPeriodForm.curriculumTime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                        <div class="mode-info">
                            <span>模式设置</span>
                        </div>
                        <el-form-item label="目录解锁模式">
                            <el-radio-group v-model="addCampPeriodForm.unlockingaDirectoryMode">
                                <el-radio :label="1">自由模式</el-radio>
                                <el-radio :label="2">闯关解锁模式</el-radio>
                                <el-radio :label="3">日期解锁</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item class="date-unlock" label="" v-if="addCampPeriodForm.unlockingaDirectoryMode==3">
                            <div class="row-select">
                                <span>每</span>
                                <el-input class="date-unlock-everyday" v-model="addCampPeriodForm.dateUnlockEveryday"></el-input>
                                <el-select class="date-unlock-every-unit" v-model="addCampPeriodForm.dateUnlockEveryUnit" placeholder="请选择时间单位">
                                    <el-option label="日" value="日"></el-option>
                                    <el-option label="周" value="周"></el-option>
                                    <el-option label="月" value="月"></el-option>
                                </el-select>
                                <span>解锁一</span>
                                <el-select class="date-unloc-type" v-model="addCampPeriodForm.dateUnlocType" placeholder="请选择更新类型">
                                    <el-option label="节" :value="1"></el-option>
                                </el-select>
                            </div>
                            <div class="row-time">
                                <span>解锁时间</span>
                                <el-time-picker
                                class="unlock-time"
                                v-model="addCampPeriodForm.dateUnlockTime"
                                :picker-options="{
                                    selectableRange: '18:30:00 - 20:30:00'
                                }"
                                placeholder="解锁时间">
                            </el-time-picker>
                            </div>
                        </el-form-item>
                        <el-form-item label="开课提醒">
                            <el-radio-group v-model="addCampPeriodForm.lessonStartReminder">
                                <el-radio :label="1">开启&nbsp;&nbsp;&nbsp;&nbsp;开启后，第一次开课时和课程解锁时会向学员发送服务号模板消息。</el-radio>
                                <el-radio :label="2">关闭</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </template>
                    <!-- 营销基础信息 -->
                    <template v-if="active==2">
                        <div class="course-catalogue">
                            <span>课程目录</span>
                        </div>
                        <div class="catalogue-box">
                            <ul>
                                <li class="first-level-directory"
                                :style="item.spread?{
                                    height:'auto'
                                }:{
                                    height:'50px'
                                }"
                                v-for="(item, firstIndex) in addCampPeriodForm.trainModuleContentList" :key="firstIndex">
                                    <div @click="firstLevelDirectoryChange(item)" class="first-level-box">
                                        <div class="left">
                                            <i :style="item.spread?{}:{
                                                transform: 'rotate(-90deg)'
                                            }" class="el-icon-arrow-down"></i>
                                            {{item.name}}
                                        </div>
                                        <div class="right">
                                            （{{ item.upTime }}开课） 共{{item.trainModuleContentList.length}}项任务
                                        </div>
                                    </div>
                                    <ul>
                                        <div v-for="(twoLevel, twoIndex) in item.trainModuleContentList" :key="twoLevel.contentUid">
                                            <li class="two-level-directory">
                                                <span>{{twoLevel.name}}</span>
                                                <div class="operation">
                                                    <i class="el-icon-rank"></i>
                                                    <i class="el-icon-edit" @click="editorTask(twoLevel)"></i>
                                                    <i class="el-icon-delete" @click="deleteTask(item,index)"></i>
                                                </div>
                                            </li>
                                            <div class="task-box" v-if="twoLevel.isEditor">
                                                <el-form-item class="task-name" label="任务名称">
                                                    <el-input class="task-input" v-model="twoLevel.name" placeholder="请输入任务名称" maxlength="16"></el-input>
                                                </el-form-item>
                                                <!-- 知识商品信息 -->
                                                <el-form-item class="task-name" label="" v-if="twoLevel.contentUid">
                                                    <h3>您当前所选择的内容信息</h3>
                                                    <el-tag>内容ID：{{twoLevel.contentUid}}</el-tag>
                                                    <el-button @click="reSelect(twoLevel, firstIndex, twoIndex)">重新选择</el-button>
                                                    <!-- <div :style="{
                                                        display:'flex',
                                                        alignItem: 'center',
                                                    }">
                                                        <span>视频封面：</span>
                                                        <el-image
                                                        style="width: 100px; height: 100px"
                                                        :preview-src-list="[twoLevel.fileUrl]"
                                                        :src="twoLevel.fileUrl"
                                                        fit="fill"></el-image>
                                                    </div> -->
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <li class="two-level-directory add-study-task" @click="addStudyTask(item)">
                                            <i class="el-icon-plus"></i>
                                            <span>添加学习任务</span>
                                        </li>
                                    </ul>
                                </li>
                                <li class="first-level-directory add-first-level" @click="addFirstSection">
                                    <i class="el-icon-plus"></i>
                                    <span>新增一节</span>
                                </li>
                            </ul>
                        </div>
                    </template>
                    <!-- 售卖信息 -->
                    <template v-if="active==3">
                        <div class="shop-info">
                            <span>商品信息</span>
                        </div>
                        <el-form-item class="mode-of-sale" label="售卖方式">
                            <div class="tip">
                                <span>支持单独售卖</span>
                                <span>客户可以通过店铺或链接的方式单独购买该商品</span>
                            </div>
                            <div class="sale-type">
                                <el-radio-group v-model="addCampPeriodForm.saleType">
                                    <el-radio :label="1">免费</el-radio>
                                    <el-radio :label="2">付费</el-radio>
                                    <el-radio :label="3">加密</el-radio>
                                    <el-radio :label="4">指定学员</el-radio>
                                </el-radio-group>
                            </div>
                        </el-form-item>
                        <el-form-item v-if="addCampPeriodForm.saleType==3" class="encrypted-password-item" prop="encryptedPassword" label="加密密码">
                            <el-input class="encrypted-password" v-model="addCampPeriodForm.encryptedPassword" placeholder="请输入加密密码"></el-input>
                        </el-form-item>
                        <div class="shelf-setting">
                            <span>上架设置</span>
                        </div>
                        <el-form-item label="上架设置">
                            <el-radio-group v-model="addCampPeriodForm.shelfType">
                                <el-radio :label="1">立即上架</el-radio>
                                <el-radio :label="2">定时上架</el-radio>
                                <!-- <el-radio :label="3">暂不上架</el-radio> -->
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="" v-if="addCampPeriodForm.shelfType==2">
                            <el-date-picker
                            value-format="yyyy-MM-dd HH:mm:ss"
                            v-model="addCampPeriodForm.timingShelfTime"
                            type="datetime"
                            placeholder="上架时间">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-radio-group v-model="addCampPeriodForm.status">
                                <el-radio :label="0">停用</el-radio>
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="2">隐藏</el-radio>
                                <!-- <el-radio :label="3">暂不上架</el-radio> -->
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item class="more-seting-item" label="">
                            <span class="more-seting"
                            @click="changeShowMoreSeting">
                                更多设置
                                <i :style="{transform:moreSettingFlag?'':'rotate(-180deg)'}" class="el-icon-caret-bottom"></i>
                            </span>
                            <div
                            :style="moreSettingFlag?{
                                height:'max-content'
                            }:{
                                height:0
                            }"
                            class="more-seting-status">
                                <el-checkbox-group v-model="addCampPeriodForm.shelfStatus">
                                    <el-radio-group v-model="addCampPeriodForm.shelfStatus">
                                        <el-radio :label="0">
                                            停售&nbsp;&nbsp;
                                            上架的商品设置停售后，在店铺内会显示，但停止售卖
                                        </el-radio>
                                        <el-radio :label="1">
                                            在售&nbsp;&nbsp;
                                            上架的商品设置隐藏后，在店铺内不显示，但可以通过链接的方式访问
                                        </el-radio>
                                    </el-radio-group>
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                    </template>
                </el-form>
            </div>

            <!-- 取消、上一步、下一步 -->
            <div class="submit-btn" :style="[
        sidebar.opened ? { width: 'calc(100% - 180px)' }:{width: 'calc(100% - 35px)' } ]">
                <el-button type="info" v-if="active==1" @click="cancel">取消</el-button>
                <el-button v-if="active!=1" @click="pre">上一步</el-button>
                <el-button v-if="active!=3" type="primary" @click="next">下一步</el-button>
                <el-button v-if="active==3" type="primary" @click="complete">完成</el-button>
            </div>
        </template>
        <!-- 新增一节弹窗 -->
        <template>
            <el-dialog
            title="新增一节"
            :visible.sync="addNewSectionDialogVisible"
            width="30%"
            center>
            <el-form :rules="addNewSectionFormRule" label-position="right" label-width="80px" ref="addNewSectionForm" :model="addNewSectionForm">
                <el-form-item label="章节名称" prop="sectionName">
                    <el-input v-model="addNewSectionForm.sectionName"></el-input>
                </el-form-item>
                <el-form-item label="开课时间" prop="upTime">
                    <el-date-picker
                    v-model="addNewSectionForm.upTime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addNewSectionDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="realAddNewSection">确 定</el-button>
            </span>
            </el-dialog>
        </template>
        <!-- 添加学习任务弹窗 -->
        <el-dialog
            @closed="addStudyTaskClose"
            :title="oprationFlag == 1?'添加学习任务':'修改学习任务'"
            :visible.sync="addStudyTaskDialogVisible"
            width="30%"
            center>
            <!-- 视频库选择弹窗 -->
            <el-dialog
            width="40%"
            title="视频库"
            :visible.sync="selectVideoDialogVisible"
            append-to-body>
                <!-- 视频库组件 -->
                <videoLibrary @cancelSelectVideo="cancelSelectVideo" @realSelectVideo="realSelectVideo" :currentVideoInfo.sync="addStudyTaskForm.currentSectionContentData"></videoLibrary>
            </el-dialog>
            <el-form :rules="addStudyTaskFormRule" ref="addStudyTaskForm" label-position="right" label-width="80px" :model="addStudyTaskForm">
                <el-form-item label="任务名称" prop="taskName">
                    <el-input v-model="addStudyTaskForm.taskName"></el-input>
                </el-form-item>
                <el-form-item label="任务类型">
                    <el-radio v-model="addStudyTaskForm.taskType" :label="1">知识商品（添加图文/音频/商品/直播/电纸书）</el-radio>
                    <br/>
                    <el-radio v-model="addStudyTaskForm.taskType" :label="2">助学工具（可添加打卡/考试/表单）</el-radio>
                </el-form-item>
                <el-form-item label="" v-if="(addStudyTaskForm.taskType == 1 && panelNull(addStudyTaskForm.currentSectionContentData).uid)">
                    <div :style="{
                        marginTop:'-34px'
                    }">
                        <h3>您当前所选择的内容信息</h3>
                        <el-tag>内容ID：{{ panelNull(addStudyTaskForm.currentSectionContentData).uid }}</el-tag>
                        <!-- <div :style="{
                            display:'flex',
                            alignItem: 'center',
                        }">
                            <span>视频封面：</span>
                            <el-image
                            style="width: 100px; height: 100px"
                            :preview-src-list="[addStudyTaskForm.currentSectionContentData.fileUrl]"
                            :src="addStudyTaskForm.currentSectionContentData.fileUrl"
                            fit="fill"></el-image>
                        </div> -->
                    </div>
                </el-form-item>
                <el-form-item prop="currentSectionContentData" label="">
                    <el-button @click="videoSelect">{{ panelNull(addStudyTaskForm.currentSectionContentData).uid ?'重新选择内容':'内容选择'}}</el-button>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="addStudyTaskDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="realAddStudyTask">{{(oprationFlag==1?'确认添加':'确认修改')}}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import videoLibrary from '@/components/Content/videoLibrary'
import { getToken } from '@/utils/auth'
import { uploadFile } from '@/api/upload'
import { addCampPeriod, editorCampPeriod } from '@/api/content/trainingCamp';
export default {
    inject: ['parentThis'],
    components:{
        videoLibrary
    },
    data(){
        return {
            //1 添加 2编辑
            oprationFlag:1,
            addNewSectionDialogVisible:false,
            addStudyTaskDialogVisible:false,
            selectVideoDialogVisible:false,
            finish:false,
            moreSettingFlag:true,
            stepArr:["1 营销基础信息","2 营销课程","3 售卖信息"],
            active:1,
            addStudyTaskForm:{
                videoList:[],
                currentSectionData:{},
                taskType:1,
                taskName:"",
                currentSectionContentData:null
            },
            addNewSectionFormRule: { 
                sectionName: [{ required: true, message: '请输入章节名称', trigger: 'blur' }],
                upTime: [{ required: true, message: '请选择开课时间', trigger: 'blur' }]
            },
            addStudyTaskFormRule: {
                taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
                currentSectionContentData: [{ required: true, message: '请选择关联任务', trigger: 'blur' }]
            },
            addNewSectionForm:{
                sectionName:"",
                upTime:""
            },
            addCampPeriodForm: {
                status:1,
                trainModuleContentList:[
                ],
                campPeriodName:"",
                campPeriodDesc:"",
                campPeriodCover:"",
                campPeriodDetail:"",
                admissionsTime:[],
                curriculumTime:[],
                unlockingaDirectoryMode:1,
                lessonStartReminder:1,
                dateUnlockEveryday:"",
                dateUnlockTime:"",
                dateUnlockEveryUnit:1,
                dateUnlocType:1,
                pictureUid:"",
                saleType:1,
                encryptedPassword:"",
                shelfType:1,
                timingShelfTime:"",
                shelfStatus:''
            },
            addCampPeriodRule: {
                campPeriodName: [{ required: true, message: '请输入营期名称', trigger: 'blur' }],
                campPeriodDetail: [{ required: true, message: '请输入营期详情', trigger: 'blur' }],
                campPeriodDesc: [{ required: true, message: '请输入营期简介', trigger: 'blur' }],
                campPeriodCover: [{ required: true, message: '请上传营期封面', trigger: 'blur' }],
                admissionsTime: [{ required: true, message: '请选择招生时间', trigger: 'blur' }],
                curriculumTime: [{ required: true, message: '请选择开课时间', trigger: 'blur' }],
                encryptedPassword: [{ required: true, message: '请输入加密密码', trigger: 'blur' }],
            },
            currentEditorPositionInfo:[]
        }
    },
    mounted() {
        // 初始化编辑信息
        this.initEditorInfo();
    },
    computed: {
        sidebar() {
            return this.$store.state.app.sidebar;
        },
        ...mapGetters(['trainingCampInfo','campPeriodInfo'])
    },
    methods: {
        panelNull(data) {
            return data ? data : {};
        },
        // 重新选择内容
        reSelect(data, firstIndex, twoIndex) {
            // 编辑当前项信息
            this.currentEditorPositionInfo = [firstIndex, twoIndex]; 
            // 赋值内容id
            this.addStudyTaskForm.currentSectionContentData = { uid: data.contentUid };
            // 编辑标识
            this.oprationFlag = 2;
            this.addStudyTaskDialogVisible = true;
            this.addStudyTaskForm.taskName = data.name;
            this.addStudyTaskForm.taskType = data.contentType;
            console.log(data);
        },
        initEditorInfo() {
            // 编辑营期
            if (this.parentThis.oprationFlag == 2) { 
                this.addCampPeriodForm.campPeriodCover = this.campPeriodInfo.picturePath;
                this.addCampPeriodForm.campPeriodName = this.campPeriodInfo.campPeriodName;
                this.trainingCampInfo.uid = this.campPeriodInfo.campUid;
                this.addCampPeriodForm.campPeriodDesc = this.campPeriodInfo.summary;
                this.addCampPeriodForm.campPeriodDetail = this.campPeriodInfo.content;
                this.addCampPeriodForm.pictureUid = this.campPeriodInfo.pictureUid;
                this.addCampPeriodForm.admissionsTime = [this.campPeriodInfo.registrationEndTime, this.campPeriodInfo.registrationStartTime];
                this.addCampPeriodForm.curriculumTime = [this.campPeriodInfo.trainingStartTime, this.campPeriodInfo.trainingEndTime];
                this.addCampPeriodForm.unlockingaDirectoryMode = this.campPeriodInfo.type;
                this.addCampPeriodForm.dateUnlockTime = this.campPeriodInfo.unlockTime;
                this.addCampPeriodForm.dateUnlockEveryday = this.campPeriodInfo.unlockNumber;
                this.addCampPeriodForm.dateUnlockEveryUnit = this.campPeriodInfo.unlockUnit;
                this.addCampPeriodForm.saleType = this.campPeriodInfo.saleMethod;
                this.addCampPeriodForm.encryptedPassword = this.campPeriodInfo.password;
                this.addCampPeriodForm.shelfType = this.campPeriodInfo.releaseSet;
                this.addCampPeriodForm.timingShelfTime = this.campPeriodInfo.releaseTime;
                this.addCampPeriodForm.shelfStatus = this.campPeriodInfo.saleStatus;
                this.addCampPeriodForm.status = this.campPeriodInfo.status;
                this.addCampPeriodForm.trainModuleContentList = this.campPeriodInfo.trainModuleContentList.map(item => { 
                    let everyItem={
                        name: item.name,
                        sort: 1,
                        spread: false,
                        upTime: item.upTime,
                        trainModuleContentList:[]
                    }
                    everyItem.trainModuleContentList = item.trainModuleContentList.map(item => {
                        return {
                            isEditor: false,
                            contentType: item.contentType,
                            name: item.name,
                            contentUid: item.contentUid,
                            sort:1
                        }
                    });
                    return everyItem;
                });
                console.log('营期详情信息', this.campPeriodInfo.trainModuleContentList);
            }
        },
        // 取消视频选择
        cancelSelectVideo(){
            this.selectVideoDialogVisible=false;
        },
        // 确定视频选择
        realSelectVideo() {
            this.selectVideoDialogVisible=false;
        },
        // 视频库选择
        videoSelect(){
            this.selectVideoDialogVisible=true;
        },
        // 删除任务
        deleteTask(data,index){
            data.trainModuleContentList.splice(index,1);
        },
        // 设置所有任务为未编辑状态
        setAllUnEditor(){
            this.addCampPeriodForm.trainModuleContentList.forEach(first=>{
                first.trainModuleContentList.forEach(two=>{
                    two.isEditor=false;
                });
            })
        },
        // 编辑任务
        editorTask(data){
            this.setAllUnEditor();
            data.isEditor=true;
        },
        // 确定添加学习任务
        realAddStudyTask() {
            if (this.addStudyTaskForm.taskType == 2) { 
                this.$message.warning('正在加急开发中');
                return;
            }
            this.$refs['addStudyTaskForm'].validate(async (valid) => {
                if (valid) {
                    // 编辑
                    if (this.oprationFlag == 2) {
                        // 根据index修改对应对象的内容
                        let [first, end] = this.currentEditorPositionInfo;
                        console.warn("当前对象", this.addCampPeriodForm.trainModuleContentList[first].trainModuleContentList[end]);
                        this.addCampPeriodForm.trainModuleContentList[first].trainModuleContentList[end].name = this.addStudyTaskForm.taskName;
                        this.addCampPeriodForm.trainModuleContentList[first].trainModuleContentList[end].contentType = this.addStudyTaskForm.taskType;
                        this.addCampPeriodForm.trainModuleContentList[first].trainModuleContentList[end];
                    } else {
                        //添加
                        this.addStudyTaskForm.currentSectionData.trainModuleContentList.push({
                            isEditor: false,
                            contentUid: this.panelNull(this.addStudyTaskForm.currentSectionContentData).uid,
                            contentType: this.addStudyTaskForm.taskType,
                            name: this.addStudyTaskForm.taskName,
                            sort: 1
                        });
                    }
                    this.addStudyTaskDialogVisible = false;
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 确定添加新章节
        realAddNewSection() {
            this.$refs['addNewSectionForm'].validate(async (valid) => {
                if (valid) {
                    this.addCampPeriodForm.trainModuleContentList.push({
                        spread: false,
                        name: this.addNewSectionForm.sectionName,
                        sort: 1,
                        upTime: this.addNewSectionForm.upTime,
                        trainModuleContentList: []
                    });
                    this.addNewSectionDialogVisible = false;
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 添加一节
        addFirstSection(){
            this.addNewSectionDialogVisible=true;
        },
        // 添加学习任务
        addStudyTask(data) {
            // 添加标识
            this.oprationFlag = 1;
            this.addStudyTaskForm.currentSectionData=data;
            this.addStudyTaskDialogVisible=true;
        },
        firstLevelDirectoryChange(data){
            data.spread=!data.spread;
        },
        addStudyTaskClose(){
            this.dataReset();
        },
        // 数据重置
        dataReset(){
            this.addStudyTaskForm={
                videoList:[],
                currentSectionData:{},
                taskType:1,
                taskName:"",
                currentSectionContentData:{}
            };
        },
        // 返回列表
        goBackList(){
            //数据重置
            this.dataReset();
            this.$parent.initCampPeriodList();
            // 返回列表
            this.$parent.isAddCampPeriod=false;
        },
        changeShowMoreSeting(){
            this.moreSettingFlag=!this.moreSettingFlag;
        },
        // 取消
        cancel(){
            this.$parent.isAddCampPeriod=false;
        },
        // 上一步
        pre(){
            this.active--;
        },
        // 下一步
        next(){
            this.$refs['addCampPeriodForm'].validate((valid) => {
                if (valid) {
                    this.active++;
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 完成
        async complete() {
            console.log(this.addCampPeriodForm);
            let params = {
                campPeriodName: this.addCampPeriodForm.campPeriodName,//训练营营期名称
                campUid: this.trainingCampInfo.uid,//训练营uid
                summary: this.addCampPeriodForm.campPeriodDesc,//训练营营期简介
                content: this.addCampPeriodForm.campPeriodDetail,//训练营营期详情
                pictureUid: this.addCampPeriodForm.pictureUid,//封面uid
                registrationStartTime: this.addCampPeriodForm.admissionsTime[0],//招生开始时间
                registrationEndTime: this.addCampPeriodForm.admissionsTime[1],//招生结束时间
                trainingStartTime: this.addCampPeriodForm.curriculumTime[0],//培训开始时间
                trainingEndTime: this.addCampPeriodForm.curriculumTime[1],//培训结束时间
                type: this.addCampPeriodForm.unlockingaDirectoryMode,//模式【自由模式】【闯关模式】【日期解锁】
                unlockTime: this.addCampPeriodForm.dateUnlockTime,//日期解锁-解锁时间
                unlockNumber: this.addCampPeriodForm.dateUnlockEveryday,//日期解锁-数量
                unlockUnit: this.addCampPeriodForm.dateUnlockEveryUnit,//日期解锁-单位
                saleMethod: this.addCampPeriodForm.saleType,//售卖方式
                password: this.addCampPeriodForm.encryptedPassword,//加密训练营密码
                releaseSet: this.addCampPeriodForm.shelfType,//发布设置1立即发布2定时发布
                releaseTime: this.addCampPeriodForm.timingShelfTime,//定时发布时间
                saleStatus: this.addCampPeriodForm.shelfStatus,//售卖状态，1在售，0停售
                status: this.addCampPeriodForm.status,//状态(0:已停用，1：已启用，2：已隐藏)
                trainModuleContentList: this.addCampPeriodForm.trainModuleContentList,//科目
            }
            this.$refs['addCampPeriodForm'].validate(async(valid) => {
                if (valid) {
                    // 添加标识
                    if (this.parentThis.oprationFlag == 1) {
                        let result = await addCampPeriod(params);
                        if (result.code == this.$ECode.SUCCESS) {
                            this.finish = true;
                        }
                    } else { 
                        //编辑
                        params.uid = this.campPeriodInfo.uid;
                        let result = await editorCampPeriod(params);
                        if (result.code == this.$ECode.SUCCESS) {
                            this.finish = true;
                        }
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 封面图
        changeCover(file,fileList) {
            console.warn(file)
            let whiteList=['image/jpeg','image/png','image/gif'];
            const isLt2M = file.size / 1024 / 1024 < 2;
            if(!isLt2M){
                this.$message.warning("图片大小不能超过2M");
                return;
            }
            if(!whiteList.includes(file.raw.type)){
                this.$message.warning("请上传指定文件格式");
                return;
            }
            let formData = new FormData();
            formData.append("file", file.raw);
            formData.append("token", getToken());
            formData.append("source", "picture");
            formData.append("projectName", "blog");
            formData.append("sortName", "admin");
            formData.append("file",file.raw);
            uploadFile(formData).then(res=>{
                if (res.code == this.$ECode.SUCCESS){
                    this.addCampPeriodForm.pictureUid=res.data[0].uid;
                    this.addCampPeriodForm.campPeriodCover=res.data[0].url;
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.addCamp-period-container {
    padding: 20px 20px 60px 20px;
    .finish{
        margin-top: 120px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        i{
            color: #00ce77;
            font-size: 120px;
        }
        .title{
            font-weight: bold;
            font-size: 24px;
            margin-bottom: 50px;
        }
        .desc{
            font-size: 16px;
            color: #cac9c9;
        }
    }
    .submit-btn{
        transition: all .5s;
        z-index: 99;
        width: calc(100% - 180px);
        right: 0;
        position: fixed;
        bottom: 0;
        background: white;
        box-shadow: 0 0 10px rgb(209, 209, 209);
        align-items: center;
        display: flex;
        justify-content: center;
        height: 60px;
    }
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        border: 1px dashed #d9d9d9;
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
    }

    .avatar {
        width: 178px;
        height: 178px;
        display: block;
    }

    .form-box {        
        .form{
            .catalogue-box{
                ul{
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    li{
                        cursor: pointer;
                    }
                }
                .add-first-level{
                    padding-left: 10px;
                    background: #f5f7fa;
                    display: flex;
                    align-items: center;
                    color: #5d9ef3;
                    i{
                        margin-right: 5px;
                    }
                }
                .first-level-directory{
                    height: 50px;
                    overflow:hidden;
                    transition: all .5s;
                    width: 900px;
                    .first-level-box{
                        padding:0 10px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        width: 900px;
                        min-height: 50px;
                        background: #f5f7fa;
                        i{
                            transition: all .5s;
                            padding-right: 3px;
                        }
                        .right{
                            font-size: 14px;
                            color: #b8b7b7;
                        }
                    }
                    .two-level-directory{
                        padding:0 30px;
                        width: 900px;
                        height: 50px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        &:hover{
                            background: #f5f7fa;
                        }
                        .operation{
                            i{
                                &:hover{
                                    color: #2f97ec;
                                }
                                margin:0 4px;
                            }
                        }
                        +.task-box{
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;
                            width: 900px;
                            padding: 20px 0;
                            min-height: 150px;
                            background: #e7eaee;
                            .task-name{
                                margin: 0;
                                .task-input{
                                    width: 250px;
                                }
                            }
                        }
                    }
                    .add-study-task{
                        justify-content:flex-start;
                        color: #5d9ef3;
                        i{
                            margin-right: 5px;
                        }
                    }
                }
            }
           .base-info,.open-class-info,.mode-info,.shop-info,.shelf-setting,.course-catalogue{
            padding-left: 10px;
            margin: 20px 0;
            border-left: 3px solid #2f97ec;
           }
           .more-seting-item{
                /deep/ .el-form-item__content{
                    .more-seting{
                        cursor: pointer;
                        color: #2f97ec;
                        i{
                            transition: all .5s;
                        }
                    }
                    .more-seting-status{
                        overflow: hidden;
                    }
                }
           }
           .encrypted-password-item{
                /deep/ .el-form-item__content{
                    .encrypted-password{
                        width: 220px;
                    }
                }
           }
           .mode-of-sale{
            /deep/ .el-form-item__content{
                .tip{
                    span:first-of-type{
                        color: rgb(51, 51, 51);
                    }
                    span:last-of-type{
                        margin-left: 20px;
                        color: #cac9c9;
                    }
                }
                .sale-type{
                    padding-left: 20px;
                    display: flex;
                    align-items: center;
                    width: 500px;
                    height: 60px;
                    background: #f5f5f5;
                }
            }
           }
           .date-unlock{
            /deep/ .el-form-item__content{
                .row-select{
                    display: flex;
                    align-items: center;
                    .date-unlock-everyday{
                        margin: 0 10px;
                        width: 100px;
                    }
                    .date-unlock-every-unit{
                        width: 70px;
                        margin-right: 10px;
                    }
                    .date-unloc-type{
                        margin-left: 10px;
                        width: 70px;
                    }
                }
                .row-time{
                    margin-top: 10px;
                }
            }
           }
        }
    }
}
</style>