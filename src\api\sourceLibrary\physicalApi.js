import request from '@/utils/request'

// 查询设备图标
export function brandIcon() {
  return request({
    url: process.env.ADMIN_API + '/lable/icon/list',
    method: 'get'
  })
}

// 查询品牌型号
export function brandModel(id) {
  return request({
    url: process.env.ADMIN_API + '/lable/treeListByIdAndTier/' + id,
    method: 'get'
  })
}

// 接口
export function brandPort(query) {
  return request({
    url: process.env.ADMIN_API + '/lable/list',
    method: 'get',
    params: query
  })
}

// 查询设备分类
export function deviceType() {
  return request({
    url: process.env.ADMIN_API + '/lable/treeListById/103',
    method: 'get'
  })
}

// 查询物理设备列表
export function listPhysics(query) {
  return request({
    url: process.env.ADMIN_API + '/physics/list',
    method: 'post',
    data: {...query,currentPage:query.pageNum}
  })
}

// 查询物理设备详细
export function getPhysics(id) {
  return request({
    url: process.env.ADMIN_API + '/physics/getInfo/' + id,
    method: 'get'
  })
}

// 新增物理设备
export function addPhysics(data) {
  return request({
    url: process.env.ADMIN_API + '/physics/insertPhysics',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改物理设备
export function updatePhysics(data) {
  return request({
    url: process.env.ADMIN_API + '/physics/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除物理设备
export function delPhysics(id) {
  return request({
    url: process.env.ADMIN_API + '/physics/batchRemove',
    method: 'post',
    data:id
  })
}
