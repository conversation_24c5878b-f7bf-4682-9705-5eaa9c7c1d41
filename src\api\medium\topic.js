import request from '@/utils/request'

// 话题列表
export function getList (params) {
  return request({
    url: process.env.ADMIN_API + '/topic/getList',
    method: 'post',
    data: params
  })
}


// 话题新增
export function addTopic (params) {
  return request({
    url: process.env.ADMIN_API + '/topic/add',
    method: 'post',
    data: params
  })
}

// 话题更新
export function editTopic (params) {
  return request({
    url: process.env.ADMIN_API + '/topic/edit',
    method: 'post',
    data: params
  })
}

// 话题删除
export function deleteBatch (params) {
  return request({
    url: process.env.ADMIN_API + '/topic/deleteBatch',
    method: 'post',
    data: params
  })
}

// 话题置顶
export function pinned (params) {
  return request({
    url: process.env.ADMIN_API + '/topic/pinned',
    method: 'post',
    data: params
  })
}

// 批量话题上下架
export function batchUpAndDown(params) {
  return request({
    url: process.env.ADMIN_API + `/topic/updateStatus?status=${params.status}`,
    method: 'post',
    data: params.uids
  })
}