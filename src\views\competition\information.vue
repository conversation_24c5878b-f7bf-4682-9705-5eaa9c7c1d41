<template>
    <div class="root">
        <div class="top-form">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="类型">
                    <el-select clearable @change="handleType" v-model="topForm.type" placeholder="类型">
                        <el-option label="国赛" :value="1"></el-option>
                        <el-option label="省赛" :value="2"></el-option>
                        <el-option label="行业赛" :value="3"></el-option>
                        <el-option label="企业赛" :value="4"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input size="medium" placeholder="搜索" suffix-icon="el-icon-search" v-model="topForm.title">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button v-if="multipleSelection.length" type="danger" @click="handleDelAll">删除</el-button>
                </el-form-item>
                <div style="float:right;margin-right:45px;" class="top_create">
                    <el-button type="primary" @click="onCreate">发布</el-button>
                </div>
            </el-form>
        </div>
        <div class="content_div">
            <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" header-align="center">
                </el-table-column>
                <el-table-column label="图片" width="140" align="center">
                    <template slot-scope="scope">
                        <img style="height:60px;" :src="scope.row.pictureId">
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" align="center" header-align="center">
                </el-table-column>
                <el-table-column prop="matchType" align="center" header-align="center" label="类型" width="120">
                    <template slot-scope="scope">
                        <span>{{ scope.row.type | filterType }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="releaseTime" label="发布时间" align="center" header-align="center" width="200">
                </el-table-column>
                <el-table-column label="操作" width="240" align="center" header-align="center">
                    <template slot-scope="scope">
                        <div class="control_div">
                            <div @click="handleIsTop(scope.row)">{{ scope.row.isTop == 1 ? '取消置顶' : '置顶' }}</div>
                            <div @click="handleLook(scope.row)">查看</div>
                            <div @click="handleEdit(scope.row)">编辑</div>
                            <div @click="handleDel(scope.row)">删除</div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog :close-on-click-modal="false" class="dialog_div" :title="submitType == 'add' ? '发布资讯' : '修改资讯'"
            :visible.sync="dialogFormVisible" @close="handleClose">
            <el-form :model="InfoForm" :rules="InfoRule" ref="matchForm" label-width="100px">
                <el-form-item label="资讯标题" prop="title">
                    <el-input type="textarea" v-model="InfoForm.title" show-word-limit maxlength="30"></el-input>
                </el-form-item>
                <el-form-item class="yiban" label="资讯类型" prop="type">
                    <el-select v-model="InfoForm.type" placeholder="不限">
                        <el-option label="国赛" :value=1></el-option>
                        <el-option label="省赛" :value=2></el-option>
                        <el-option label="行业赛" :value=3></el-option>
                        <el-option label="企业赛" :value=4></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="上传封面" required>
                    <div class="upload_div">
                        <el-upload
                        class="avatar-uploader"
                        :action="actionPath"
                        :data="otherData"
                        :headers="{
                            Authorization:$GetToken()
                        }"
                            :show-file-list="false" :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload">
                            <img v-if="imageUrl" :src="imageUrl" class="avatar">
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <div class="tip">
                            <p>*支持.JPG,.JPEG,.PNG不超过3M</p>
                        </div>
                        <div class="err_text">
                            {{ imgText }}
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="资讯详情" prop="detail">
                    <div class="infoQuill">
                    </div>
                    <el-upload
                    class="avatar-uploader-img"
                    :action="actionPath"
                    name="img"
                    :data="otherData"
                    :headers="{
                        Authorization:$GetToken()
                    }"
                        accept=".png,.jpeg,.jpg" :show-file-list="false" :on-success="uploadSuccess"
                        :on-error="uploadError" :before-upload="beforeUploadImg">
                    </el-upload>
                    <el-upload
                    class="video-uploader"
                    :action="actionPath"
                    name="video"
                    :data="otherData"
                    accept=".mp4,"
                    :headers="{
                        Authorization:$GetToken()
                    }"
                        :show-file-list="false" :on-success="uploadSuccessVideo" :on-error="uploadError"
                        :before-upload="beforeUploadVideo">
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="submitCreatMatch('matchForm')">{{ submitType == 'add' ? '创建' : '修改' }}
                </el-button>
            </div>
        </el-dialog>
        <infoDialogVue ref="infoDialog" :infoItem="infoItem"></infoDialogVue>
        <div class="paging_div">
            <el-pagination v-show="tableData.length" @current-change="handleCurrentChange" background
                :current-page.sync="topForm.current" :page-size="10" layout="total, prev, pager, next" :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
const toolbarOptions = [
    ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
    ["blockquote", "code-block"], // 引用  代码块
    [{ header: 1 }, { header: 2 }], // 1、2 级标题
    [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
    [{ script: "sub" }, { script: "super" }], // 上标/下标
    [{ indent: "-1" }, { indent: "+1" }], // 缩进
    [{ size: ["small", false, "large", "huge"] }], // 字体大小
    [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
    [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
    [{ font: [] }], // 字体种类
    [{ align: [] }], // 对齐方式
    ["clean"], // 清除文本格式
    ["link", "image", "video"] // 链接、图片、视频
];

import {getToken} from "../../utils/auth";
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';
//引入Qill插件
import Quill from 'quill'
import QuillBetterTable from 'quill-better-table'
import imageResize from "quill-image-resize-module"; // 调整大小组件。
import 'quill-better-table/dist/quill-better-table.css'
import infoDialogVue from "./components/infoDialog.vue";
import {
  addInformation,
  changeInformation,
  deleteInformation,
  getInformation,
  updateIsTopInfo
} from "@/api/informationManage.js";

Quill.register({
    'modules/better-table': QuillBetterTable
}, true)
Quill.register("modules/imageResize", imageResize);

export default {
    created() {
        // 图片上传地址
        this.actionPath = process.env.PICTURE_API + "/file/cropperPicture";
        this.getInformationList();
    },
    mounted() {
        this.otherData.token = getToken() || null;
    },
    components: {
        infoDialogVue
    },
    filters: {
        filterType: function (val) {
            if (val == 1) {
                return '国赛';
            } else if (val == 2) {
                return '省赛';
            } else if (val == 3) {
                return '行业赛';
            } else if (val == 4) {
                return '企业赛';
            }
        },
    },
    data() {
        return {
            submitType: 'add', //提交的类型  创建或者保存
            total: 0,
            type: '',
            infoItem: {},  //资讯详情
            topForm: {
                title: undefined,
                type: undefined,
                current: 1,
                size: 10
            },
            tableData: [],  //资讯列表
            multipleSelection: [],  // 进行多选后的数组
            dialogFormVisible: false,  //弹窗
            formLabelWidth: '80px',
            imgText: '',  //img提示语
            InfoForm: {  //添加资讯的参数
                title: '',  //资讯标题
                pictureId: '', //封面
                type: '', //资讯类型
                detail: '', //资讯描述
            },
            InfoRule: {  //表单校验
                title: [
                    { required: true, message: '请输入资讯标题', trigger: ['blur', 'change'] }
                ],
                type: [
                    { required: true, message: '请选择资讯类型', trigger: ['blur', 'change'] }
                ],
                detail: [
                    { required: true, message: '请输入资讯详情', trigger: ['blur', 'change'] }
                ]
            },
            imageUrl: '',  //封面url
            actionPath: '', //上传图片的请求地址
            otherData: { // 上传图片的参数
                source: "picture",
                userUid: "uid00000000000000000000000000000000",
                adminUid: "uid00000000000000000000000000000000",
                projectName: "blog",
                sortName: "admin",
                token: null,
            },
            infoQuill: '',
            editorOption: {  //富文本
                theme: "snow",
                placeholder: "请输入资讯详情",
                modules: {
                    imageResize: {},
                    toolbar: {
                        container: toolbarOptions,
                        handlers: {
                            image: function (value) {
                                if (value) {
                                    document.querySelector(".avatar-uploader-img input").click();
                                } else {
                                    this.quill.format("image", false);
                                }
                            },
                            video: function (value) {
                                if (value) {
                                    document.querySelector(".video-uploader input").click();
                                } else {
                                    this.quill.format("video", false);
                                }
                            },
                        }
                    }
                }
            }
        }
    },
    methods: {
        initParams() {
            this.topForm.current = 1;
            this.topForm.size = 10;
            this.tableData = [];
        },
        // 初始化创建或者修改的参数
        initInfoForm() {
            this.InfoForm = {
                title: '',  //资讯标题
                pictureId: '', //封面
                type: '', //资讯类型
                detail: '', //资讯描述
            }
            if (this.infoQuill) {
                this.infoQuill.root.innerHTML = '';
            }
        },
        // 初始化获取资讯列表的参数
        initListParams() {
            this.ListParams = {  //资讯列表的参数
                current: 1,
                size: 10
            }
        },
        //获取资讯列表
        getInformationList() {
            getInformation(this.topForm).then((res) => {
                if (res.code == this.$ECode.SUCCESS) {
                    this.tableData = res.data[0] && res.data[0].records;
                    this.total = res.data[0] && res.data[0].total;
                }
            })
        },
        //新建资讯的点击事件
        submitCreatMatch(formName) {
            if (this.InfoForm.pictureId) {
                if (String(this.infoQuill.root.innerText).trim().length) {
                    this.InfoForm.detail = this.infoQuill.root.innerHTML;
                } else {
                    this.InfoForm.detail = '';
                }
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        if (this.submitType == 'add') {
                            addInformation(this.InfoForm).then((res) => {
                                if (res.code == this.$ECode.SUCCESS) {
                                    this.$commonUtil.message.success(res.message);
                                    this.dialogFormVisible = false;
                                    this.getInformationList();
                                    this.initInfoForm();
                                } else {
                                    this.$commonUtil.message.error(res.message);
                                }
                            })
                        } else {
                            changeInformation(this.InfoForm).then((res) => {
                                if (res.code == this.$ECode.SUCCESS) {
                                    this.$commonUtil.message.success(res.message);
                                    this.dialogFormVisible = false;
                                    this.getInformationList();
                                    this.initInfoForm();
                                } else {
                                    this.$commonUtil.message.error(res.message);
                                }
                            })
                        }
                    } else {
                        return false;
                    }
                })
            } else {
                this.imgText = '请上传封面'
            }
        },

        //选择资讯类型
        handleType(value) {
            this.initParams();
            this.getInformationList();

        },

        //删除资讯
        handleDel(item) {
            this.$confirm('确定要删除此资讯吗？', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                center: true
            }).then(() => {
                let params = [];
                params.push(item.uid);
                deleteInformation(params).then((res) => {
                    if (res.code == this.$ECode.SUCCESS) {
                        this.$commonUtil.message.success(res.message);
                        this.getInformationList();
                    }
                })
            });
        },

        //批量删除
        handleDelAll() {
            if (this.multipleSelection.length == 0) {
                this.$commonUtil.message.warning('请选择要删除的资讯');
                return;
            }

            this.$confirm('确定要删除所选资讯吗？', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                center: true
            }).then(() => {
                let params = this.multipleSelection.map((val) => {
                    return val.uid;
                })
                deleteInformation(params).then((res) => {
                    if (res.code == this.$ECode.SUCCESS) {
                        this.$commonUtil.message.success(res.message);
                        this.getInformationList();
                    }
                })
            });
        },
        //编辑资讯
        handleEdit(item) {
            this.InfoForm.uid = item.uid;
            this.imageUrl = item.pictureId;
            this.dialogFormVisible = true;
            this.submitType = 'edit';
            this.InfoForm = item;
            this.$nextTick(() => {
                if (!this.infoQuill) {
                    let dom = document.querySelector('.infoQuill');
                    this.infoQuill = new Quill(dom, this.editorOption);
                }
                this.infoQuill.root.innerHTML = item.detail;
            })
        },

        //查看资讯
        handleLook(item) {
            this.$refs['infoDialog'].infoDialog = true;
            this.infoItem = item;
        },

        //置顶资讯
        handleIsTop(item) {
            let params = {
                id: item.uid,
                isTop: item.isTop ? 0 : 1,
            }
            updateIsTopInfo(params).then((res) => {
                if (res.code == this.$ECode.SUCCESS) {
                    this.$commonUtil.message.success(res.message);
                    this.getInformationList();
                }
            })
        },
        //查询
        onSearch() {
            this.initParams();
            this.getInformationList();
        },
        //发布
        onCreate() {
            if (this.submitType == 'edit') {
                this.initInfoForm();
                this.imageUrl = '';
                this.submitType = 'add';
            }
            this.dialogFormVisible = true;
            this.$nextTick(() => {
                if (!this.infoQuill) {
                    let dom = document.querySelector('.infoQuill');
                    this.infoQuill = new Quill(dom, this.editorOption);
                }
            })
        },
        toggleSelection(rows) {
            if (rows) {
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row);
                });
            } else {
                this.$refs.multipleTable.clearSelection();
            }
        },
        //删除多选的点击事件
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },

        //封面上传成功
        handleAvatarSuccess(res, file) {
            if (res.code == this.$ECode.SUCCESS) {
                // this.imageUrl = URL.createObjectURL(file.raw);
                this.imageUrl = res.data[0] && res.data[0].url
                this.InfoForm.pictureId = res.data[0] && res.data[0].uid;
                this.imgText = '';
            }
        },

        //封面上传前
        beforeAvatarUpload(file) {
            this.otherData.file = file;
            let fileType = ['image/jpeg', 'image/jpg', 'image/png']
            const isJPG = fileType.includes(file.type);
            const isLt3M = file.size / 1024 / 1024 < 3;
            if (!isJPG) {
                this.$message.error('上传封面图片只能是 JPG,JPEG,PNG 格式!');
            }
            if (!isLt3M) {
                this.$message.error('上传封面图片大小不能超过 3MB!');
            }
            return isJPG && isLt3M;
        },

        uploadSuccessVideo(res, file) {
            console.log('file', file);
            // res为图片服务器返回的数据
            // 获取富文本组件实例
            let quill = this.infoQuill;
            // 如果上传成功
            if (res.code == this.$ECode.SUCCESS) {
                // 获取光标所在位置
                let length = quill.getSelection().index;
                // 插入视频
                quill.insertEmbed(length, "video", res.data[0] && res.data[0].url);
                this.InfoForm.videoId = res.data[0] && res.data[0].uid;
                // 调整光标到最后
                quill.setSelection(length + 1);
            } else {
                this.$message.error("视频插入失败");
            }
        },
        // 富文本图片上传前
        beforeUploadImg(file) {
            // 显示loading动画
            this.otherData.file = file;
        },
        // 富文本图片上传前
        beforeUploadVideo(file) {
            // 显示loading动画
            this.otherData.file = file;
        },
        uploadSuccess(res, file) {
            console.log('file', file);

            // res为图片服务器返回的数据
            // 获取富文本组件实例
            let quill = this.infoQuill;
            // 如果上传成功
            if (res.code == this.$ECode.SUCCESS) {
                // 获取光标所在位置
                let length = quill.getSelection().index;
                // 插入图片  res.url为服务器返回的图片地址
                quill.insertEmbed(length, "image", res.data[0] && res.data[0].url);
                // 调整光标到最后
                quill.setSelection(length + 1);
            } else {
                this.$message.error("图片插入失败");
            }
        },
        // 富文本图片上传失败
        uploadError() {
            this.$message.error("图片插入失败");
        },

        handleCurrentChange(val) {
            this.topForm.current = val;
            this.getInformationList();
        },
        handleClose() {
            if (this.submitType == 'edit') {
                this.getInformationList();
            }
            this.initInfoForm();
            this.imageUrl = '';
            this.dialogFormVisible = false
        },

    }
}
</script>

<style lang="scss" scoped>
.root {
    .top-form {
        padding: 10px;

        .el-select {
            /deep/ .el-input {
                width: 125px;
            }
        }
    }

    .content_div {
        margin: 0px 45px 0px 30px;

        .control_div {
            display: flex;
            flex-direction: row;
            justify-content: center;

            div {
                color: #006eff;
                border-right: 1px solid #006eff;
                height: 20px;
                margin-left: 10px;
                padding-right: 6px;
                cursor: pointer;
            }

            div:last-child {
                border: none;
            }
        }
    }


    .dialog_div {
        /deep/ .el-dialog {
            width: 75%;

            .el-dialog__body {
                padding: 30px 20px 0px 20px;

                .upload_div {
                    display: flex;
                    align-items: center;
                    position: relative;

                    .tip {
                        font-size: 10px;
                        color: #ccc;
                        margin-left: 20px;
                    }

                    .err_text {
                        position: absolute;
                        color: #F56C6C;
                        font-size: 12px;
                        top: 85%;
                        left: 2%;
                    }

                    .avatar-uploader .el-upload {
                        border: 1px dashed #d9d9d9;
                        border-radius: 6px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }

                    .avatar-uploader .el-upload:hover {
                        border-color: #409EFF;
                    }

                    /deep/.el-upload {
                        position: relative;
                    }

                    .avatar-uploader-icon {
                        font-size: 22px;
                        color: black;
                        width: 104px;
                        height: 105px;
                        background: #f3f3f3;
                        line-height: 98px;
                        text-align: center;
                    }

                    .avatar {
                        width: 160px;
                        height: 160px;
                        display: block;
                    }
                }
            }



            .yiban {
                .el-form-item__content {
                    width: 40%;

                    .el-select {
                        width: 100%;
                    }
                }
            }

            .el-dialog__footer {
                padding: 0px 20px 20px 20px;
                display: flex;
                justify-content: center;
            }

            .ql-formats {
                margin-right: 10px;
                line-height: normal !important;

            }

            .ql-editor {
                height: 300px;
            }

            .avatar-uploader-img {
                height: 0px;
            }

            .video-uploader {
                height: 0px;
            }
        }
    }

}

.paging_div {
    margin: 20px 0px;
    display: flex;
    justify-content: center;
}
</style>
