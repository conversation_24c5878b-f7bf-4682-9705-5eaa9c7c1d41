import { getToken } from '@/utils/auth'
import axios from 'axios'
import { Loading, Message, MessageBox } from 'element-ui'
import store from '../store'
// 白名单的不展示请求地址
const whiteList = ['getOnlineList', 'getChatList', 'bigFileUpload']
export const host = ''
// 创建axios实例
export const service = axios.create({
  baseURL: host ? host + '/huanyu-admin' : '', // api 的 base_url
  withCredentials: true, // 允许后台的cookie传递到前端
  timeout: 300000 // 请求超时时间
})

// 传递token
service.defaults.headers.common['Authorization'] = getToken()

// 请求计数器
var requestNum = 0
var loading = null

// request拦截器
service.interceptors.request.use(
  config => {
    if (getToken()) {
      // 让每个请求携带自定义token 请根据实际情况自行修改
      config.headers.Authorization = getToken()
    }

    // 请求加1
    requestNum++
    let url = config.url.split('/')
    url = (url && url[url.length - 1]) || ''
    if (!whiteList.includes(url)) {
      if (loading == null) {
        loading = Loading.service({ fullscreen: true, text: '正在努力加载中~', background: 'rgba(0, 0, 0, 0)' })
      } else if (loading != null && requestNum > 0) {
        loading = Loading.service({ fullscreen: true, text: '正在努力加载中~', background: 'rgba(0, 0, 0, 0)' })
      }
    }
    return config
  },
  error => {
    // Do something with request error
    // console.log(requestNum);
    console.log(error)
    Promise.reject(error)
    // 出错了直接关闭loading
    store.dispatch('clearNumber')
    requestNum = 0
    if (loading) {
      loading.close()
    }
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    /**
     * code为非success和error是抛错 可结合自己业务进行修改
     */
    const res = response.data
    const config = response.config
    // 请求数减1
    requestNum--
    // console.log(requestNum);
    if ((loading == null || requestNum <= 0) && store.getters.number <= 0) {
      if (loading) {
        loading.close()
      }
    }
    if (
      res.code === 208 ||
      res.code === 0 ||
      res.code === 200 ||
      res.code === 201 ||
      res.success
    ) {
      // 请求完毕
      if (res.code === 201) {
        if (res.message || res.msg) {
          Message({
            message: res.message || res.msg,
            type: 'warning',
            duration: 3 * 1000
          })
        }
      }
      return response.data
    } else {
      if (config.responseType === 'blob') {
        // 导出文件时
        if (response.data.size == 0) {
          Message({
            message: '请检查是否有权限及数据',
            type: 'error',
            duration: 5 * 1000
          })
          return Promise.reject('error')
        }
        return response.data
      }
      // 出错了直接关闭loading
      store.dispatch('clearNumber')
      requestNum = 0
      if (loading) {
        loading.close()
      }
      if (res.code === 401) {
        MessageBox.confirm(
          'token已过期或暂无权限，可以取消继续留在该页面，或者重新登录',
          '确定登出',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
        return Promise.reject('error')
      } else if (res.code === 402) {
        // 接口没有权限访问时
        Message({
          message: res.data,
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject('error')
      } else {
        // console.log('错误信息', res)

        if (response.config.url.indexOf('/huanyu-admin/blog/keyword/exportWords') > -1) {
          // 下载白名单
          return Promise.resolve({ code: 'success', data: res })
        }
        if (res.code === 201) {
          Message({
            message: res.message || res.msg,
            type: 'success',
            duration: 5 * 1000
          })
        } else {
          Message({
            message: res.message || res.msg,
            type: 'error',
            duration: 5 * 1000
          })
        }

        // return Promise.reject(res.message)
        return Promise.resolve(res)
      }
    }
  },
  error => {
    console.log('错误码', error)
    // 出错了直接关闭loading
    // console.log(requestNum);
    store.dispatch('clearNumber')
    requestNum = 0
    if (loading) {
      loading.close()
    }
    Message({
      message: error,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error.message)
  }
)

export default service
