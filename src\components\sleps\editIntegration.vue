<template>
  <div>
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-formData"
    >
      <el-form-item label="设备名称" prop="topoName">
        <el-input v-model="topoName" maxlength="20" show-word-limit @change="formData.topoName = topoName"/>
      </el-form-item>
      <el-form-item label="资源群集" prop="availability_zone">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.availability_zone"
          class="header-search-select"
          disabled
        >
          <el-option
            v-for="option in clusterNameOptions"
            :key="option.name"
            :value="option.value"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="云主机类型" prop="specificationId">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.specificationId"
          class="header-search-select"
          @change="changeFlavor"
        >
          <el-option
            v-for="option in specificationNameOptions"
            :key="option.id"
            :value="option.id"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="cloudCategory">
        <el-radio-group v-model="formData.cloudCategory">
          <el-radio-button label="云主机" />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="CPU" prop="vcpus">
        <el-radio-group v-model="formData.vcpus" @change="changeCpu">
          <el-radio-button
            v-for="item in cpuListAll"
            :label="item.name"
            :key="item.id"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内存" prop="ram">
        <el-radio-group v-model="formData.ram" @change="changeMemory">
          <el-radio-button
            v-for="item in memoryList"
            :label="item.name"
            :key="item.id"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="系统盘类型" prop="diskTypeId">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.diskTypeId"
          class="header-search-select"
        >
          <el-option
            v-for="option in diskTypeNameOptions"
            :key="option.id"
            :value="option.id"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="系统盘大小" prop="diskSize">
        <el-slider
          v-model="formData.diskSize"
          :min="20"
          :max="3000"
          style="width: calc(100% - 40px)"
        />
        <el-input
          v-model="formData.diskSize"
          style="width: calc(100% - 40px); margin-right: 10px"
          readonly
        />GB
      </el-form-item>
      <el-form-item
        v-for="(item, index) in i3NetworkVos"
        :key="index"
        label="网络"
        prop="network"
      >
        <el-form-item
          :prop="'i3NetworkVos.' + index + '.id'"
          :rules="rules.i3SubnetId"
          label="子网"
        >
          <el-select
            ref="headerSearchSelect"
            v-model="item.id"
            :filterable="true"
            class="header-search-select"
            clearable
            @change="changeI3SubnetId(item.id, index)"
          >
            <el-option-group
              v-for="group in typeOptions"
              :key="group.id"
              :label="group.networkName"
            >
              <el-option
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item
          :prop="'i3NetworkVos.' + index + '.ip'"
          :rules="rules.ip"
          label="IP"
        >
          <el-input
            v-model="item.ip"
            placeholder="默认自动分配IP地址"
            disabled
            class="width_input"
          />
          <el-button
            v-if="i3NetworkVos.length > 1"
            style="margin-left: 20px"
            type="info"
            icon="el-icon-minus"
            @click="deleteType(item, index)"
          />
        </el-form-item>
      </el-form-item>
      <el-form-item label>
        <el-button
          :disabled="addDisable"
          type="primary"
          icon="el-icon-plus"
          @click="addType"
        />
      </el-form-item>
      <el-form-item label="安全组" prop="securityGroupId">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.securityGroupId"
          class="header-search-select"
        >
          <el-option
            v-for="option in securityGroupNameOptions"
            :key="option.id"
            :value="option.id"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="登陆方式" prop="loginType">
        <el-radio-group v-model="formData.loginType">
          <el-radio-button label="密码" />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="用户名" prop="admin_user">
        <el-input
          v-model="formData.admin_user"
          maxlength="20"
          show-word-limit
          class="width_input"
          disabled
        />
      </el-form-item>
      <el-form-item label="密码" prop="security">
        <el-input
          v-model="formData.security"
          show-password
          minlength="8"
          maxlength="30"
          show-word-limit
          placeholder="8-30个字符，且同时包含其中三项（大写字母、小写字母、数字、特殊符号）"
          class="width_input"
        />
      </el-form-item>
      <el-form-item label="数量" prop="count">
        <el-input v-model="formData.count" class="width_input" type="number" />
      </el-form-item>
    </el-form>
    <div class="btn_div">
      <el-button type="primary" class="btn" @click="preservation">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  getAvailabilityZoneList,
  getCloudHostType,
  getSystemType,
  treeListById,
  getSecurityGroup,
  getSubnetList
} from '@/api/sourceLibrary/virtualApi'
import { listLabel } from '@/api/admin/label'
export default {
  name: 'EditAdviser',
  components: {},
  // eslint-disable-next-line vue/require-prop-types
  props: ['isSubmitAdviser', 'formData'],
  data() {
    var validateCount = (rule, value, callback) => {
      if (value < 1 || value > 500) {
        callback(new Error('数量范围为1-500'))
      } else {
        callback()
      }
    }
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        // this.$refs.ruleForm.validateField('checkPass');
        const reg =
          /((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,30}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,30}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{8,30}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{8,30}$))/
        if (!reg.test(value)) {
          callback(
            new Error(
              '请输入8-30个字符，且同时包含其中三项（大写字母、小写字母、数字、特殊符号）'
            )
          )
        } else {
          const tes = /^[^\u4e00-\u9fa5]+$/
          if (!tes.test(value)) {
            callback(new Error('请勿输入中文！'))
          } else {
            callback()
          }
        }
      }
    }
    // ip校验
    var checkIp = (rule, value, callback) => {
      const reg =
        /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/
      if (!value) {
        callback()
      } else if (!reg.test(value)) {
        callback(new Error('ip格式不正确！'))
      } else {
        callback()
      }
    }
    return {
      optionProps: {
        value: 'id',
        label: 'label',
        children: 'children'
      },
      cpuList: [],
      cpuListAll: [],
      memoryList: [],
      securityGroupNameOptions: [],
      diskTypeNameOptions: [],
      clusterNameOptions: [], // 资源集群
      specificationNameOptions: [], // 云主机类型
      facilityBrandOptions: [],
      specAllOptions: [],
      typeOptions: [],
      i3NetworkVos: this.formData.i3NetworkVos,
      facilityTypeOptions: [],
      connectorOptions: [],
      addDisable: false,
      topoName: this.formData.topoName,
      productPortOptions: [],
      rules: {
        topoName: [
          { required: true, message: '请输入设备名称', trigger: 'blur' }
        ],
        security: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' }
        ],
        count: [{ validator: validateCount, trigger: 'blur' }],
        securityGroupId: [
          { required: true, message: '请选择安全组', trigger: 'change' }
        ],
        ip: [
          // { required: true, message: '请选择安全组', trigger: 'change' },
          { validator: checkIp, trigger: 'blur' }
        ],
        i3SubnetId: [
          { required: true, message: '请选择子网', trigger: 'change' }
        ],
        clusterName: [
          { required: true, message: '请输入资源集群', trigger: 'change' }
        ],
        specificationId: [
          { required: true, message: '请输入云主机类型', trigger: 'change' }
        ],
        cloudCategory: [
          { required: true, message: '请输入类型', trigger: 'change' }
        ],
        vcpus: [{ required: true, message: '请选择cpu', trigger: 'change' }],
        ram: [{ required: true, message: '请选择内存', trigger: 'change' }],
        diskTypeId: [
          { required: true, message: '请选择系统盘类型', trigger: 'change' }
        ],
        diskSize: [
          { required: true, message: '请选择系统盘大小', trigger: 'change' }
        ],
        facilityBrandId: [
          { required: true, message: '请选择品牌', trigger: 'change' }
        ],
        facilityBrandTypeId: [
          { required: true, message: '请选择型号', trigger: 'change' }
        ],
        connectorId: [
          { required: true, message: '请选择管理接口', trigger: 'change' }
        ],
        administerIp: [{ validator: checkIp, trigger: 'blur' }]
      },
      expId: ''
    }
  },
  created() {
    this.getAvaList()
    this.getCloudTypeList()
    this.getSystemTypeList()
    this.getFacilityBrandList()
    this.getList()
    this.getNetwork()
  },
  async mounted() {},
  methods: {
    preservation() {
      this.$emit('preservation')
    },
    async _confirm() {
      this.topologyId = this.currentRow.id
      this.formData.topology_id = this.currentRow.id
      this.formData.topology_name = this.currentRow.name
    },
    changeI3SubnetId(val, index) {
      for (const item of this.typeOptions) {
        // eslint-disable-next-line no-unused-vars
        if (val == item.id) {
          this.i3NetworkVos[index].i3NetworkName = item.networkName
          this.i3NetworkVos[index].i3NetworkId = item.networkId
          this.i3NetworkVos[index].i3SubnetId = item.id
        }
      }
    },
    async getList() {
      await getSecurityGroup()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.securityGroupNameOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    async getNetwork() {
      await getSubnetList()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.typeOptions = res.data.result
            console.log(this.typeOptions)
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    onSubmit() {
      this.getList()
    },
    addType() {
      if (this.i3NetworkVos.length >= 16) {
        this.addDisable = true
        this.$message.error('系统允许的最大网络数量为16')
        return
      }
      const obj = {}
      this.i3NetworkVos.push(obj)
    },
    deleteType(item, index) {
      this.i3NetworkVos.splice(index, 1)
      if (this.i3NetworkVos.length < 16) {
        this.addDisable = false
      }
    },
    // 品牌
    getFacilityBrandList() {
      const id = 102
      this.facilityBrandOptions = []
      treeListById(id)
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.facilityBrandOptions = res.data
            this.changeFacilityBrandId()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 改变品牌
    changeFacilityBrandId() {
      // this.formData.facilityBrandTypeId = null
      let filterData = this.facilityBrandOptions.filter(
        (ele) => ele.id == this.formData.facilityBrandId
      );
      this.facilityTypeOptions = filterData.length && filterData[0].children;
      if (this.facilityTypeOptions) {
        const facilityBrandTypeId = this.facilityTypeOptions.filter(
          (ele) => ele.id == this.formData.facilityBrandTypeId
        )
        if (!facilityBrandTypeId || !facilityBrandTypeId.length) {
          this.formData.facilityBrandTypeId = null
          this.formData.connectorId = null
        } else {
          this.changeFacilityType()
        }
      } else {
        this.formData.facilityBrandTypeId = null
        this.formData.connectorId = null
      }
    },
    // 改变型号
    changeFacilityType() {
      // this.formData.connectorId = null
      const arr = this.facilityBrandOptions.filter(
        (ele) => ele.id == this.formData.facilityBrandId
      )[0].children
      this.productPortOptions = arr.filter(
        (ele) => ele.id == this.formData.facilityBrandTypeId
      )[0].children
      this.connectorOptions = arr.filter(
        (ele) => ele.id == this.formData.facilityBrandTypeId
      )[0].children
      let connectorId
      if (this.productPortOptions && this.productPortOptions.length) {
        connectorId = this.productPortOptions.filter(
          (ele) => ele.id == this.formData.connectorId
        )
        this.formData.productPort = []
        for (const item of this.productPortOptions) {
          this.formData.productPort.push(item.id)
        }
      }
      if (!connectorId || !connectorId.length) {
        this.formData.connectorId = null
      }
      this.$forceUpdate()
    },
    // 改变品牌型号
    handleChange(val) {
      this.formData.facilityBrandId = val[val.length - 1]
      this.formData.productPort = []
      const params = {
        lableId: this.formData.facilityBrandId,
        pageNum: 1,
        pageSize: 10000
      }
      listLabel(params)
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.connectorOptions = res.rows
            this.productPortOptions = res.rows
            for (const item of this.productPortOptions) {
              this.formData.productPort.push(item.lableId)
            }
            this.$forceUpdate()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(() => {})
    },
    // 资源集群下拉框
    async getAvaList() {
      await getAvailabilityZoneList()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.clusterNameOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 云主机类型下拉框
    async getCloudTypeList() {
      await getCloudHostType()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.specificationNameOptions = res.data.result
            for (const item of this.specificationNameOptions) {
              const obj = {
                name: item.vcpus + 'CPU',
                id: item.vcpus
              }
              const arr = this.cpuListAll.filter((ele) => ele.id == obj.id)
              if (!arr || !arr.length) {
                this.cpuListAll.push(obj)
                this.cpuListAll.sort((a, b) => a.id - b.id)
              }
            }
            // if (this.formData.flavor_id) {
            this.changeFlavor(this.formData.flavor_id)
            // }
            console.log(this.cpuListAll)
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 系统盘类型下拉框
    async getSystemTypeList() {
      await getSystemType()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.diskTypeNameOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    changeFlavor(val) {
      console.log(val)
      this.specAllOptions = []
      this.memoryList = []
      const arr = this.specificationNameOptions.filter((ele) => ele.id == val)
      console.log('arr', arr)
      this.formData.specificationName = arr[0].name
      if (arr && arr.length) {
        this.cpuList = this.cpuListAll.filter((ele) => ele.id <= arr[0].vCpus)
        this.formData.vcpus = arr[0].vcpus + 'CPU'
      }
      for (const item of this.specificationNameOptions) {
        const obj = {
          name: item.ram >= 1024 ? item.ram / 1024 + 'GB' : item.ram + 'MB',
          id: item.ram,
          cpu: item.vcpus
        }
        const memory = this.specAllOptions.filter((ele) => {
          return ele.name === obj.name && ele.cpu == arr[0].vcpus
        })
        if (!memory || !memory.length) {
          this.specAllOptions.push(obj)
        }
      }
      this.memoryList = this.specAllOptions.filter((ele) => {
        return ele.cpu == arr[0].vcpus
      })
      // console.log('this.memoryList', this.memoryList)
      this.memoryList.sort((a, b) => a.id - b.id)
      this.formData.ram =
        arr[0].ram >= 1024 ? arr[0].ram / 1024 + 'GB' : arr[0].ram + 'MB'
    },

    addDiskVos() {
      const obj = { diskSize: 0, diskTypeId: '' }
      if (this.formData.diskVos.length > 1) {
        return
      }
      this.formData.diskVos.push(obj)
    },
    changeDiskVos(item, index) {
      const arr = this.diskTypeNameOptions.filter(
        (ele) => ele.diskTypeId == item.diskTypeId
      )
      this.formData.diskVos[index].diskTypeId = arr[0].diskTypeId
    },
    deleteDiskVos(item, index) {
      this.formData.diskVos.splice(index, 1)
    },
    changeCpu(val) {
      this.memoryList = []
      const arr = this.specificationNameOptions.filter(
        (ele) => ele.vcpus == parseInt(val)
      )
      for (const item of arr) {
        const obj = {
          id: item.ram,
          name: item.ram >= 1024 ? item.ram / 1024 + 'GB' : item.ram + 'MB'
        }
        this.memoryList.push(obj)
        this.memoryList.sort((a, b) => a.id - b.id)
      }
      const arr1 = this.memoryList.filter(
        (ele) => ele.name == this.formData.ram
      )
      if (!arr1 || !arr1.length) {
        this.formData.ram = this.memoryList[0].name
      }
      this.formData.specificationId = this.specificationNameOptions.filter(
        (ele) =>
          ele.vcpus == parseInt(val) &&
          ele.ram ==
            (this.formData.ram.indexOf('MB') != -1
              ? parseInt(this.formData.ram)
              : parseInt(this.formData.ram) * 1024)
      )[0].id
    },
    changeMemory(val) {
      let memory
      if (val.indexOf('MB') != -1) {
        memory = parseInt(val)
      } else {
        memory = parseInt(val) * 1024
      }
      // let memory = parseInt(val) < 1024 ? parseInt(val) * 1024 : parseInt(val)
      const arr = this.specificationNameOptions.filter(
        (ele) => ele.vcpus == parseInt(this.formData.vcpus) && ele.ram == memory
      )
      this.formData.specificationId = this.specificationNameOptions.filter(
        (ele) => ele.vcpus == parseInt(this.formData.vcpus) && ele.ram == memory
      )[0].id
      console.log(arr)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .width_input {
    width: 193px;
  }
  .btn_div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      margin-bottom: 20px;
    }
  }
}
</style>
