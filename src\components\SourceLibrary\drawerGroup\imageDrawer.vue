<template>
  <div class="image">
    <drawerGroup :drawer="drawer" title="系统镜像" @closeDrawer="close">
      <template>
        <div class="image_content">
          <slide-tab :tab-arr="tabs" @tabClick="handleClick"/>
          <div class="detail_chain">
            <div v-if="activeName == '0'">
              <el-row>
                <el-col :span="24">
                  <div class="message">基本信息</div>
                </el-col>
              </el-row>
              <el-form ref="image_content" :model="formData" label-width="120px">
                <el-form-item label="名称:">{{ formData.name }}</el-form-item>
                <el-form-item label="ID:">{{ formData.id }}</el-form-item>
                <el-form-item label="所有者:">{{ formData.owner }}</el-form-item>
                <el-form-item
                  v-if="formData.status = 'active' ? '可用': '不可用'"
                  label="状态:"
                >{{ formData.status }}</el-form-item>
                <el-form-item
                  v-if="formData.is_public = true ? '是': '否'"
                  label="共享:"
                >{{ formData.is_public }}</el-form-item>
                <el-form-item
                  v-if="formData.isProtected = false ? '是': '否'"
                  label="受保护:"
                >{{ formData.isProtected }}</el-form-item>
                <el-form-item label="校验和:">{{ formData.checksum }}</el-form-item>
                <el-form-item label="创建时间:">{{ formatDate(formData.created_at) }}</el-form-item>
                <el-form-item label="更新时间:">{{ formatDate(formData.updated_at) }}</el-form-item>
                <el-form-item label="描述:">{{ formData.description }}</el-form-item>
                <el-form-item
                  v-if="formData.properties && formData.properties.ipv6_support"
                  label="支持IPV6:"
                >{{ formData.properties ? (formData.properties.ipv6_support == 'true' ? '是': '否') : '' }}</el-form-item>
                <!-- <el-form-item label="支持IPv6:"  v-if="formData.properties.ipv6_support  = 'true' ? '是': '否'">{{formData.properties.ipv6_support}}</el-form-item> -->
              </el-form>
              <el-row>
                <el-col :span="24">
                  <div class="message">规格</div>
                </el-col>
              </el-row>
              <el-form ref="image_content" :model="formData" label-width="120px">
                <el-form-item label="容量:">{{ formData.format_size }}</el-form-item>
                <el-form-item label="对象存储格式:">{{ formData.container_format }}</el-form-item>
                <el-form-item label="磁盘格式:">{{ formData.disk_format }}</el-form-item>
                <el-form-item label="最低内存:">{{ formData.min_ram }}</el-form-item>
                <el-form-item label="最小磁盘:">{{ formData.min_disk }}</el-form-item>
              </el-form>
              <el-row>
                <el-col :span="24">
                  <div class="message">定制属性</div>
                </el-col>
              </el-row>
              <el-form ref="image_content" :model="formData" label-width="180px">
                <el-form-item
                  v-if="formData.hw_boot_menu= true ? '是': '否'"
                  label="显示启动菜单:"
                >{{ formData.hw_boot_menu }}</el-form-item>
                <el-form-item
                  v-if="formData.hw_qemu_guest_agent= 'yes' ? '是': '否'"
                  label="支持qemu-guest-agent:"
                >{{ formData.hw_qemu_guest_agent }}</el-form-item>
                <el-form-item label="镜像配置驱动:">{{ formData.img_config_drive }}</el-form-item>
                <el-form-item label="网卡模式:">{{ formData.hw_vif_model }}</el-form-item>
                <el-form-item label="操作系统类型:">{{ formData.os_type }}</el-form-item>
                <el-form-item label="用户名:">{{ formData.user_name }}</el-form-item>
                <el-form-item v-if="formData.pass_word" label="密码:">******</el-form-item>
                <el-form-item v-else label="密码:">{{ formData.pass_word }}</el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </template>
    </drawerGroup>
  </div>
</template>
<script>
import { getImage } from '@/api/sourceLibrary/imageApi'
import { formatDate } from '@/utils/index'
import drawerGroup from '@/components/SourceLibrary/drawerGroup/index'
import slideTab from '@/components/SourceLibrary/slideTab/index.vue'
export default {
  name: 'ImageDrawer',
  components: {
    drawerGroup,
    slideTab
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    tabsNum: {
      type: String,
      default: '0'
    },
    // 主界面传来的id，通过此id获取详情数据
    drawerData: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    }
  },
  data() {
    return {
      formatDate: '', // 格式化时间
      tabs: ['详情'], // 顶部选项卡
      activeName: '0', // 顶部选项卡默认选中详情
      formData: {
        // 详情页数据
        name: '',
        id: '',
        owner: '',
        status: '',
        is_public: '',
        isProtected: '',
        checksum: '',
        created_at: '',
        updated_at: '',
        description: '',
        systemDiskType: '',
        properties: {
          ipv6_support: ''
        }
      }
    }
  },
  created() {
    // 顶部选项卡
    this.activeName = this.tabsNum
    this.getList()
    // 格式化时间
    this.formatDate = formatDate
  },
  methods: {
    /**
     * @name:
     * @msg: 点击名称获取详情页数据
     * @param {*}
     * @return {*}
     */
    async getList() {
      if (this.activeName == '0') {
        await getImage(this.drawerData.id).then((res) => {
          if (res.code == 200) {
            this.formData = Object.assign(res.data, this.drawerData)
            // this.drawerFlag = true
          }
        })
      }
    },
    // 顶部选项卡切换
    handleClick(val) {
      console.log(val)
      this.activeName = val
    },
    // 详情弹框关闭
    close() {
      this.$parent.openDrawer = false
    }
  }
}
</script>
<style lang="scss" scoped>
.image {
  &::v-deep {
    .el-drawer__open {
      height: 80%;
    }
    .el-drawer__container {
      top: 20%;
    }
    .image_content {
      .detail_chain {
        padding: 20px !important;
        box-sizing: border-box;
        height: 72vh;
        overflow-y: auto;
        .el-form-item {
          margin-bottom: 0;
        }
        .message {
          color: #666;
          background: #f7f7f7;
          height: 40px;
          line-height: 40px;
          padding-left: 10px;
        }
        .el-col {
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
