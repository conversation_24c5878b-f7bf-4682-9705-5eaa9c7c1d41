import request from '@/utils/request'

/**
 * 获取管理员列表
 * @param params
 */
export function getSkipList(params) {
  return request({
    url: process.env.ADMIN_API + '/interfaceSkip/pageList',
    method: 'post',
    data: params
  })
}

export function addSkip(params) {
  return request({
    url: process.env.ADMIN_API + '/interfaceSkip/add',
    method: 'post',
    data: params
  })
}

export function editSkip(params) {
  return request({
    url: process.env.ADMIN_API + '/interfaceSkip/edit',
    method: 'post',
    data: params
  })
}

export function deleteSkip(params) {
  return request({
    url: process.env.ADMIN_API + '/interfaceSkip/deleteBatch',
    method: 'post',
    data: params
  })
}

export function restoreSkip(params) {
  return request({
    url: process.env.ADMIN_API + '/interfaceSkip/restore',
    method: 'post',
    data: params
  })
}
