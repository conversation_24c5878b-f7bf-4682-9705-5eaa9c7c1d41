import request from '@/utils/request'

/**
 * 保存拓扑
 */
export function addAreaTopoAPI(data) {
  return request({
    url: '/topology/cepoSysTopology/addAreaTopo',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取拓扑
 * @param {*} params
 */
export function getAreaTopoAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/getAreaTopo/${params}`,
    method: 'get'
  })
}
