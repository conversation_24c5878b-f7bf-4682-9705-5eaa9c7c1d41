import request from "@/utils/request";

export function getPersonLabList(params) {
    return request({
        url: process.env.ADMIN_API + "/classroom/speaker/getPersonLabList",
        method: "post",
        data: params
    });
}

export function updateStatus(params) {
    return request({
        url: process.env.ADMIN_API + "/classroom/speaker/updateStatus",
        method: "post",
        data: params
    });
}

// 批量启用
export function disableStatus(params) {
    return request({
        url: process.env.ADMIN_API + "/classroom/enterprise/updateAbleStatus",
        method: "post", 
        data: params
    });
}


// 批量禁用
export function updateAbleStatus(params) {
    return request({
        url: process.env.ADMIN_API + "/classroom/enterprise/disableStatus",
        method: "post",
        data: params
    });
}