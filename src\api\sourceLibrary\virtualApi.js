import request from '@/utils/request'

// 查询虚拟设备列表
export function listVirtual(query) {
  return request({
    url: process.env.ADMIN_API + '/virtual/list',
    method: 'get',
    params: query
  })
}

// 查询虚拟设备详细
export function getVirtual(id) {
  return request({
    url: process.env.ADMIN_API + '/virtual/' + id,
    method: 'get'
  })
}

// 新增虚拟设备
export function addVirtual(data) {
  return request({
    url: process.env.ADMIN_API + '/virtual/addOfRestful',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改虚拟设备
export function editVirtual(data) {
  return request({
    url: process.env.ADMIN_API + '/virtual/edit',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除虚拟设备
export function delVirtual(ids) {
  return request({
    url: process.env.ADMIN_API + '/virtual/batchRemove',
    method: 'post',
    data: [ids]
  })
}


// 系统盘类型列表
export function getSystemType(id) {
  return request({
    url: process.env.ADMIN_API + '/topology/getVolumeTypeList',
    method: 'get'
  })
}
// 安全组列表
export function getSecurityGroup(id) {
  return request({
    url: process.env.ADMIN_API + '/topology/getSecurityGroup',
    method: 'get'
  })
}
// 网络列表
export function getNetworks(id) {
  return request({
    url: process.env.ADMIN_API + '/topology/getNetworks',
    method: 'get'
  })
}

// 添加路由器
export function addRouterV3(data) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/routerV3',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除路由器
export function removeRouterV3(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/routerV3/remove?routerId=' + id,
    method: 'get'
  })
}

// 删除交换机
export function removeSwitchV3(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/switchV3/remove?switchId=' + id,
    method: 'get'
  })
}

// 查询路由子网网络列表
export function getSubnetList(id) {
  return request({
    url: process.env.ADMIN_API +'/images/getSubnetList',
    method: 'get'
  })
}

// 添加交换机
export function addSwitchV3(data) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/switchV3',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 开机
export function cloudHostStart(id) {
  return request({
    url: '/topology/virtual/cloudHostStart/' + id,
    method: 'get'
  })
}
// 关机
export function cloudHostStop(id) {
  return request({
    url: '/topology/virtual/cloudHostStop/' + id,
    method: 'get'
  })
}
// 重启
export function cloudHostReboot(id) {
  return request({
    url: '/topology/virtual/cloudHostReboot/' + id,
    method: 'get'
  })
}
// 重建
export function rebuild(id) {
  return request({
    url: '/topology/virtual/rebuild/' + id.id + '/' + id.systemImageId,
    method: 'post',
    data: {
      password: id.password
    }
  })
}
// 挂起
export function suspend(id) {
  return request({
    url: '/topology/virtual/suspend/' + id,
    method: 'get'
  })
}
// 恢复
export function resume(id) {
  return request({
    url: '/topology/virtual/resume/' + id,
    method: 'get'
  })
}
// vnc登陆
export function cloudHostVnc(id) {
  return request({
    url: '/topology/virtual/cloudHostVncV3/' + id,
    method: 'get'
  })
}
//
// 查询云主机已挂载的数据盘
export function cloudHostVolumes(id) {
  return request({
    url: '/topology/virtual/cloudHostVolumes/' + id,
    method: 'get'
  })
}
// 查询云主机已挂载的系统盘
export function findCloudHostUUID(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/findCloudHostUUID/' + id,
    method: 'get'
  })
}
// 创建快照
export function addSnapshots(id) {
  return request({
    url: '/topology/virtual/addSnapshots/' + id,
    method: 'get'
  })
}
// 删除快照
export function deleteSnapshots(id) {
  return request({
    url: '/topology/virtual/deleteSnapshots/' + id,
    method: 'get'
  })
}
// 编辑快照
export function editSnapshots(id) {
  return request({
    url: '/topology/virtual/editSnapshots/' + id,
    method: 'get'
  })
}
// 恢复快照
export function recoverSnapshots(id) {
  return request({
    url: '/topology/virtual/recoverSnapshots/' + id,
    method: 'get'
  })
}
// 生成镜像
export function cloudHostCreateImage(id) {
  return request({
    url: '/topology/virtual/cloudHostCreateImage/' + id.cloudHostId,
    method: 'post',
    data: id.imageName
  })
}
// 更改云主机配置
export function cloudHostResize(id) {
  return request({
    url: '/topology/virtual/cloudHostResize/' + id.cloudHostId,
    method: 'post',
    data: id
  })
}
// 修改密码
export function cloudHostResetPassword(id) {
  return request({
    url: '/topology/virtual/cloudHostResetPassword/' + id.cloudHostId,
    method: 'post',
    data: id.password
  })
}
// 查询云主机已挂载的云硬盘
export function cloudHostVolume(id) {
  return request({
    url: '/topology/virtual/cloudHostVolumes/' + id,
    method: 'get'
  })
}
// 管理挂载的云硬盘-添加
export function volumesAttach(id) {
  return request({
    url: '/topology/virtual/volumesAttach/' + id,
    method: 'get'
  })
}
// 管理挂载的云硬盘-删除
export function volumesDetach(id) {
  return request({
    url: '/topology/virtual/volumesDetach/' + id,
    method: 'get'
  })
}
// 查询云主机可以挂载的云硬盘列表
export function volumesMountable(id) {
  return request({
    url: '/topology/virtual/volumesMountable/' + id,
    method: 'get'
  })
}
// 查询所有自由下拉框
export function treeListById(id) {
  return request({
    url: process.env.ADMIN_API + '/lable/treeListById/' + id,
    method: 'get'
  })
}
// 根据云主机id获取安全组信息
export function securityGroup(id) {
  return request({
    url: '/accumulate/virtual/securityGroup/' + id,
    method: 'get'
  })
}
// 102042

// 整理中
// 查询快照列表
export function cloudHostSnapshots(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/cloudHostSnapshots/' + id,
    method: 'get'
  })
}
// 查询云主机配额
export function instancesQuotas() {
  return request({
    url: process.env.ADMIN_API + '/images/instancesQuotas',
    method: 'get'
  })
}
// 查询网络配额
export function networksQuotas() {
  return request({
    url: process.env.ADMIN_API + '/images/networksQuotas',
    method: 'get'
  })
}
// 查询云硬盘配额
export function volumesQuotas() {
  return request({
    url: process.env.ADMIN_API + '/images/volumesQuotas',
    method: 'get'
  })
}
// 系统镜像列表
export function getImagesList(id) {
  return request({
    url: process.env.ADMIN_API + '/images/getImagesList',
    method: 'get'
  })
}
// 资源集群列表
export function getAvailabilityZoneList(id) {
  return request({
    url: process.env.ADMIN_API + '/topology/getAvailabilityZoneList',
    method: 'get'
  })
}
// 云主机类型列表
export function getCloudHostType(id) {
  return request({
    url: process.env.ADMIN_API + '/topology/getCloudHostType',
    method: 'get'
  })
}
// 根据模板创建启动云主机
export function cloudHostRun(id) {
  return request({
    url: '/topology/cepoVirtualElement/cloudHostRun/' + id,
    method: 'get'
  })
}
