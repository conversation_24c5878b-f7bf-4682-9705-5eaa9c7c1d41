export default {
  data() {
    return {
      topoOptions: {
        data: null, // 拓扑数据
        status: -1, // 0未启动 1启动中 2启动完成
        readonly: false, // 只读
        editable: true, // 是否可编辑,
        scene: true, // 是否可启动 关闭
        curriculumType: ''
      },
      topoAttrs: null,
      topoStatusTimer: null
    }
  },
  created() {
    if (this.topoOptions.curriculumType != '理论') {
      this.getTopoData()
    }
  },
  methods: {
    getTopoData() {
      // 获取拓扑
      if (!this.businessId) return
      this.getTopoDataAPI(this.businessId).then((ret) => {
        if (ret.code !== 0) {
          this.$message.error('获取拓扑数据失败.')
          return
        }
        if (ret.data.jsonData) {
          this.topoOptions.data = ret.data.jsonData.map(el => JSON.parse(el))
          this.topoAttrs = ret.data.topology
          this.topoOptions.status = 0
          const status = Number(this.topoAttrs.openEnv)
          this.topoOptions.status = status
          console.log(Number(this.topoAttrs.openEnv))
          // 释放中
          if (status === 3) {
            this.topoStopStatus()
          }
          // 启动中
          if (status === 2) {
            this.topoStartStatus()
          }
        }
      })
    },
    // 启动拓扑
    startTopo() {
      this.topoOptions.status = 12
      this.startTopoAPI(this.businessId).then(async(ret) => {
        if (ret.code === 0) {
          this.topoStatusTimer = setInterval(() => {
            this.topoStartStatus()
          }, 5000)
          return
        }
        this.topoOptions.status = 11
        this.$message.error('拓扑资源创建失败.')
      }).catch(() => {
        this.topoOptions.status = 11
      })
    },
    // 释放拓扑
    releaseTopo() {
      this.topoOptions.status = 3
      this.releaseTopoAPI(this.topoAttrs.id).then((ret) => {
        if (ret.code === 0) {
          this.topoStatusTimer = setInterval(() => {
            this.topoStopStatus()
          }, 5000)
          return
        }
        this.$message.error('释放失败.')
        this.topoOptions.status = 1
      }).catch(() => {
        this.topoOptions.status = 1
      })
    },
    // 保存
    saveTopo(cells, topoImage) {
      let businessId = ''
      let groupCode = ''
      if (typeof this.businessId == 'object') {
        businessId = this.businessId.examCode
        groupCode = this.businessId.groupCode
      } else {
        businessId = this.businessId
      }
      this.addTopoDataAPI({
        jsonData: cells.map(el => JSON.stringify(el)),
        businessId: businessId,
        topoImage: topoImage,
        groupCode: groupCode
      }).then((ret) => {
        if (ret.code === 0) {
          this.$message.success('保存成功.')
          this.getTopoData()
          return
        }
        this.$message.error('保存失败.')
      })
    },
    // vnc登录
    vncLogin(templateId) {
      this.vncLoginAPI(templateId, this.topoAttrs.id).then((ret) => {
        if (ret.code !== 0) {
          return
        }
        window.open(`${ret.data}`)
      })
    },
    // 拓扑启动状态
    topoStartStatus() {
      this.topoOptions.status = 2
      this.getTopoStartStatusAPI(this.topoAttrs.id).then((ret) => {
        if (ret.data == 1) {
          this.topoOptions.status = 1
          clearInterval(this.topoStatusTimer)
          return
        }
        this.$message.error('获取拓扑状态失败.')
      }).catch(() => {
      }).finally(() => {
        this.loading = false
      })
    },
    // 拓扑释放状态
    topoStopStatus() {
      this.getTopoStopStatusAPI(this.topoAttrs.id).then((ret) => {
        if (ret.data == 1) {
          this.topoOptions.status = 0
          clearInterval(this.topoStatusTimer)
          return
        }
        this.$message.error('获取拓扑状态失败.')
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
