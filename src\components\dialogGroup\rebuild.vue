<template>
  <div class="dialog">
    <dialogCom v-if="dialogFlag" :title="title" :dialog-visible.sync="dialogFlag" :cancel="_closeDialog"
      :dis-flag="disFlag" :submit="_submit" :hide-cancel="true">
      <template>
        <div class="dialog_item">
          <!-- 不存在快照 -->
          <el-form v-if="!hasDrawer" ref="form" :model="form" :rules="rules" label-width="100px" style="width: 800px;">
            <el-form-item label="镜像名称:" prop="systemImageId">
              <el-select v-model="form.systemImageId" filterable placeholder="请选择镜像名称">
                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="密码:" prop="password">
              <el-input v-model="form.password" show-password maxlength="30" minlength="8" />
            </el-form-item>
            <p style="margin-left: 100px; font-size: 12px">8 - 30个字符，必须同时包含其中三项（大写字母、小写字母、数字、特殊字符）</p>
          </el-form>
          <!-- 存在快照 -->
          <div v-if="hasDrawer" class="hasdrawer">该云主机存在快照，需删除所有快照才能重建云主机！</div>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import { rebuild, getImagesList } from '@/api/sourceLibrary/virtualApi'
import dialogCom from '@/components/dialogGroup/index'
export default {
  components: {
    dialogCom
  },
  props: {
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    },
    title: {
      type: String,
      default: null
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    hasDrawer: {
      type: Boolean
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        // this.$refs.ruleForm.validateField('checkPass');
        const reg =
          /((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,16}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{8,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{8,16}$))/
        if (!reg.test(value)) {
          callback(
            new Error(
              '请输入8-30个字符，且同时包含其中三项（大写字母、小写字母、数字、特殊符号）'
            )
          )
        } else {
          callback()
        }
      }
    }
    return {
      dialogFlag: false,
      disFlag: false,
      form: {
        systemImageId: '',
        password: ''
      },
      options: [],
      rules: {
        systemImageId: {
          required: true,
          message: '请输入镜像名称',
          trigger: 'blur'
        },
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur'
          },
          { validator: validatePass, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dialogVisible: {
      deep: true,
      immediate: true,
      handler(val) {
        this.dialogFlag = val
      }
    },
    hasDrawer: {
      immediate: true,
      handler(val) {
        this.hasDrawer = val
      }
    }
  },
  created() {
    this.dialogFlag = this.dialogVisible
    this.getList()
  },
  methods: {
    // 获取数据列表
    getList() {
      getImagesList()
        .then((res) => {
          if (res.code == 200) {
            this.options = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    _closeDialog() {
      this.dialogFlag = false
      this.$parent.dialogRebuildFlag = false
    },
    _submit() {
      if (this.hasDrawer) {
        this.disFlag = false
        this._closeDialog()
      } else {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.disFlag = true
            if (!this.hasDrawer) {
              const paramas = {
                id: this.list[0].id,
                password: this.form.password,
                systemImageId: this.form.systemImageId
              }
              rebuild(paramas)
                .then((res) => {
                  if (res.code == 200 && res.data.data) {
                    // this.$message.success(res.msg)
                    res.name = '重建' + this.list[0].facilityName + '， '
                    this.$parent.msgList = res
                    this.$emit('msgShow')
                    this.$emit('updateList')
                    this._closeDialog()
                  } else {
                    this.$message.error(res.msg)
                  }
                  this.disFlag = false
                })
                .catch((err) => {
                  this.disFlag = false
                  console.log(err)
                })
            } else {
              this.disFlag = false
              this._closeDialog()
            }
          } else {
            return false
          }
        })
      }
    }
  }
}
</script>
<style scoped="scoped">
.dialog_item {
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: center;
}

.hasdrawer {
  background-color: #d7d7d7;
  width: 80%;
  height: 30px;
  line-height: 30px;
  border: 1px solid rgba(170, 170, 170, 1);
}
</style>
