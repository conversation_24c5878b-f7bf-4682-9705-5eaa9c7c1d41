import request from '@/utils/request'

// 上传
export function uploadImages() {
  return process.env.BASE_API + 'drills/cepoDrillsDefense/upCover'
}
// 查询漏洞复现列表
export function queryHoleRecurrenceList(params) {
  return request({
    url: '/accumulate/holeRecurrence/list',
    method: 'get',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 新增漏洞复现
export function holeRecurrenceAdd(data) {
  return request({
    url: '/accumulate/holeRecurrence/add',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 编辑漏洞复现
export function holeRecurrenceUpdate(params) {
  return request({
    url: `/accumulate/holeRecurrence/update`,
    method: 'put',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 删除漏洞复现
export function holeRecurrenceDelete(id) {
  return request({
    url: `/accumulate/holeRecurrence/${id}`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询漏洞类别是否已存在
export function holeIsExist(data) {
  return request({
    url: '/accumulate/holeRecurrence/isExist',
    method: 'get',
    params: data
  })
}

// 为漏洞复现添加漏洞
export function addloophole(data) {
  return request({
    url: '/accumulate/holeRecurrenceDetails/add',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询漏洞复现的漏洞列表
export function queryLoopHoleList(data) {
  return request({
    url: `/accumulate/holeRecurrence/${data.id}`,
    method: 'get',
    params: {
      pageNumber: data.pageNumber,
      pageSize: data.pageSize,
      bugLevel: data.bugLevel,
      bugName: data.bugName
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取漏洞的场景
export function queryHoleScene(params) {
  return request({
    url: '/topology/cepoSysScene/selectSceneListByType',
    method: 'get',
    params
  })
}


// 查看漏洞的详情
export function seeHoleDetail(id) {
  return request({
    url: `/accumulate/holeRecurrenceDetails/${id}`,
    method: 'get'
  })
}


// 删除漏洞复现里的漏洞
export function deleteHoleRecurrenceDetails(ids) {
  return request({
    url: `/accumulate/holeRecurrenceDetails/delete`,
    method: 'delete',
    params: { ids: ids.join(',') }
  })
}

// 编辑漏洞复现里的漏洞
export function updateHoleRecurrenceDetails(data) {
  return request({
    url: `/accumulate/holeRecurrenceDetails/update`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
