import request from "@/utils/request";

export function getInvoiceList(params) {
  return request({
    url: process.env.ADMIN_API + "/invoiceManage/getInvoiceList",
    method: "post",
    data: params
  });
}

export function openInvoice(params) {
  return request({
    url: process.env.ADMIN_API + "/invoiceManage/openInvoice",
    method: "post",
    data: params
  });
}

export function getInvoice(params) {
  return request({
    url: process.env.ADMIN_API + "/invoiceManage/getInvoice",
    method: "get",
    params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/invoiceManage/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/invoiceManage/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
