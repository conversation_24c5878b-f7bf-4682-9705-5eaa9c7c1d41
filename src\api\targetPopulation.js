import request from "@/utils/request";

//获取行业列表
export function getDataTarget(params) {
  return request({
    url: process.env.ADMIN_API + "/targetCrowd/targetCrowd/list",
    method: "post",
    data: params
  });
}
//新增行业
export function addIndustry(params) {
  return request({
    url: process.env.ADMIN_API + "/targetCrowd/insert",
    method: "post",
    data: params
  });
}
//删除行业
export function deleteIndustry(params) {
  return request({
    url: process.env.ADMIN_API + "/targetCrowd/delete",
    method: "get",
    params
  });
}
//查看行业详情
export function checkIndustryDetail(params) {
  return request({
    url: process.env.ADMIN_API + "/targetCrowd/byId",
    method: "get",
    params
  });
}
//修改行业
export function upDateIndustryDetail(params) {
  return request({
    url: process.env.ADMIN_API + "/targetCrowd/updates",
    method: "post",
    data: params
  });
}
