import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/deleteBatch",
    method: "post",
    data: params
  });
}

export function editLevel(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/editLevel",
    method: "post",
    data: params
  });
}

export function deleteLevelData(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/deleteLevel",
    method: "post",
    data: params
  });
}

export function setStatus(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/setStatus",
    method: "get",
    params: params
  });
}

export function getActiveCareer() {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/getActiveCareer",
    method: "get"
  });
}


export function getLevelMsg(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/getRelationList",
    method: "get",
    params
  });
}

export function editSortFun(params) {
  return request({
    url: process.env.ADMIN_API + "/trainCareer/editSort",
    method: "get",
    params
  });
}
