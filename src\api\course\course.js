import request from '@/utils/request'

// process.env.ADMIN_API
const api = process.env.ADMIN_API

// 训练营列表
export function getTrainCamp(params) {
    return request({
        url: api + '/trainCamp/getCampList',
        method: 'get',
        params
    })
}

// 专栏列表
export function getTrainColumn(params) {
    return request({
        url: api + '/trainColumn/getPageList',
        method: 'post',
        data: params
    })
}

// 视频
export function getPageList(params) {
    return request({
        url: api + '/video/getPageList',
        method: 'post',
        data: params
    })
}

// 获取各个内容的基本信息
export function getListData(params) {
    return request({
        url: api + '/trainModuleContent/getList',
        method: 'get',
        params
    })
}

// 后台获取培训班目录
export function getClassInfo(params) {
    return request({
        url: api + '/trainClass/getClassInfo',
        method: 'get',
        params
    })
}



