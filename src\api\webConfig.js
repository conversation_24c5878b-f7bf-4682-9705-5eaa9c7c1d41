import request from '@/utils/request'

export function getWebConfig(params) {
  return request({
    url: process.env.ADMIN_API + '/webConfig/getWebConfig',
    method: 'get',
    params
  })
}

export function editWebConfig(params) {
  return request({
    url: process.env.ADMIN_API + '/webConfig/editWebConfig',
    method: 'post',
    data: params
  })
}

//《关于我们》排序
export function usSort(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/sort',
    method: 'post',
    data: params
  })
}

// 《关于我们》新增内容
export function addUs(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/add',
    method: 'post',
    data: params
  })
}

// 《关于我们》更新内容
export function editUs(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/edit',
    method: 'post',
    data: params
  })
}


// 《关于我们》批量删除
export function deleteBatchUs(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/deleteBatch',
    method: 'post',
    data: params
  })
}


// 《关于我们》不分页查询一级列表
export function getParentList(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/getParentList',
    method: 'post',
    data: params
  })
}

// 《关于我们》分页查询二级列表
export function getPage(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/getPage',
    method: 'post',
    data: params
  })
}

// 《关于我们》查询详情
export function getInfo(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/getInfo',
    method: 'post',
    data: params
  })
}

// 《关于我们》上下架
export function updateStatus(params) {
  return request({
    url: process.env.ADMIN_API + '/home/<USER>/us/updateStatus',
    method: 'post',
    data: params
  })
}