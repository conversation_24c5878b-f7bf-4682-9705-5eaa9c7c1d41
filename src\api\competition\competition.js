import request from "@/utils/request";

// 添加赛事
export function addCompetition(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/add",
    method: "post",
    data: params
  });
}
// 赛事列表
export function getCompetitionList(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/getList",
    method: "post",
    data: params
  });
}
// 批量删除赛事
export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/deleteBatch",
    method: "post",
    data: params
  });
}
// 查看赛事
export function getInfo(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/getInfo",
    method: "post",
    data: params
  });
}
// 编辑赛事
export function editCompetition(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/edit",
    method: "post",
    data: params
  });
}
// 赛事入口
export function editCompetitionUrl(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/editUrl",
    method: "post",
    data: params
  });
}
// 发布赛事
export function publish(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionManage/release",
    method: "post",
    data: params
  });
}
// 赛事报名战队列表 --团队
export function getApplicationList(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionRegistration/getList",
    method: "post",
    data: params
  });
}

// 赛事报名战队列表 --个人
export function getPersonList(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionRegistration/getPersonList",
    method: "post",
    data: params
  });
} 

// 赛事报名战队列表导出
export function downloadTeamExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionRegistration/downloadTeamExcel",
    method: "post",
    data: params,
    responseType: 'blob'
  });
}

// 赛事报名战队列表导出 -- 个人
export function downloadPersonExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/competitionRegistration/downloadPersonExcel",
    method: "post",
    data: params,
    responseType: 'blob'
  });
}

// 获取赛事列表报名所属行业//行业
export function getDict(params) {
  return request({
    url: process.env.ADMIN_API + "/sysDictData/getListByDictType",
    method: "post",
    params: {
      dictType: params
    }
  });
}