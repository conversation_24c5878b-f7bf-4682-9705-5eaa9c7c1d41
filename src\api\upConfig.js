import request from "@/utils/request";

export function configList(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/config/haveConfigList",
    method: "post",
    data: params
  });
}

export function addConfig(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/config/add",
    method: "post",
    data: params
  });
}

export function editConfig(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/config/edit",
    method: "post",
    data: params
  });
}

export function deleteConfig(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/config/delete",
    method: "get",
    params: params
  });
}

export function equityList() {
  return request({
    url: process.env.ADMIN_API + "/enterprise/configEquity/select",
    method: "post"
  });
}

export function addEquity(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/configEquity/add",
    method: "post",
    data: params
  });
}

export function editEquity(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/configEquity/edit",
    method: "post",
    data: params
  });
}

export function deleteEquity(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/configEquity/delete",
    method: "post",
    params: params
  });
}

export function deleteEquityDetail(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/configEquityDetail/delete",
    method: "get",
    params: params
  });
}

export function deleteEquityTime(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/configEquity/deleteEquityTime",
    method: "post",
    data: params
  });
}
