import request from '@/utils/request'

/**
 * 查询公告列表
 */
export function noticelistAPI(data) {
  return request({
    url: '/admin/notice/searchList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 编辑公告信息
 */
export function updateNoticeAPI(data) {
  return request({
    url: '/admin/notice/updateNotice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 上传封面图片
 */
export function upTeachingCoverAPI(data) {
  return request({
    url: '/admin/portal/upTeachingCover',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 新增公告信息
 */
export function insertNoticeAPI(data) {
  return request({
    url: '/admin/notice/insertNotice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 删除公告信息
 */
export function deleteNoticeAPI(data) {
  return request({
    url: '/admin/notice/deleteNotice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
