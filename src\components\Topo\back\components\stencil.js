import { Stencil } from '@antv/x6-plugin-stencil'

export default function(graph, pLength, vLength) {
  const groups = []
  if (pLength > 0) {
    groups.push({
      name: 'group1',
      title: '物理组件',
      graphWidth: 260,
      graphHeight: pLength * 54 + 60
    })
  }
  if (vLength > 0) {
    groups.push({
      name: 'group2',
      title: '虚拟组件',
      graphWidth: 260,
      graphHeight: vLength * 54 + 60
    })
  }
  return new Stencil({
    title: '组件',
    target: graph,
    search(cell, keyword) {
      return cell.attr('text/text').includes(keyword)
    },
    placeholder: '输入组件名称搜索',
    notFoundText: '未找到设备',
    collapsable: true,
    groups
  })
}
