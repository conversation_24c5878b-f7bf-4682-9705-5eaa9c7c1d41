import request from '@/utils/request'

/**
 * 拓扑物理
 */
export function getPhysicalList() {
  return request({
    url: process.env.ADMIN_API + '/physics/list',
    method: 'get'
  })
}

/**
 * 拓扑虚拟
 */
export function getVirtualList() {
  return request({
    url: process.env.ADMIN_API + '/virtual/list',
    method: 'get'
  })
}

/**
 * VNC
 * @param {*} params
 */
export function vncLoginAPI(params, topoId) {
  return request({
    url: process.env.ADMIN_API + `/tcloud/inner/cloud_host/cloudHostVncV3/${params}?topoId=${topoId}`,
    method: 'get'
  })
}

/**
 * 拓扑启动状态
 * @param {*} params
 */
export function getTopoStartStatusAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getCloudHostStartStatusV3/${params}`,
    method: 'get'
  })
}

/**
 * 拓扑释放状态
 * @param {*} params
 */
export function getTopoStopStatusAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getCloudHostStopStatusV3/${params}`,
    method: 'get'
  })
}


/**
 * 获取拓扑状态
 * @param {*} params
 */
export function getCurriculumTopo(params) {
  return request({
    url: process.env.ADMIN_API + `/curriculum/getCurriculumTopo`,
    method: 'get',
    params
  })
}
