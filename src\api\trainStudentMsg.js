import request from '@/utils/request'

// 学员列表
export function getList(params) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/getStudentList',
    method: 'post',
    data: params
  })
}

// 学员新增
export function add(data) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/addStudentByForm',
    method: 'post',
    data
  })
}

// 学员上下架
export function upDown(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/upDown',
    method: 'get',
    params
  })
}

// 学员删除
export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/deleteStudentBatch',
    method: 'post',
    data: params
  })
}

// 学员导入
export function inputExcel(params) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/inputExcel',
    method: 'get',
    params
  })
}

// 审核
export function checkDocument(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/checkDocument',
    method: 'post',
    data: params
  })
}

// 删除
export function delDocument(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/deleteRecordBatch',
    method: 'post',
    data: params
  })
}

// 指定
export function assignSales(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/assignSales',
    method: 'post',
    data: params
  })
}

// 导出
export function downloadRecordExcel(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/downloadRecordExcel',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 列表
export function getSignList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/getList',
    method: 'post',
    data: params
  })
}

// 报名审核-审批接口
export function approval(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainStudentMsg/checkDocument',
    method: 'get',
    params
  })
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + '/trainStudentMsg/edit',
    method: 'post',
    data: params
  })
}

// 考试审核-审批接口
export function checkExamSignUp(data = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExamSignUp/checkExamSignUp',
    method: 'post',
    data
  })
}

// 删除
export function delExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExamSignUp/deleteBatch',
    method: 'post',
    data: params
  })
}

// 导出
export function downloadExcel(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExamSignUp/downloadExcel',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 导出
export function downSingUpRelationList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainSignUp/downSingUpRelationList',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 列表
export function trainVerifySignUpPageList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainVerifySignUp/pageList',
    method: 'post',
    data: params
  })
}
// 二维码
export function trainVerifySignGetCode(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainVerifySignUp/getCode',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
// 审核
export function trainVerifySignEditStatus(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainVerifySignUp/editStatus',
    method: 'post',
    data: params
  })
}
// 下载
export function downloadRecordExcelSignUp(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainVerifySignUp/downloadRecordExcel',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
// 报名类型
export function getListByDictType(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/sysDictData/getListByDictType?dictType=sign_up_type',
    method: 'post',
    data: params
  })
}

// 获证证书列表
export function trainCertificateUserPageList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificateUser/pageList',
    method: 'post',
    data: params
  })
}

// 续证
export function recertification(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificateUser/recertification',
    method: 'post',
    data: params
  })
}

// 获证证书列表导出
export function trainCertificateUserDownExcel(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificateUser/downExcel',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 获证人员批量导出
export function batchDownload(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificateUser/batchDownload',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 考试-获取证书编号
export function getNoConfig(params = {}) {
  return request({
    url: process.env.ADMIN_API + `/trainCertificateUser/getNoConfig`,
    method: 'get',
    params
  })
}

// 考试-获取证书图片
export function downPicture(params = {}) {
  return request({
    url: process.env.ADMIN_API + `/trainCertificateUser/downPicture`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 学生列表模版下载
export function downloadTemplate(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/downloadTemplate',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

