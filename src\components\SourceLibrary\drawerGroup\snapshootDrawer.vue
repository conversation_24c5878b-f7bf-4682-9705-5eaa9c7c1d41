<template>
  <div class="snapshoot">
    <drawerGroup v-if="drawerFlag" :drawer="drawerFlag" title="快照" @closeDrawer="close">
      <template>
        <div class="snapshoot_content">
          <slide-tab :tab-arr="tabs" :active-index="activeName" @tabClick="handleClick"/>
          <!-- 内容区 -->
          <div class="detail_chain">
            <!-- 详情 -->
            <div v-loading="loading" v-if="activeName == '0'" class="snapshoot_detail">
              <el-form ref="snapshootForm" :model="formData" label-width="120px">
                <el-form-item label="设备名称:">{{ formData.facilityName }}</el-form-item>
                <el-form-item label="设备图标:">
                  <img :src="formData.facilityIcon" alt width="130" height="130" >
                </el-form-item>
                <el-form-item label="设备分类:">{{ formData.facilityCategoryName }}</el-form-item>
                <el-form-item label="创建时间:">{{ formatDate(formData.createTime) }}</el-form-item>
                <!-- <el-form-item label="系统镜像:">{{formData.systemImageName}}</el-form-item> -->
                <el-form-item label="设备描述:">{{ formData.facilityDescription }}</el-form-item>
                <el-form-item label="资源集群:">{{ formData.clusterName }}</el-form-item>
                <el-form-item label="云主机类型:">{{ formData.specificationName }}</el-form-item>
                <el-form-item label="类型:">云主机</el-form-item>
                <el-form-item label="CPU:">{{ formData.vcpus ? formData.vcpus : '' }}</el-form-item>
                <el-form-item
                  label="内存:"
                >{{ formData.ram ? formData.ram > 1024 ? formData.ram/1024 : formData.ram : '' }}</el-form-item>
                <!-- <el-form-item label="系统盘类型:">{{formData.diskTypeName}}</el-form-item> -->
                <!-- <el-form-item label="系统盘大小:">{{formData.diskSize}}</el-form-item> -->
                <!-- <el-form-item label="网络:">{{formData.i3NetworkVos}}</el-form-item> -->
                <!-- <el-form-item label="安全组:">{{formData.securityGroupName}}</el-form-item> -->
                <!-- <el-form-item label="登录方式:">密码</el-form-item>
                <el-form-item label="用户名:">root</el-form-item>
                <el-form-item label="密码:">********</el-form-item>-->
                <!-- <el-form-item label="数量:">{{formData.count}}</el-form-item> -->
              </el-form>
            </div>
            <!-- 安全组 -->
            <div v-loading="loading" v-if="activeName == '1'" class="snapshot_chain">
              <el-form ref="snapshootForm" :model="formData" label-width="120px">
                <el-form-item
                  v-if="formData.securityGroup && formData.securityGroup.name"
                  :label="formData.securityGroup.name"
                >
                  <div
                    v-for="(item, index) in formData.securityGroup.rules"
                    :key="index"
                  >{{ item.info }}</div>
                </el-form-item>
              </el-form>
            </div>
            <!-- 网络 -->
            <div v-loading="loading" v-if="activeName == '2'" class="snapshot_chain">
              <el-form ref="snapshootForm" :model="drawerData" label-width="120px">
                <el-form-item v-if="drawerData.address && drawerData.address.length" label="IP地址:">
                  <div v-for="(item, index) in drawerData.address" :key="index">
                    <span style="margin-right: 20px">{{ item.firstName }}</span>
                    <span>{{ item.secondName }}</span>
                  </div>
                </el-form-item>
              </el-form>
            </div>

            <!-- 快照链 -->
            <div v-loading="loading" v-if="activeName == '3'" class="snapshot_chain">
              <div class="add_snapshot">
                <img src="@/assets/img/camera.png" alt width="40" height="40" >
                <el-button plain class="add_button" @click="createSnapshot">
                  <svg-icon icon-class="add" class-name="card-panel-icon" />
                  <span style="margin-left: 20px">创建快照</span>
                </el-button>
              </div>
              <div
                v-for="(item, index) in formData.snapshotList"
                :key="index"
                class="snapshot_list"
              >
                <img src="@/assets/img/camera.png" alt width="40" height="40" >
                <div class="right_detail">
                  <div class="title">
                    <div>{{ item.name }}</div>
                    <div class="buttons">
                      <el-button
                        title="恢复"
                        type="primary"
                        icon="el-icon-s-promotion"
                        circle
                        @click="_recover(item)"
                      />
                      <el-button
                        title="编辑"
                        type="primary"
                        icon="el-icon-edit"
                        circle
                        @click="_edit(item)"
                      />
                      <el-button
                        title="删除"
                        type="primary"
                        icon="el-icon-close"
                        circle
                        @click="_delete(item)"
                      />
                    </div>
                  </div>
                  <div v-if="item.openFlag" class="detail">
                    <el-form ref="snapshootForm" :model="item" label-width="120px">
                      <el-form-item label="ID:">{{ item.id }}</el-form-item>
                      <el-form-item label="描述:">{{ item.description }}</el-form-item>
                    </el-form>
                  </div>
                  <div class="openOrClose">
                    <el-button v-if="!item.openFlag" type="text" @click="openDetail(index)">
                      展开详情
                      <i class="el-icon-caret-right"/>
                    </el-button>
                    <el-button v-else type="text" @click="openDetail(index)">
                      隐藏详情
                      <i class="el-icon-caret-bottom"/>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </drawerGroup>
  </div>
</template>
<script>
import {
  getVirtual,
  cloudHostSnapshots,
  recoverSnapshots,
  deleteSnapshots,
  securityGroup
} from '@/api/sourceLibrary/virtualApi'
import { formatDate } from '@/utils/index'
import slideTab from '@/components/SourceLibrary/slideTab/index.vue'
import drawerGroup from '@/components/SourceLibrary/drawerGroup/index'
export default {
  name: 'SnapshootDrawer',
  components: {
    drawerGroup,
    slideTab
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    tabsNum: {
      type: Number,
      default: 0
    },
    drawerData: {
      type: Object,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: {}
    }
  },
  data() {
    return {
      formatDate: '', // 格式化时间
      tabs: ['详情'],
      activeName: -1,
      loading: false,
      formData: {
        facilityName: '', // 设备名称
        facilityIcon: '', // 设备图标
        facilityCategory: '', // 设备类型
        systemImageName: '', // 系统镜像
        facilityDescription: '', // 设备描述
        clusterName: '', // 资源集群
        specificationName: '', // 云主机类型
        cloudCategory: '', // 类型
        CPU: '', // CPU
        memory: '', // 内存
        diskTypeName: '', // 系统盘类型
        diskSize: '', // 系统盘大小
        i3NetworkVos: '', // 网络
        securityGroupName: '', // 安全组
        loginType: '', // 登录方式
        userName: '', // 用户名
        passWord: '', // 密码
        count: '' // 数量
      },
      snapshotList: [], // 快照列表
      flagList: [false, false, false],
      drawerFlag: false
    }
  },
  watch: {
    activeName: {
      handler(newVal, oldVal) {
        if (newVal != oldVal) {
          this.getList()
        }
      }
    }
  },
  created() {
    this.activeName = this.tabsNum
    this.formatDate = formatDate
    // this.getList()
  },
  methods: {
    // 编辑快照
    _edit(item) {
      this.$parent.editInfo = item
      this.$parent.drawerDialog = true
      this.$parent.openDrawer = false
    },
    async getList() {
      this.drawerFlag = true
      this.loading = true
      if (this.activeName == '0') {
        await getVirtual(this.drawerData.id)
          .then((res) => {
            if (res.code == 200 || res.code == 0) {
              this.formData = Object.assign(res.data, this.drawerData)
              // this.formData = [...res.data, ...this.drawerData]
              console.log('this.formData', this.formData)
              // this.drawerFlag = true
            } else {
              this.$message.error(res.msg)
            }
            this.loading = false
          })
          .catch((err) => {
            console.log(err)
            this.loading = false
          })
      } else if (this.activeName == '1') {
        await securityGroup(this.drawerData.id)
          .then((res) => {
            if (res.code == 200) {
              this.formData.securityGroup = res.data[0]
            } else {
              this.$message.error(res.msg)
            }
            this.loading = false
          })
          .catch((err) => {
            console.log(err)
            this.loading = false
          })
      } else if (this.activeName == '2') {
        this.drawerData.address = []
        const addresses = JSON.parse(this.drawerData.addresses)
        console.log(
          'this.formData.addresses',
          JSON.parse(this.drawerData.addresses)
        )
        if (addresses && addresses.length) {
          for (const i in addresses) {
            if (addresses[i]) {
              for (const j in addresses[i][Object.keys(addresses[i])[0]]) {
                if (addresses[i][Object.keys(addresses[i])[0]][j]) {
                  if (addresses[i][Object.keys(addresses[i])[0]][j]) {
                    if (
                      addresses[i][Object.keys(addresses[i])[0]][j][
                        Object.keys(
                          addresses[i][Object.keys(addresses[i])[0]][j]
                        )[0]
                      ]
                    ) {
                      const obj = {
                        firstName: Object.keys(addresses[i])[0],
                        secondName:
                          Object.keys(
                            addresses[i][Object.keys(addresses[i])[0]][j]
                          )[0] +
                          ' / ' +
                          addresses[i][Object.keys(addresses[i])[0]][j][
                            Object.keys(
                              addresses[i][Object.keys(addresses[i])[0]][j]
                            )[0]
                          ].fixedip +
                          ' / ' +
                          addresses[i][Object.keys(addresses[i])[0]][j][
                            Object.keys(
                              addresses[i][Object.keys(addresses[i])[0]][j]
                            )[0]
                          ].mac_address +
                          ' / ' +
                          addresses[i][Object.keys(addresses[i])[0]][j][
                            Object.keys(
                              addresses[i][Object.keys(addresses[i])[0]][j]
                            )[0]
                          ].floating
                      }
                      // console.log('obj',obj);
                      this.drawerData.address.push(obj)
                    }
                  }
                }
              }
            }
          }
        }
        this.loading = false
      } else {
        await cloudHostSnapshots(this.drawerData.id)
          .then((res) => {
            if (res.code == 200) {
              this.formData = this.drawerData
              this.formData.snapshotList = res.data
              for (const item of this.formData.snapshotList) {
                item.openFlag = false
              }
              console.log(this.formData)
            } else {
              this.$message.error(res.msg)
            }
            this.loading = false
          })
          .catch((err) => {
            console.log(err)
            this.loading = false
          })
      }
    },
    openDetail(index) {
      this.formData.snapshotList[index].openFlag =
        !this.formData.snapshotList[index].openFlag
      this.$forceUpdate()
    },
    handleClick(val) {
      this.activeName = val
    },
    // 创建快照
    createSnapshot() {
      this.$parent.editInfo = null
      this.$parent.drawerDialog = true
      this.$parent.openDrawer = false
    },
    // 恢复快照
    async _recover(item) {
      this.loading = true
      const id = this.drawerData.id + '/' + item.id
      await recoverSnapshots(id)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
          this.getList()
        })
        .catch((err) => {
          console.log(err)
          this.loading = false
        })
    },
    // 删除快照
    async _delete(item) {
      this.loading = true
      // let id = this.drawerData.id + '/' + item.id
      await deleteSnapshots(item.id)
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
          this.loading = false
          this.getList()
        })
        .catch((err) => {
          console.log(err)
          this.loading = false
        })
    },
    close() {
      this.$parent.openDrawer = false
      this.drawerFlag = false
    }
  }
}
</script>
<style lang="scss" scoped>
.snapshoot {
  &::v-deep {
    .el-drawer__open {
      height: 80%;
    }
    .el-drawer__container {
      top: 20%;
    }
    .snapshoot_content {
      .detail_chain {
        padding: 20px !important;
        box-sizing: border-box;
        height: 72vh;
        overflow-y: auto;
        .snapshot_chain {
          .add_snapshot {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            margin-bottom: 20px;
            .add_button {
              width: calc(100% - 80px);
              height: 100px;
              margin: 0 20px;
              font-size: 26px;
            }
          }
          .snapshot_list {
            width: 100%;
            display: flex;
            // flex-flow: column;
            margin-bottom: 20px;
            .right_detail {
              width: calc(100% - 80px);
              margin: 0 20px;
              background: #f5f5f5;
              border: 1px solid #b5b5b5;
              padding: 10px;
              box-sizing: border-box;
              display: flex;
              flex-flow: column;
              border-radius: 4px;
              .title {
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
              }
            }
          }
        }
      }
    }
  }
}
</style>
