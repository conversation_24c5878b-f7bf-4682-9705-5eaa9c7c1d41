import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/customerCase/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/customerCase/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/customerCase/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/customerCase/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/customerCase/deleteBatch",
    method: "post",
    data: params
  });
}

export function setStatus(params) {
  return request({
    url: process.env.ADMIN_API + "/customerCase/setStatus",
    method: "post",
    data: params
  });
}
