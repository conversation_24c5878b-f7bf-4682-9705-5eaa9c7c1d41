<template>
  <div class="paper-container">
    <template>
      <el-form :inline="true" :model="paperForm" class="demo-form-inline">
        <el-form-item label="试卷名称">
          <el-input v-model="paperForm.paperName" size="small" placeholder="试卷名称" @change="search" />
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-table
      highlight-current-row
      :header-cell-style="{ textAlign: 'center' }"
      :data="paperList"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      ref="multipleTable"
      :row-key="getRowKey"
    >
      <el-table-column type="selection" width="55" :reserve-selection="true">
      </el-table-column>
      <el-table-column
        align="center"
        prop="uid"
        label="试卷ID"
        show-overflow-tooltip
        width="180"
      >
      </el-table-column>
      <el-table-column align="center" prop="examName" label="试卷名称">
      </el-table-column>
      <el-table-column
        align="center"
        prop="picturePath"
        label="试卷封面"
        width="180"
      >
        <template slot-scope="scope">
          <el-image
            :preview-src-list="[scope.row.picturePath]"
            style="width: 100px; height: 100px"
            :src="scope.row.picturePath"
            fit="fill"
          ></el-image>
        </template>
      </el-table-column>
    </el-table>
    <div
      :style="{
        marginTop: '20px',
        display: 'flex',
        justifyContent: 'center',
      }"
    >
      <el-pagination
        @size-change="selectPaperSizeChange"
        @current-change="selectPaperCurrentPageChange"
        background
        layout="prev, pager, next"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :total="total"
      >
      </el-pagination>
    </div>
    <div
      :style="{
        marginTop: '20px',
        display: 'flex',
        justifyContent: 'flex-end',
      }"
    >
      <el-button @click="cancelSelectPaper">取消</el-button>
      <el-button type="primary" @click="realSelectPaper">确定</el-button>
    </div>
  </div>
</template>
<script>
import { getListData } from "@/api/course/course";
export default {
  data() {
    return {
      paperList: [],
      currentPage: 1,
      pageSize: 3,
      total: 0,
      multipleSelection: [],
      target: [],
      paperForm: {}
    };
  },
  mounted() {
    this.initPaperInfo();
  },
  methods: {
    Popup(examList) {
      if (examList.length) {
        this.target = examList;
      } else {
        this.target = [];
      }
      this.currentPage = 1;
      this.initPaperInfo(this.target);
      this.statusBackShow(this.target)
    },
    // 取消试卷选择
    cancelSelectPaper() {
      this.$emit("cancelSelectPaper");
    },
    // 确定试卷选择
    realSelectPaper() {
      //对象去重
      let obj = {};
      this.multipleSelection = this.multipleSelection.reduce((item, next) => {
        obj[next.uid] ? "" : (obj[next.uid] = true && item.push(next));
        return item;
      }, []);
      console.log("this.multipleSelectionCom", this.multipleSelection);
      this.$emit("realSelectPaper", this.multipleSelection);
    },
    selectPaperSizeChange(val) {
      this.pageSize = val;
      this.initPaperInfo(this.target);
    },
    selectPaperCurrentPageChange(val) {
      this.currentPage = val;
      this.initPaperInfo(this.target);
    },
    // 多选(选择试卷)
    handleSelectionChange(val) {
      console.log(val);
      this.multipleSelection = val;
    },
    getRowKey(row) {
      return row.uid;
    },
    statusBackShow(examList) {
      this.$refs.multipleTable.clearSelection();
      this.$nextTick(() => {
        if (examList && examList.length) {
          console.log("examList", examList);
          examList.map((item) => {
            this.multipleSelection.push({
              uid: item.moduleUid,
              examName: (item.course && item.course.name) || item.name,
              picturePath: (item.course? item.course.picturePath:'')||item.picturePath,
            });
          });
          // 取出组件实例的数据
          const { paperList } = this;
          this.multipleSelection.forEach((item) => {
            let targetObj = paperList.find(paperItem => {
              return item.uid == paperItem.uid;
            });
            if (targetObj) {
              this.$refs.multipleTable.toggleRowSelection(targetObj, true);
            } else {
              this.$refs.multipleTable.toggleRowSelection({
                uid: item.uid,
                examName: item.examName,
                picturePath: item.picturePath
              }, true);
            }
          });
        }
      });
    },
    // 加载试卷信息
    async initPaperInfo(examList) {
      //对象去重
      let params = {
        type: 0,
        examType: 0,
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        name: this.paperForm.paperName
      };
      let result = await getListData(params);
      console.log("试卷信息", result);
      if (result.code == this.$ECode.SUCCESS) {
        this.total = result.data.total;
        this.paperList = result.data.records;
        // this.statusBackShow(examList);
      } else {
        this.$commonUtil.message.error(res.message);
      }
    },
    search() {
      this.currentPage = 1;
      this.initPaperInfo()
    }
  },
};
</script>
<style lang="scss" scoped>
</style>