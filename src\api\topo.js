import request from '@/utils/request'

/**
 * 拓扑物理
 */
export function getPhysicalList() {
  return request({
    url: process.env.ADMIN_API + '/physics/list',
    method: 'get'
  })
}

/**
 * 拓扑物理新
 */
export function getPhysicalListNew() {
  return request({
    url: process.env.ADMIN_API + '/physics/listNew',
    method: 'get'
  })
}

/**
 * 拓扑虚拟
 */
export function getVirtualList() {
  return request({
    url: process.env.ADMIN_API+'/virtual/list',
    method: 'get'
  })
}

/**
 * 保存拓扑
 */
export function addTopoData(data) {
  return request({
    url: process.env.ADMIN_API + '/cepoSysTopology/addCourseTopo',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 拓扑启动状态
 * @param {*} params
 */
export function getTopoStartStatusAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getCloudHostStartStatusV3/${params}`,
    method: 'get'
  })
}

/**
 * 拓扑释放状态
 * @param {*} params
 */
export function getTopoStopStatusAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getCloudHostStopStatusV3/${params}`,
    method: 'get'
  })
}

/**
 * 启动拓扑
 */
export function startTopo(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/startCourseTopo/${params}`
  })
}

/**
 * 释放拓扑
 */
export function releaseTopo(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/releaseCourseTopo/${params}`
  })
}

/**
 * 获取拓扑
 * @param {*} params
 */
export function getTopoDataAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getCourseTopo/${params}`,
    method: 'get'
  })
}

/**
 * VNC
 * @param {*} params
 */
export function vncLoginAPI(data) {
  return request({
    url: process.env.ADMIN_API + `/tcloud/inner/cloud_host/cloudHostVncV3/${data.templateId}?topoId=${data.topoId}`,
    method: 'get'
  })
}

/**
 * 拓扑状态
 * @param {*} params
 */
export function getTopoStatusAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTogetPhysicalListpology/getCloudHostStatus/${params}`,
    method: 'get'
  })
}


/**
 * 保存任务拓扑
 */
export function addTaskTopoDataAPI(data) {
  return request({
    url: process.env.ADMIN_API + '/cepoSysTopology/addModelTaskTopo',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 获取任务拓扑
 * @param {*} params
 */
export function getTaskTopoDataAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getModelTaskTopo/${params}`,
    method: 'get'
  })
}

/**
 * 启动任务拓扑
 */
export function startTaskTopoAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/startModelTaskTopo/${params}`
  })
}




/**
 * 释放任务拓扑资源
 */
export function releaseTaskTopoAPI(params) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/releaseModelTaskTopo/${params}`
  })
}

/**
 * 获取小组操作的任务拓扑
 * @param {*} params
 */
export function getTaskTopoAPI(data) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/getTaskTopo/${data.examCode}?groupCode=${data.groupCode}`,
    method: 'get'
  })
}

/**
 * 启动小组任务拓扑
 */
export function startGroupTaskTopoAPI(data) {
  return request({
    url: process.env.ADMIN_API + `/cepoSysTopology/startTaskTopo/${data.examCode}?groupCode=${data.groupCode}`,
    method: 'get'
  })
}

/**
 * 释放小组任务拓扑资源
 */
export function releaseGroupTaskTopoAPI(data) {
  return request({
    url: `/topology/cepoSysTopology/releaseTaskTopo/${data.examCode}?groupCode=${data.groupCode}`,
    method: 'get'
  })
}

/**
 * 保存小组任务拓扑
 */
export function addTaskTopoAPI(data) {
  return request({
    url: '/topology/cepoSysTopology/addTaskTopo',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 释放小组任务拓扑资源
 */
export function getCloudHostStopStatusAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/getCloudHostStopStatus/${params}`,
    method: 'get'
  })
}

/**
 * 获取用户所操作的课程拓扑
 */
export function getCourseTopoAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/getCourseTopo/${params}`,
    method: 'get'
  })
}


/**
 * 获取拓扑倒计时
 */
export function getCourseSurplusOpeaTimeAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/getCourseSurplusOpeaTime/${params}`,
    method: 'get'
  })
}

/**
 * 重置拓扑倒计时
 */
export function updateCourseSurplusOpeaTimeAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/updateCourseSurplusOpeaTime/${params}`,
    method: 'get'
  })
}

/**
 * 获取用户操作的试卷考题拓扑
 */
export function getExamTopoAPI(params) {
  return request({
    url: `topology/cepoSysTopology/getExamTopo/${params}`,
    method: 'get'
  })
}

/**
 * 启动用户操作的试卷考题拓扑
 */
export function startExamTopoAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/startExamTopo/${params}`,
    method: 'get'
  })
}

/**
 * 释放考试拓扑资源
 */
export function releaseExamTopoAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/releaseExamTopo/${params}`
  })
}

// 网络列表
export function getNetworks(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/getNetworks',
    method: 'get'
  })
}
// 添加路由器
export function addRouterV3(data) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/routerV3',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
// 添加交换机
export function addSwitchV3(data) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/switchV3',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
// 删除路由器
export function removeRouterV3(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/routerV3/remove?routerId=' + id,
    method: 'get'
  })
}
// 删除交换机
export function removeSwitchV3(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/switchV3/remove?switchId=' + id,
    method: 'get'
  })
}

// 查询路由子网网络列表
export function getSubnetListV3(id) {
  return request({
    url: '/topology/tcloud/inner/cloud_host/getSubnetListV3',
    method: 'get'
  })
}
