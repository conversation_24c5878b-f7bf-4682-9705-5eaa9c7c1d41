import request from '@/utils/request'

// 获取选题策略
export function getSelectionStrategy(param) {
    return request({
        url: process.env.ADMIN_API + '/trainCareer/titleStrategy/page',
        method: 'post',
        data: param
    })
}

// 批量删除
export function strategyDelete(param) {
    return request({
        url: process.env.ADMIN_API + '/trainCareer/titleStrategy/delete',
        method: 'post',
        data: param
    })
}

// 批量上下架
export function strategymanualAudit(params) {
    return request({
        url: process.env.ADMIN_API + `/trainCareer/titleStrategy/listStatus?listStatus=${params.listStatus}`,
        method: 'post',
        data: params.uids
    })
}
// 获取各难度总题目数
export function getStarLevelCountV2(params) {
    return request({
        url: process.env.ADMIN_API + `/train/titleV2/getStarLevelCount`,
        method: 'post',
        data: params
    })
}
// 新增策略
export function addTopic(params) {
    return request({
        url: process.env.ADMIN_API + `/trainCareer/titleStrategy/add`,
        method: 'post',
        data: params
    })
}

// 新增策略
export function editTopic(params) {
    return request({
        url: process.env.ADMIN_API + `/trainCareer/titleStrategy/update`,
        method: 'post',
        data: params
    })
}