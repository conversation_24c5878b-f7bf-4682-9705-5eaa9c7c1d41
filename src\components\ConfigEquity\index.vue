<!-- create by lbin -->
<template>
  <div class="content">
    <div class="equity">
      <div class="equity-main-header">
        <span>{{ isEdit ? "编辑权益" : "权益列表" }}</span>
        <div>
          <el-button
            class="el-button-width"
            v-if="isEdit"
            @click="isEdit = false"
            >取消</el-button
          >
          <el-button
            class="el-button-width"
            v-if="isEdit"
            @click="editEquitys()"
            type="primary"
          >
            保存
          </el-button>
          <el-button
            class="el-button-width"
            v-if="!isEdit"
            @click="openEquity(false)"
            >编辑</el-button
          >
          <el-button
            class="el-button-width"
            @click="openEquity(true)"
            type="primary"
          >
            添加权益
          </el-button>
        </div>
      </div>
      <div
        class="equity-main"
        v-for="(equityItem, equityIndex) in versionEquityList"
        :key="equityIndex"
        :draggable="isEdit"
        @dragstart="dragStart($event, equityItem.uid)"
        @dragover.prevent="dragOver($event)"
        @dragenter="dragEnter($event, equityItem)"
        @dragend="dragEnd($event)"
      >
        <div v-if="!isEdit" class="title">{{ equityItem.name }}</div>
        <div v-else class="equity-input">
          <el-input
            class="equity-input-header"
            style="width: 120px"
            v-model="equityItem.name"
            placeholder="请输入内容"
          ></el-input>
          <i class="el-icon-error" @click="deleteEquity(equityItem)"></i>
        </div>
        <div
          class="tab-item"
          v-for="(childItem, childIndex) in equityItem.equityDetailDtoList"
          :key="childIndex"
          :draggable="isEdit"
          @dragstart="handleDragStart($event, childItem.uid)"
          @dragover.prevent="handleDragOver($event)"
          @dragenter="handleDragEnter($event, childItem, equityItem.uid)"
          @dragend="handleDragEnd($event)"
        >
          <div v-if="!isEdit">
            <span>{{ childItem.detailName }}</span>
            <span
              v-if="
                childItem.flagList != undefined && childItem.flagList != null
              "
            >
              <span
                class="config-time-list"
                v-for="(timeItem, timeIndex) in childItem.flagList"
                :key="timeIndex"
              >
                {{ timeItem.remark }}
              </span>
            </span>
          </div>
          <div v-else class="equity-input">
            <el-input
              style="width: 140px"
              v-model="childItem.detailName"
              placeholder="请输入内容"
            ></el-input>
            <i
              class="el-icon-error"
              @click="deleteEquityItem(childItem.uid)"
            ></i>

            <div
              style="display: inline-block"
              v-if="
                childItem.flagList != undefined && childItem.flagList != null
              "
            >
              <span
                class="config-time-content"
                v-for="(timeItem, timeIndex) in childItem.flagList"
                :key="timeIndex"
              >
                <el-input
                  class="config-time-input"
                  size="small"
                  style="width: 140px"
                  v-model="timeItem.remark"
                  placeholder="请输入时间段"
                ></el-input>
                <i
                  class="el-icon-error"
                  @click="deleteEquityTime(timeItem)"
                ></i>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :title="isEquity ? '添加权益' : '编辑权益'"
      :visible.sync="addEquityDialog"
      :width="isEquity ? '700px' : '60%'"
    >
      <el-form
        ref="addEquityForm"
        :rules="addEquityRules"
        :model="addEquityForm"
        label-width="80px"
      >
        <el-form-item label="类目" prop="eauityType">
          <div class="add-equity-form-type">
            <div style="width: 60%">
              <el-input
                v-model="addEquityForm.eauityType"
                placeholder="添加类目"
              ></el-input>
            </div>

            <span style="margin: 0 20px; color: #bbbbbb">或</span>
            <el-select
              v-model="addEquityForm.eauityType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(equityItem, equityIndex) in versionEquityList"
                :key="equityIndex"
                :label="equityItem.name"
                :value="equityItem.name"
              >
              </el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="名称" prop="detailName">
          <el-input
            v-model="addEquityForm.detailName"
            placeholder="权益名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="addEquityForm.sort" placeholder="排序"></el-input>
        </el-form-item>
        <el-form-item label="是否文字">
          <el-radio-group v-model="isSetTime">
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="isSetTime" label="权益内容">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: flex-start;
            "
          >
            <el-input
              class="price-input"
              v-model="item.value"
              v-for="(item, index) in addEquityForm.configTimeList"
              :key="index"
              placeholder="输入详情"
            ></el-input>
            <el-button
              v-if="addEquityForm.configTimeList.length < 3"
              @click="addTime"
              circle
              icon="el-icon-plus"
            ></el-button>
            <el-button
              v-if="addEquityForm.configTimeList.length > 1"
              @click="reduceTime"
              circle
              icon="el-icon-minus"
            ></el-button>
          </div>
        </el-form-item>
        <!-- <el-form-item label="描述">
          <div></div>
          <el-input
            v-model="addEquityForm.describe"
            placeholder="输入描述"
          ></el-input>
        </el-form-item> -->
      </el-form>

      <div class="dialog-button">
        <el-button class="el-button-width" @click="addEquityDialog = false">
          关 闭
        </el-button>
        <el-button class="el-button-width" type="primary" @click="submitEquity">
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addEquity,
  deleteEquity,
  deleteEquityDetail,
  deleteEquityTime,
  editEquity,
  equityList
} from "@/api/upConfig";

export default {
  data() {
    return {
      isSetTime: false,
      versionEquityList: [],
      isEdit: false,
      isEquity: false,
      equityForm: {},
      addEquityForm: {
        configTimeList: [{ value: "" }]
      },
      tabFormRules: [],
      addEquityDialog: false,
      addEquityRules: {
        detailName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        eauityType: [
          {
            required: true,
            message: "类目不能为空",
            trigger: ["blur", "change"]
          }
        ],
        sort: [
          { required: true, message: "排序不能为空", trigger: "blur" },
          { pattern: /^[0-9]\d*$/, message: "排序字段只能为自然数" }
        ]
      },
      dragging: null,
      equityDragging: null
    };
  },
  created() {
    // 权益列表
    this.getEquityList();
    // 获取字典时间
    this.getEquityTime();
  },
  methods: {
    // 权益拖动
    dragStart(e, uid) {
      this.equityDragging = uid;
    },
    dragEnd(e) {
      this.equityDragging = null;
    },
    //首先把div变成可以放置的元素，即重写dragenter/dragover
    dragOver(e) {
      e.dataTransfer.dropEffect = "move"; // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
    },
    dragEnter(e, item) {
      e.dataTransfer.effectAllowed = "move"; //为需要移动的元素设置dragstart事件
      if (item.uid === this.equityDragging) {
        return;
      }
      let list = JSON.parse(JSON.stringify(this.versionEquityList));

      const newItems = list.map(value => value.uid);
      const src = newItems.indexOf(this.equityDragging);
      const dst = newItems.indexOf(item.uid);
      newItems.splice(dst, 0, ...newItems.splice(src, 1));
      // console.log(newItems, list);

      let newList = [];
      newItems.forEach(oneItem => {
        list.forEach(twoItem => {
          if (twoItem.uid == oneItem) {
            newList.push(twoItem);
          }
        });
      });
      this.versionEquityList = newList;
      // console.log(newList);
      // this.versionEquityList.forEach(map => {
      //   if (map.uid == item.uid) {
      //     map = newList;
      //   }
      // });
    },

    // 权益详情拖动
    handleDragStart(e, uid) {
      this.dragging = uid;
    },
    handleDragEnd(e) {
      this.dragging = null;
    },
    //首先把div变成可以放置的元素，即重写dragenter/dragover
    handleDragOver(e) {
      e.dataTransfer.dropEffect = "move"; // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
    },
    handleDragEnter(e, item, uid) {
      e.dataTransfer.effectAllowed = "move"; //为需要移动的元素设置dragstart事件
      if (item.uid === this.dragging) {
        return;
      }
      let list = JSON.parse(
        JSON.stringify(
          this.versionEquityList.find(map => map.uid == uid).equityDetailDtoList
        )
      );

      const newItems = list.map(value => value.uid);
      const src = newItems.indexOf(this.dragging);
      const dst = newItems.indexOf(item.uid);
      newItems.splice(dst, 0, ...newItems.splice(src, 1));
      // console.log(newItems, list);

      let newList = [];
      newItems.forEach(oneItem => {
        list.forEach(twoItem => {
          if (twoItem.uid == oneItem) {
            newList.push(twoItem);
          }
        });
      });
      // console.log(newList);
      this.versionEquityList.forEach(map => {
        if (map.uid == uid) {
          map.equityDetailDtoList = newList;
        }
      });
    },

    getEquityTime() {
      // getSysDictDataList
    },

    getEquityList() {
      equityList().then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.versionEquityList = response.data;
        } else {
          this.$commonUtil.message.error(response.message);
        }
      });
    },
    openEquity(flag) {
      if (flag) {
        this.addEquityDialog = true;
        this.isSetTime = false;
        this.addEquityForm.configTimeList = [{ value: "" }];
      } else {
        this.isEdit = true;
      }

      this.isEquity = flag;
    },
    submitEquity() {
      this.$refs.addEquityForm.validate(valid => {
        if (!valid) return;
        var params = {};
        let equityParam = this.versionEquityList.find(
          item => this.addEquityForm.eauityType == item.name
        );
        if (equityParam != undefined) {
          params.name = equityParam.name;
          params.uid = equityParam.uid;
        } else {
          params.name = this.addEquityForm.eauityType;
        }
        params.equityDetailList = [
          {
            detailName: this.addEquityForm.detailName,
            sort: this.addEquityForm.sort,
            configTimeList: this.isSetTime
              ? this.addEquityForm.configTimeList
                  .filter(item => {
                    if (item.value != "") {
                      return item.value;
                    }
                  })
                  .map(i => {
                    return {
                      remark: i.value
                    };
                  })
              : null
          }
        ];
        addEquity(params).then(response => {
          if (response.code == this.$ECode.SUCCESS) {
            this.$commonUtil.message.success(response.message);
            this.addEquityDialog = false;
            this.addEquityForm.eauityType = null;
            this.addEquityForm.detailName = null;
            this.addEquityForm.sort = null;
            this.getEquityList();
          } else {
            this.$commonUtil.message.error(response.message);
          }
        });
      });
    },

    editEquitys() {
      let list = this.versionEquityList;

      let paramslist = list.map((item, parentIndex) => {
        let childList =
          item.equityDetailDtoList == undefined
            ? []
            : item.equityDetailDtoList.map((childItem, index) => {
                return {
                  uid: childItem.uid,
                  detailName: childItem.detailName,
                  configTimeList: childItem.flagList || null,
                  sort: index
                };
              });
        return {
          uid: item.uid,
          name: item.name,
          equityDetailList: childList,
          sort: parentIndex
        };
      });
      editEquity(paramslist).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.$commonUtil.message.success(response.message);
          this.isEdit = false;
          this.getEquityList();
        } else {
          this.$commonUtil.message.error(response.message);
        }
      });
    },

    deleteEquityItem(id) {
      this.$confirm("此操作将把该权益删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          deleteEquityDetail({
            detailUid: id
          }).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message);
              this.getEquityList();
            } else {
              this.$commonUtil.message.error(response.message);
            }
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消删除");
        });
    },

    deleteEquityTime(item) {
      this.$confirm(`此操作将把该权益的${item.remark}删除, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          deleteEquityTime(item).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message);
              this.getEquityList();
            } else {
              this.$commonUtil.message.error(response.message);
            }
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消删除");
        });
    },

    deleteEquity(row) {
      this.$confirm("此操作将把该类目及权益删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let list = row.equityDetailDtoList
            ? row.equityDetailDtoList.map(item => item.uid)
            : [];
          var params = {
            configEquityUid: row.uid,
            detailUidList: list.toString()
          };
          deleteEquity(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message);
              this.getEquityList();
            } else {
              this.$commonUtil.message.error(response.message);
            }
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消删除");
        });
    },

    reduceTime() {
      this.addEquityForm.configTimeList.pop();
    },
    addTime() {
      this.addEquityForm.configTimeList.push({ value: "" });
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  .equity {
    .equity-main {
      padding: 20px 20px 0 20px;
      .title {
        font-weight: bold;
      }
      .tab-item {
        display: inline-block;
        margin: 15px 25px 0 0;
        color: #888888;
      }
    }
    .equity-main-header {
      // padding: 20px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-size: large;
        font-weight: bolder;
      }
    }
  }
}
.add-equity-form-type {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dialog-button {
  width: 100%;
  display: flex;
  justify-content: center;
}
.equity-input {
  position: relative;
  margin-right: 10px;
  i {
    position: absolute;
    cursor: pointer;
  }
  .config-time-input:nth-child(1) {
    margin-left: 30px;
  }
  .config-time-input {
    // margin-right: 30px;
  }
}
/deep/ .equity-input-header {
  .el-input__inner {
    font-weight: bolder;
  }
}

/deep/.el-button-width {
  padding: 6px;
  width: 100px;
  height: 30px;
}

.config-time-list {
  color: #bbbbbb;
  font-size: small;
  margin: 0 10px;
}

.config-time-content {
  position: relative;
  i {
    position: absolute;
    top: -10px;
    cursor: pointer;
  }
}
</style>
