import request from '@/utils/request'

// process.env.ADMIN_API
const api = process.env.ADMIN_API

// 新建、编辑练习
export function practiceEdit(params) {
    return request({
        url: api + '/Practice/edit',
        method: 'post',
        data: params
    })
}

// 删除练习
export function practiceDelete(params) {
    return request({
        url: api + '/Practice/delete',
        method: 'post',
        data: params
    })
}

// 查询所有练习
export function practiceSelect(params) {
    return request({
        url: api + '/Practice/select',
        method: 'get',
        params
    })
}

// 分页模糊查询学员练习概况
export function studentfilter(params) {
    return request({
        url: api + '/Practice/studentfilter',
        method: 'get',
        params
    })
}

// 查看学员某一次练习的所有记录
export function analyticsPractice(params) {
    return request({
        url: api + '/Practice/analyticsPractice',
        method: 'get',
        params
    })
}

// 查看单个学员信息概况
export function getStudentRecord(params) {
    return request({
        url: api + '/StudentRecord/getStudentRecord',
        method: 'get',
        params
    })
}

// 查看某个学院单次练习试卷详情
export function praPaperAnalytics(params) {
    return request({
        url: api + '/Practice/praPaperAnalytics',
        method: 'get',
        params
    })
}

// 数据分析
export function generalSituation(params) {
    return request({
        url: api + '/Practice/generalSituation',
        method: 'get',
        params
    })
}

// 学员数据
export function studentData(params) {
    return request({
        url: api + '/Practice/studentData',
        method: 'get',
        params
    })
}

// 练习试卷数据
export function paperData(params) {
    return request({
        url: api + '/Practice/paperData',
        method: 'get',
        params
    })
}

// 数据导出
export function practiceExport() {
    return api + "/Practice/export"
}

// 练习分享
export function share(params) {
    return request({
        url: api + '/Practice/share',
        method: 'get',
        params
    })
}

// 分页查询练习(新版)
export function practiceSelectNew(params) {
    return request({
        url: api + '/Practice/newPage',
        method: 'post',
        data: params
    })
}

// 新建练习（新版)
export function practiceAddV2(params) {
    return request({
        url: api + '/Practice/addV2',
        method: 'post',
        data: params
    })
}

// 编辑练习（新版)
export function practiceEditV2(params) {
    return request({
        url: api + '/Practice/editV2',
        method: 'post',
        data: params
    })
}