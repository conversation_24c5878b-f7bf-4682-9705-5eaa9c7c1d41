import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/disRankIntegralRecord/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/disRankIntegralRecord/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/disRankIntegralRecord/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/disRankIntegralRecord/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/disRankIntegralRecord/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/disRankIntegralRecord/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
