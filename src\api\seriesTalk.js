import request from '@/utils/request'

// 系列Talk列表
export function selectTalkList(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/selectTalkList',
        method: 'post',
        data:params
    })
}

// 添加系列Talk
export function addTalk(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/addTalk',
        method: 'post',
        data: params
    })
}

// 置顶
export function topTalk(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/topTalk',
        method: 'post',
        data: params
    })
}

//编辑
export function editTalk(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/editTalk',
        method: 'post',
        data: params
    })
}

// 上下架
export function updateTalkStatus(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/updateTalkStatus',
        method: 'post',
        data: params
    })
}

// 删除
export function deleteTalk(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/deleteTalk',
        method: 'post',
        data: params
    })
}

// 订阅系列Talk的用户列表
export function selectUserSeriesTheme(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/seriesTheme/selectUserSeriesTheme',
        method: 'post',
        data: params
    })
}

//导出订阅用户
export function exportData(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/admin/seriesTheme/finish/export",
        method: "post",
        params,
        responseType: "blob"
    });
}
