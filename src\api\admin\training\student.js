import request from '@/utils/request'

/**
 * 教师列表
 */
export function searchAPI(data) {
  return request({
    url: 'training/PjtSysUser/searchMajorStudent',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 新增
 */
export function addAPI(data) {
  return request({
    url: '/training/PjtSysUser/insertStudent',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 编辑
 */
export function editAPI(data) {
  return request({
    url: '/training/PjtSysUser/updatePjtUserMajorRelation',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除
 */
export function dropAPI(data) {
  return request({
    url: '/training/PjtSysUser/deletePjtUserMajorRelation',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 修改状态
 */
export function editStatusAPI(data) {
  return request({
    url: '/training/PjtSysUser/updatePjtUserMajorRelationStatus',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 专业树
 */
export function majorTreeAPI(data) {
  return request({
    url: '/training/pjtMajorClass/backSearchMajorClass',
    method: 'post'
  })
}

/**
 * 模版下载
 */
export function templateAPI(data) {
  return request({
    url: '/training/PjtSysUser/exportUserTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

/**
 * 导入
 */
export function importAPI(file, classCode) {
  const data = new FormData()
  data.append('file', file)
  data.append('classCode', classCode)
  return request({
    url: 'training/PjtSysUser/importUserStudentTemplate',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

/**
 * 导出
 */
export function exportAPI(data) {
  return request({
    url: 'training/PjtSysUser/exportSearchMajorStudent',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
