<template>
  <div class="evaluation-list">
    <el-page-header content="返回考试管理首页" @back="goBack"/>
    <div class="evaluation-content">
      <!-- 搜索表单区域 -->
      <template>
        <div class="top-form">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item v-if="examInfo.synchronizationClass === 1" label="状态">
              <el-select v-model="queryForm.status" clearable size="small" placeholder="状态">
                <el-option value="" label="不限" />
                <el-option :value="1" label="启用" />
                <el-option :value="2" label="停用" />
              </el-select>
            </el-form-item>
            <el-form-item><el-input v-model="queryForm.applicantName" size="small" placeholder="姓名" clearable suffix-icon="el-icon-search" /></el-form-item>
            <el-form-item><el-button size="small" type="primary" @click="handleQuery">查询</el-button></el-form-item>
            <el-form-item style="float:right;margin-right: 50px;">
              <el-button v-if="examInfo.synchronizationClass === 1" size="small" type="primary" @click="handleExcel">导入</el-button>
              <el-button v-if="examInfo.synchronizationClass === 1" size="small" type="primary" @click="addStudent">新增</el-button>
              <el-button v-if="selectList.length" :disabled="selectList.length == 0" type="danger" icon="el-icon-delete" size="small" @click="handleDelete(null)">删除</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <!-- 表格区域 -->
      <template>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
          <el-table-column v-if="examInfo.synchronizationClass === 1" type="selection" header-align="center" align="center" />
          <el-table-column prop="applicantName" label="学员姓名" align="center" />
          <el-table-column prop="applicantPhone" label="学员账号" align="center" />
          <el-table-column prop="initialPassword" label="初始密码" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.initialPassword ? scope.row.initialPassword : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="idCard" label="身份证号" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.idCard ? scope.row.idCard : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="signUpType" label="报考方式" align="center">
            <template slot-scope="scope">
              <span>{{ examInfo.synchronizationClass === 1 ? 'B端导入' : '同步培训班' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="examInfo.synchronizationClass === 1" prop="status" label="状态" align="center">
            <template slot-scope="scope">
              <el-switch
                :inactive-value="2"
                :active-value="1"
                v-model="scope.row.status"
                style="display: block"
                active-color="#13ce66"
                inactive-color="#ff4949"
                active-text="启用"
                inactive-text="停用"
                @change="swChange(scope.row, $event)"
              />
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页区域 -->
      <template>
        <el-pagination
          :current-page="queryForm.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryForm.pageSize"
          :total="total"
          style="text-align: right; margin: 15px;"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </template>
    </div>
    <!-- 添加或修改 -->
    <el-dialog :title="title" :visible.sync="open" :close-on-click-modal="false" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="学员姓名" prop="applicantName"><el-input v-model="form.applicantName" placeholder="请输入学员姓名" /></el-form-item>
        <el-form-item label="学员账号" prop="applicantPhone"><el-input v-model="form.applicantPhone" placeholder="请输入学员账号" maxlength="11" /></el-form-item>
        <el-form-item label="初始密码" prop="initialPassword"><el-input v-model="form.initialPassword" placeholder="不输入即初始密码与学员账号同步" /></el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog :title="upload.drTitle" :visible.sync="upload.open" :close-on-click-modal="false" width="480px" append-to-body>
      <el-steps
        :active="stepsActive"
        simple>
        <el-step
          v-for="(item, index) in stepList"
          :key="index"
          :title="item.title"
          :icon="item.icon"
          :status="item.status" />
      </el-steps>
      <div
        v-if="stepsActive == 1"
        class="sections">
        <el-form ref="form" :model="form" :rules="rules" label-width="70px">
          <el-form-item label="文件" required>
            <el-upload
              ref="upload"
              :limit="1"
              :data="xlsForm"
              :headers="{ Authorization: $GetToken() }"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-change="handleChange"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :on-exceed="onExceed"
              :auto-upload="false"
              accept=".xlsx, .xls"
              drag
            >
              <i class="el-icon-upload"/>
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip text-center">
                <el-button type="text" @click="importTemplate">下载导入模板</el-button>
                <div>支持上传xls、xlsx文件，且大小不超过10MB</div>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div
        v-loading="loading"
        v-else-if="stepsActive == 2"
        element-loading-text="数据导入中"
        element-loading-spinner="el-icon-loading"
        class="sections" />
      <div
        v-loading="loading"
        v-else-if="stepsActive == 3"
        class="sections">
        <div class="result-info">
          <i class="el-icon-success" />
          <p class="result-info__des">数据导入完成</p>
          <p v-if="resultData" class="result-info__detail" >导入总数据<span style="font-weight: 700;">{{ resultData.total }}</span>条，导入成功<span style="color: #2362fb;"><template v-if="resultData">{{ resultData.total - (resultData.fail || 0) }}</template></span>条，导入失败<span style="color: #f56c6c;">{{ resultData.fail || 0 }}</span>条</p>
          <el-button
            v-if="resultData && resultData.fail > 0"
            class="result-info__btn--err"
            style="cursor: pointer; color: #2362fb;"
            type="text"
            @click="downloadErrData">下载错误数据</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getList, add, upDown, deleteBatch, downloadTemplate } from '@/api/trainStudentMsg'
// import { getToken } from '@/utils/auth'
import fileSaver from 'file-saver'
export default {
  inject: ['examParentThis'],
  data() {
    return {
      xlsForm: {
        classUid: ''
      },
      form: {
        applicantName: '',
        applicantPhone: '',
        initialPassword: ''
      },
      title: '',
      open: false,
      rules: {
        applicantName: [
          { required: true, message: '请输入学员姓名', trigger: 'blur' },
          { required: true, message: '仅支持汉字', pattern: /^[\u4e00-\u9fa5]+$/, trigger: ['blur', 'change'] }
        ],
        applicantPhone: [
          { required: true, message: '请输入学员账号', trigger: 'blur' },
          { required: true, pattern: /^1[2|3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的学员账号', trigger: 'blur' }
        ],
        initialPassword: [
          { message: '请输入初始密码', trigger: 'blur' },
          {
            pattern: /(?=.*\d).{6,16}/,
            message: '密码长度为6到16位！',
            trigger: 'blur'
          }
        ]
      },
      tableData: [],
      total: 0,
      queryForm: {
        currentPage: 1,
        pageSize: 10,
        applicantName: '',
        status: '',
        classUid: ''
      },
      selectList: [],
      upload: {
        classUid: '',
        drTitle: '学员导入',
        // 是否显示弹出层（用户导入）
        open: false,
        // 是否禁用上传
        isUploading: false,
        // 使用上传的地址
        url: process.env.ADMIN_API + '/trainSignUp/importExcelWithValidator'
      },
      loading: false,
      stepsActive: 1,
      stepList: [
        {
          icon: 'el-icon-folder',
          title: '上传文件',
          status: 'wait'
        },
        {
          icon: 'el-icon-upload',
          title: '导入数据',
          status: 'wait'
        },
        {
          icon: 'el-icon-success',
          title: '导入完成',
          status: 'wait'
        }
      ],
      resultData: null
    }
  },
  computed: {
    ...mapGetters(['examInfo'])
  },
  created() {
    this.queryForm.classUid = this.examInfo.trainModuleContentList ? this.examInfo.trainModuleContentList[0].contentUid : ''
    this.getDataList()
  },
  methods: {
    // 返回考试管理首页
    goBack() {
      this.examParentThis.flag = 2
    },
    watch(data) {
      this.signWatchForm = {
        className: data.className,
        signUpType: data.signUpType,
        status: data.registerStatus,
        reason: data.checkReason
      }
    },
    downLoadFile(data) {
      this.detailInfo = data
      this.download()
    },
    /** 密码跟随账号同步 */
    setPassWord(val) {
      this.$set(this.form, 'initialPassword', val)
      this.form.initialPassword = val
    },
    /** 下载模板操作 */
    importTemplate() {
      downloadTemplate().then(res => {
        fileSaver.saveAs(res, `考生导入模板.xls`)
      })
    },
    /** 下载错误数据 */
    downloadErrData() {
      fetch(this.resultData.errUrl, {
        method: 'get',
        responseType: 'blob'
      }).then((response) => response.blob())
        .then((blob) => {
          const a = document.createElement('a')
          const URL = window.URL || window.webkitURL
          const herf = URL.createObjectURL(blob)
          a.href = herf
          a.download = `导出错误数据.xls`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(herf)
        })
    },
    randomPassword(length) {
      length = Number(length)
      // Limit length
      if (length < 6) {
        length = 6
      } else if (length > 16) {
        length = 16
      }
      const passwordArray = ['ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz', '1234567890', '!@#$%&*()']
      var password = []
      let n = 0
      for (let i = 0; i < length; i++) {
        if (password.length < (length - 4)) {
          const arrayRandom = Math.floor(Math.random() * 4)
          const passwordItem = passwordArray[arrayRandom]
          const item = passwordItem[Math.floor(Math.random() * passwordItem.length)]
          password.push(item)
        } else {
          const newItem = passwordArray[n]
          const lastItem = newItem[Math.floor(Math.random() * newItem.length)]
          const spliceIndex = Math.floor(Math.random() * password.length)
          password.splice(spliceIndex, 0, lastItem)
          n++
        }
      }
      return password.join('')
    },
    // 导入
    handleExcel() {
      this.upload.drTitle = '导入'
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
      this.upload.open = true
    },
    // 提交上传文件
    submitFileForm() {
      if (this.stepsActive === 1) {
        if (this.stepList[0].status === 'finish') {
          this.stepList[1].status = 'process'
          this.stepsActive = 2
          this.$refs['form'].validate(valid => {
            if (valid) {
              this.$refs.upload.submit()
            }
          })
        } else {
          this.$commonUtil.message.error('请选择导入文件')
        }
      } else if (this.stepsActive === 3) {
        this.upload.open = false
        this.handleQuery()
        this.stepsActive = 1
        this.stepList[0].status = 'wait'
        this.stepList[1].status = 'wait'
        this.stepList[2].status = 'wait'
      }
    },
    handleChange(file, fileList) {
      this.xlsForm.classUid = this.examInfo.trainModuleContentList ? this.examInfo.trainModuleContentList[0].contentUid : ''
      this.stepList[0].status = 'finish'
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
      this.loading = true
      this.stepsActive = 2
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false
      this.stepList[1].status = 'finish'
      if (response.code === 200 || response.code === 0) {
        this.resultData = { ...response.data, failMsg: response.msg, type: true, isError: true }
        this.stepList[2].status = 'finish'
      } else if (response.code === 0 || response.code === 500) {
        this.resultData = { ...response.data, failMsg: response.msg, type: true, isError: false, errUrl: response.other }
        this.stepList[2].status = 'error'
      }
      this.loading = false
      this.stepsActive = 3
    },
    onExceed(file, fileList) {
      this.$message.warning('当前只能上传一个文件，请进行删除后再进行上传')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryForm.currentPage = 1
      this.getDataList()
    },
    // 列表
    getDataList() {
      getList(this.queryForm).then(res => {
        if (res.code === this.$ECode.SUCCESS) {
          this.total = res.data.total
          this.tableData = res.data.records
        } else {
          this.$commonUtil.message.error(res.message)
        }
      })
    },
    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.queryForm.pageSize = newSize
      this.getDataList()
    },
    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.queryForm.currentPage = newSize
      this.getDataList()
    },
    async swChange(data, event) {
      const params = {
        relationUid: data.uid
      }
      const result = await upDown(params)
      if (result.code === this.$ECode.SUCCESS) {
        this.$commonUtil.message.success(result.message)
      }
    },
    // 新增学员
    addStudent() {
      this.title = '新增学员'
      this.$commonUtil.clearValue(this.form)
      this.form.initialPassword = ''
      this.open = true
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.classUid = this.queryForm.classUid
          this.form.isSendMessage = 0
          if (this.form.initialPassword) {
            this.$set(this.form, 'initialPassword', this.form.initialPassword)
          } else {
            this.$set(this.form, 'initialPassword', this.form.applicantPhone)
          }
          this.open = false
          add(this.form).then(result => {
            if (result.code === this.$ECode.SUCCESS) {
              this.$message.success('新增成功')
              this.handleQuery()
            } else {
              this.$message.error(result.message)
            }
          })
        }
      })
    },
    cancel() {
      this.open = false
      this.$commonUtil.clearValue(this.form)
    },
    handleSelectionChange(val) {
      this.selectList = val.map(item => item.uid)
    },
    handleDelete(item) {
      const ids = item ? [item.uid] : [...this.selectList]
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteBatch(ids).then(res => {
            if (res.code === this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(res.data)
              this.getDataList()
            } else {
              this.$commonUtil.message.error(res.data)
            }
          })
        })
        .catch(err => {
          return err
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.evaluation-list{
  .evaluation-content{
    margin-top: 20px;
  }
  .content_div {
    .train_div {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
    }
  }
}
.sections {
  font-size: 14px;
  min-height: 215px;
  .result-info {
    text-align: center;
    .el-icon-success {
      font-size: 32px;
      color: #2362fb;
    }
  }
  .download {
    cursor: pointer;
    color: #2362fb;
  }
  /deep/ .el-loading-spinner {
    top: 45%;
    .el-icon-loading {
      font-size: 40px;
      color: #999;
    }
    .el-loading-text {
      color: #333;
    }
  }
}
/deep/ .el-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 20px;
    color: #000000;
    font-weight: bold;
    padding-bottom: 5px;
  }
  // .el-drawer__body {
  // }
}
.dialog-button {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
/deep/.el-button-width {
  padding: 6px;
  width: 100px;
  height: 30px;
}
.between {
  display: flex;
  justify-content: space-between;
}
.el-steps--simple {
  padding: 15px 20px;
  margin-bottom: 20px;
}
.el-upload__tip {
    font-size: 14px;
    color: #606266;
}
.el-form-item__content {
  line-height: 24px;
}
</style>
