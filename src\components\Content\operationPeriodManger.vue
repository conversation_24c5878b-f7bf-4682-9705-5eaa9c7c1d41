<template>
    <div class="operationPeriodManger">
        <!-- 添加营期状态 -->
        <template v-if="isAddCampPeriod">
            <addCampPeriodCom></addCampPeriodCom>
        </template>
        <!-- 营期管理界面 -->
        <template v-else>
            <el-page-header @back="goBack" content="返回训练营"></el-page-header>
            <div class="head">
                <p class="title">{{trainingCampInfo.campName}}</p>
                <div class="buttom">
                    <div class="left">
                        <el-button size="mini" type="primary" @click="editor">编辑</el-button>
                        <el-popconfirm
                            @confirm="deleteItem"
                            title="确定要删除该训练营吗？"
                            >
                            <el-button type="danger" v-if="uids.length" slot="reference" size="mini">删除</el-button>
                        </el-popconfirm>
                    </div>
                    <div class="right">
                        <span>营期数：{{trainingCampInfo.campPeriodNumber}}</span>
                        <span>报名人数：{{trainingCampInfo.enrollment}}</span>
                    </div>
                </div>
            </div>
            <div class="body">
                <el-tabs class="training-camp-tab" v-model="currentTab">
                    <el-tab-pane name="operationPeriodManger" label="营期管理">
                        <div class="operationPeriodManger-tab">
                            <el-button type="primary" @click="addOperationPeriod">新建营期</el-button>
                            <el-form :inline="true" :model="operationPeriodMangerSearchForm" class="demo-form-inline operationPeriodManger-form">
                                <el-form-item label="">
                                    <el-select @change="initCampPeriodList" v-model="operationPeriodMangerSearchForm.putwayStatus" placeholder="全部状态">
                                         <el-option label="全部" value=""></el-option>
                                        <el-option v-for="(item,index) in trainingCampModel.putawayStatusArr" :key="index" :label="item" :value="index"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="">
                                    <el-select @change="initCampPeriodList" v-model="operationPeriodMangerSearchForm.courseStatus" placeholder="课程状态">
                                         <el-option label="全部" value=""></el-option>
                                        <el-option v-for="(item,index) in trainingCampModel.courseStatusArr" :key="index" :label="item" :value="index"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="">
                                    <el-input v-model="operationPeriodMangerSearchForm.campName" placeholder="请输入营期名称"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="search">搜索</el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                        <p>营期列表({{trainingCampModel.total}}条)</p>
                        <!-- 表格区域 -->
                        <template>
                            <div style="width: 100%;">
                                <el-button style="float:right;" type="danger" size="mini" @click="batchDelete">删除</el-button>
                            </div>
                            <el-table @selection-change="handleSelectionChange" class="training-camp-table"
                                :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
                                :cell-style="{ textAlign: 'center' }"
                                :data="operationPeriodMangerData" style="width: 100%">
                                <el-table-column
                                type="selection"
                                width="55">
                                </el-table-column>
                                <el-table-column type="index" align="center" label="序号" width="60" />
                                <el-table-column prop="campPeriodName" label="营期名称" width="180">
                                </el-table-column>
                                <!-- <el-table-column prop="enrollment" label="报名人数" width="180">
                                </el-table-column> -->
                                <el-table-column prop="courseStatus" label="课程状态">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.courseStatus | courseStatusLabel }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="上架状态">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.status | putawayLabel }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="上架时间">
                                </el-table-column>
                                <el-table-column prop="address" width="200" label="操作">
                                    <template slot-scope="scope">
                                        <ul class="operation">
                                            <li @click="editCamp(scope.row)">编辑</li>
                                            <!-- <el-popconfirm @confirm="delCampPeriod([scope.row.uid])" title="确定删除当前营期吗？">
                                                <li slot="reference">删除</li>
                                            </el-popconfirm> -->
                                        </ul>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-tab-pane>
                </el-tabs>
                <!-- 分页 -->
                <div class="el-pagination">
                    <el-pagination
                    :page-sizes="[5, 10, 30, 40]"
                    @size-change="operationPeriodMangerSizeChange"
                    @current-change="operationPeriodMangerCurrentChange"
                    background
                    layout="sizes,prev, pager, next"
                    :page-size="trainingCampModel.pageSize"
                    :current-page="trainingCampModel.page"
                    :total="trainingCampModel.total">
                    </el-pagination>
                </div>
            </div>
        </template>
        <!-- 编辑训练营弹窗 -->
        <el-dialog @closed="trainingCampDialogClose" title="编辑训练营" :visible.sync="trainingCampModel.trainingCampDialogVisible" width="30%" center>
            <el-form :rules="trainingCampModel.addtrainingFormRule" label-width="100px" ref="addtrainingForm" :model="trainingCampModel.addtrainingForm">
                <el-form-item label="训练营名称" prop="name">
                    <el-input v-model="trainingCampModel.addtrainingForm.name"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="trainingCampModel.trainingCampDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="realEditorTrainingCamp">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 新建营期弹窗 -->
        <el-dialog class="elDialog" @closed="operationPeriodMangerClose" title="新建营期" :visible.sync="operationPeriodMangerVisible" width="38%" center>
            <div class="tip">是否复用已创建的营期内容?</div>
            <el-form class="operationPeriodMangerForm" :rules="operationPeriodMangerAddRule" label-width="100px" ref="addtrainingForm" :model="operationPeriodMangerAddForm">
                <el-form-item label="" prop="name">
                    <el-radio-group v-model="operationPeriodMangerAddForm.type">
                        <el-radio :label="1">
                            <span>复用已有营期的内容</span>
                            <el-select v-model="operationPeriodMangerAddForm.alreadyHaveContentId" placeholder="请选择">
                                <el-option
                                v-for="(item,index) in periodNameList"
                                :key="index"
                                :label="item.campPeriodName"
                                :value="item.uid">
                                </el-option>
                            </el-select>
                        </el-radio>
                        <el-radio :label="2">不需要，我要新建一个营期</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="operationPeriodMangerVisible = false">取 消</el-button>
                <el-button type="primary" @click="realAddOperationPeriod">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {mapGetters,mapMutations} from 'vuex';
import { delCamp, editCamp, getCampPeriodList, delCampPeriod, multiplexingCampPeriod, getPeriodNameList } from '@/api/content/trainingCamp';
import addCampPeriodCom from '@/components/Content/addCampPeriod';
export default {
    inject:['parentThis'],
    components:{
        addCampPeriodCom
    },
    filters:{
        putawayLabel(val){
            switch(val){
                case 0:
                    return "已下架";
                case 1:
                    return "上架";
                case 2:
                    return "待上架";
            }
        },
        courseStatusLabel(val){
            switch(val){
                case 0:
                    return "待招生";
                case 1:
                    return "招生中";
                case 2:
                    return "待开课";
                case 3:
                    return "开课中";
                case 4:
                    return "已结束";
            }
        }
    },
    data(){
        return{
            trainingCampModel:{
                courseStatusArr:['待招生','招生中','待开课','开课中','已结束'],
                putawayStatusArr:['已下架','上架','待上架'],
                page:1,
                pageSize:5,
                total:0,
                trainingCampDialogVisible:false,
                addtrainingForm: {
                    name: ""
                },
                addtrainingFormRule:{
                    name:[{required:true,message:'请输入训练营名称',trigger:'blur'}]
                },
            },
            isAddCampPeriod:false,
            operationPeriodMangerVisible:false,
            currentTab:"operationPeriodManger",
            operationPeriodMangerData:[],
            operationPeriodMangerAddForm:{
                type:1,
                alreadyHaveContentId:""
            },
            operationPeriodMangerAddRule:{},
            operationPeriodMangerSearchForm:{
                putwayStatus:"",
                courseStatus:"",
                campName:""
            },
            operationPeriodMangerForm:{
            },
            periodNameList: [],
            uids:[]
        }
    },
    mounted() {
        this.getPeriodNameList();
        this.initCampPeriodList();
    },
    computed:{
        ...mapGetters(['trainingCampInfo'])
    },
    methods: {
        handleSelectionChange(datas) {
            this.uids = datas.map(item => item.uid);
        },
        batchDelete() { 
            if (this.uids.length == 0) return this.$message.warning('请选择要删除的营期');
            this.$confirm('此操作将永久删除该营期, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                await this.delCampPeriod(this.uids);
            }).catch(() => {

            });
        },
        // 编辑训练营
        async editCamp(data) {
            // 请求营期信息
            let params = {
                campUid: this.trainingCampInfo.uid,
                campPeriodUid: data.uid
            }
            let result = await getCampPeriodList(params);
            if (result.code == this.$ECode.SUCCESS) {
                // 保存营期信息
                this.setCampPeriodInfo(result.data.records[0]);
                // 编辑标识
                this.parentThis.oprationFlag = 2;
                this.isAddCampPeriod = true;
                this.operationPeriodMangerVisible = false;
            }
        },
        // 营期复用选项列表
        async getPeriodNameList() {
            let params = {
                campUid: this.trainingCampInfo.uid
            }
            let result = await getPeriodNameList(params);
            console.warn("result",result);
            if (result.code == 200) {
                this.periodNameList = result.data;
            }
        },
        // 删除营期
        async delCampPeriod(uids) {
            let result = await delCampPeriod(uids);
            if (result.code == 200) {
                this.$message.success('删除营期成功');
                this.initCampPeriodList();
            }
        },
        // 页大小改变
        operationPeriodMangerSizeChange(val){
            this.trainingCampModel.pageSize=val;
            this.initCampPeriodList();
        },
        // 当前页改变
        operationPeriodMangerCurrentChange(val){
            this.trainingCampModel.page=val;
            this.initCampPeriodList();
        },
        // 返回训练营
        goBack(){
            this.parentThis.flag=2;
        },
        ...mapMutations({
            'setTrainingCampInfo': 'trainingCamp/setTrainingCampInfo',
            'setCampPeriodInfo': 'trainingCamp/setCampPeriodInfo'
        }),
        async initCampPeriodList(){
            let params={
                currentPage:this.trainingCampModel.page,
                pageSize:this.trainingCampModel.pageSize,
                campUid:this.trainingCampInfo.uid,
                courseStatus:this.operationPeriodMangerSearchForm.courseStatus,//课程状态
                loadingUnloading:this.operationPeriodMangerSearchForm.putwayStatus,//上下架状态
                periodName:this.operationPeriodMangerSearchForm.campName
            }
            let result = await getCampPeriodList(params);
            if(result.code==200){
                this.trainingCampModel.total=result.data.total;
                this.operationPeriodMangerData=result.data.records;
            }
        },
        // 删除训练营
        async deleteItem() {
            let params=[this.trainingCampInfo.uid];
            let result=await delCamp(params);
            if(result.code==200){
                this.$message.success('删除训练营成功!');
                // 跳转回训练营界面
                this.parentThis.flag=2;
            }
        },
        // 编辑
        editor(){
            this.trainingCampModel.addtrainingForm.name=this.trainingCampInfo.campName;
            this.trainingCampModel.trainingCampDialogVisible=true;
        },
        // 编辑训练营
        async realEditorTrainingCamp(){
            let params={
                uid:this.trainingCampInfo.uid,
                campName:this.trainingCampModel.addtrainingForm.name
            }
            this.$refs['addtrainingForm'].validate(async (valid) => {
                if(valid){
                    let result=await editCamp(params);
                    if(result.code==200){
                        this.setTrainingCampInfo({...this.trainingCampInfo,campName:this.trainingCampModel.addtrainingForm.name});
                        this.$message.success('修改训练营成功');
                        this.trainingCampModel.trainingCampDialogVisible=false;
                    }
                }
            });
        },
        // 数据重置
        dataReSet(){
            this.trainingCampModel.addtrainingForm={
                name: ""
            };
        },
        // 弹窗关闭触发
        trainingCampDialogClose(){
            this.dataReSet();
            // 重置表单校验
            this.$refs['addtrainingForm'].clearValidate();
        },
        // 搜索
        search(){
            this.trainingCampModel.page=1;
            this.initCampPeriodList();
        },
        operationPeriodMangerClose() {
            this.operationPeriodMangerAddForm={
                type: 1,
                alreadyHaveContentId: ""
            }
        },
        async addOperationPeriod() {
            await this.getPeriodNameList();
            this.operationPeriodMangerVisible=true;
        },
        async realAddOperationPeriod(){
            // 新建营期,打开添加营期弹窗
            if (this.operationPeriodMangerAddForm.type == 2) {
                // 添加标识
                this.parentThis.oprationFlag = 1;
                this.isAddCampPeriod = true;
                this.operationPeriodMangerVisible = false;
            } else {
                if (!this.operationPeriodMangerAddForm.alreadyHaveContentId) {
                    this.$message.warning('请选择需要复用的营期');
                    return;
                }
                // 请求复用营期
                let params = {
                    campPeriodUid: this.operationPeriodMangerAddForm.alreadyHaveContentId
                };
                let result = await multiplexingCampPeriod(params);
                if (result.code == 200) {
                    this.$message.success('营期复用成功');
                    this.operationPeriodMangerVisible = false;
                    // 刷新营期列表
                    this.initCampPeriodList();
                }

            }
        }
    }
}
</script>
<style lang="scss" scoped>
.operationPeriodManger {
    .head {
        padding: 20px;
        border-radius: 3px;
        background: white;
        box-shadow: 0 0 10px rgb(224, 224, 224);
        min-height: 120px;
        width: 100%;
        margin-top: 20px;

        .title {
            margin: 0;
        }

        .buttom {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .left {
                margin-top: 10px;
            }

            .right {}
        }
    }
    .elDialog{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /deep/ .el-dialog__body{
            .tip{
                text-align: center;
                font-size: 18px;
                margin-bottom: 15px;
            }
            .operationPeriodMangerForm{
                .el-form-item{
                    .el-form-item__content{
                        .el-radio-group{
                            display: flex;
                            flex-direction: column;
                            .el-radio__label{
                                font-size: 18px;
                                &:last-of-type{
                                    display: inline-block;
                                    margin-top: 20px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .body {
        margin-top: 25px;
        border-radius: 3px;
        background: white;
        box-shadow: 0 0 10px rgb(224, 224, 224);
        min-height: 400px;
        padding-bottom: 20px;
        width: 100%;
        .el-pagination{
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .training-camp-tab {
            /deep/ .el-tabs__nav-scroll {
                padding: 0 20px;
            }

            /deep/ .el-tabs__content {
                padding: 20px;

                .operationPeriodManger-tab {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .operationPeriodManger-form {
                        height: 40px;
                    }
                }

                .training-camp-table {
                    .operation {
                        padding: 0;
                        margin: 0 auto;
                        list-style: none;
                        display: flex;
                        width: max-content;

                        li {
                            color: #2a75ed;
                            cursor: pointer;
                            float: left;
                            padding: 0 15px;
                            display: flex;
                            align-items: center;
                            position: relative;
                            justify-content: center;

                            &::after {
                                content: "";
                                height: 14px;
                                border-right: 1px solid #ebe8e8;
                                right: 0;
                                position: absolute;
                            }

                            &:last-child::after {
                                border: none;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
