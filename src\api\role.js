import request from "@/utils/request";

export function getRoleList(params) {
  return request({
    url: process.env.ADMIN_API + "/role/getList",
    method: "post",
    data: params
  });
}

export function addRole(params) {
  return request({
    url: process.env.ADMIN_API + "/role/add",
    method: "post",
    data: params
  });
}

export function editRole(params) {
  return request({
    url: process.env.ADMIN_API + "/role/edit",
    method: "post",
    data: params
  });
}

export function deleteRole(params) {
  return request({
    url: process.env.ADMIN_API + "/role/delete",
    method: "post",
    data: params
  });
}

// 获取分类角色信息
export function getNewRoleList(params) {
  return request({
    url: process.env.ADMIN_API + "/role/getRoleList",
    method: "post",
    data: params
  });
}

/**
 * 清空角色下的用户
 * @param {*} params
 * @returns
 */
export function clearRoleUser(params) {
  return request({
    url: process.env.ADMIN_API + "/role/clearRoleUser",
    method: "post",
    data: params
  });
}
