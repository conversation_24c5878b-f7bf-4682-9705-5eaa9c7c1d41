import request from '@/utils/request'

/**
 * 活动列表
 * @param params
 */
export function activityList(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/getList',
    method: 'post',
    data: params
  })
}
/**
 * 删除活动
 * @param {*} params 
 * @returns 
 */
export function deleteActivity(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/deleteBatch',
    method: 'post',
    data: params
  })
}
/**
 * 编辑活动
 * @param {} params 
 * @returns 
 */
export function editorActivity(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/edit',
    method: 'post',
    data: params
  })
}

/**
 * 创建活动
 * @param {} params 
 * @returns 
 */
export function addActivity(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/add',
    method: 'post',
    data: params
  })
}

/**
 * 创建活动
 * @param {} params 
 * @returns 
 */
 export function activityDetail(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/getOne',
    method: 'get',
    params: params
  })
}
/**
 * 城市数据
 * @param {} params 
 * @returns 
 */
 export function getArea(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/getArea',
    method: 'get',
    params: params
  })
}
/**
 * 发送短信
 * @param params
 */
 export function sendSms(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/resendSMS',
    method: 'get',
    params: params,
  })
}

