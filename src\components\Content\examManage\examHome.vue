<template>
  <div class="examHome">
    <!-- 搜索栏 -->
    <template>
      <div class="btn-head">
        <el-button size="mini" type="primary" @click="addExam">新建考试</el-button>
        <el-button size="mini" @click="goToQuestionBank">题库</el-button>
      </div>
    </template>
    <!-- 表格区域 -->
    <template>
      <el-table
        :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
        :cell-style="{ textAlign: 'center' }"
        :data="examManagementData"
        class="examTable"
        style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="50" />
        <el-table-column prop="examName" label="考试名称" width="210"/>
        <el-table-column prop="name" label="关联课程" width="180" show-overflow-tooltip/>
        <el-table-column prop="examType" label="考试类型">
          <template slot-scope="scope">
            <span v-if="scope.row.examType === 0">模拟考试</span>
            <span v-else-if="scope.row.examType === 1">在线考试</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="scheduledRelease" label="考试状态">
          <template slot-scope="scope">
            <span>{{ scope.row.scheduledRelease | examStatusLabel }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unapproved" label="缺考"/>
        <el-table-column prop="review" label="已批阅"/>
        <el-table-column prop="createTime" label="创建时间" width="180"/>
        <el-table-column fixed="right" width="240" prop="address" label="操作">
          <template slot-scope="scope">
            <ul class="operation">
              <li v-if="scope.row.scheduledRelease == 0 || scope.row.scheduledRelease == 2" @click="release(scope.row)">发布</li>
              <li @click="editExam(scope.row)">编辑</li>
              <el-popconfirm title="该考试确定要删除吗？" @confirm="singleDelete(scope.row)">
                <li slot="reference">删除</li>
              </el-popconfirm>
              <el-dropdown @command="examCommand">
                <li>
                  更多
                  <i class="el-icon-arrow-down"/>
                </li>
                <el-dropdown-menu slot="dropdown" class="el-dropdown-menu-exam">
                  <el-dropdown-item v-if="scope.row.scheduledRelease==1" :command="oprationExam('stopExam',scope.row.uid)">暂停考试</el-dropdown-item>
                  <el-dropdown-item :command="oprationExam('examineeList',scope.row.uid)">考生列表</el-dropdown-item>
                  <el-dropdown-item :command="oprationExam('correctList',scope.row.uid)">批阅列表</el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.scheduledRelease ==1" :command="oprationExam('statistics',scope.row.uid)">统计分析</el-dropdown-item>
                  <el-dropdown-item :command="oprationExam('share', scope.row.uid)">分享</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </ul>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <!-- 分页 -->
    <template>
      <div class="pagination">
        <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </div>
    </template>
  </div>
</template>
<script>
import { mapMutations } from 'vuex'
import { examList, deleteExam, releaseExam, stopExam } from '@/api/content/exam'
export default {
  inject: ['examParentThis'],
  filters: {
    examStatusLabel(val) {
      switch (val) {
        case 0:
          return '未发布'
        case 2:
          return '未发布'
        case 1:
          return '已发布'
      }
    }
  },
  data() {
    return {
      examManagementData: [

      ],
      total: 0,
      page: 1,
      pageSize: 10,
      examManagementForm: {

      }
    }
  },
  mounted() {
    this.page = this.$store.state.pageParams.currentPage
    this.pageSize = this.$store.state.pageParams.pageSize
    this.initExamList()
  },
  methods: {
    ...mapMutations({
      setExamInfo: 'exam/setExamInfo'
    }),
    // 返回当前触发的考试对象
    oprationExam(command, uid) {
      return {
        command,
        uid
      }
    },
    // 表格更多下拉框触发
    async examCommand(command) {
      let result, params = {}
      switch (command.command) {
        case 'examineeList':
          // 考生列表
          result = await this.initExamList(command.uid)
          if (result.code == 200) {
            this.setExamInfo(result.data.records[0])
            this.examParentThis.flag = 6
          }
          break
        case 'correctList':
          // 批阅列表
          result = await this.initExamList(command.uid)
          if (result.code == 200) {
            this.setExamInfo(result.data.records[0])
            this.examParentThis.flag = 3
          }
          break
        case 'statistics':
          // 统计分析
          result = await this.initExamList(command.uid)
          if (result.code == 200) {
            this.setExamInfo(result.data.records[0])
            this.examParentThis.flag = 5
          }
          // setExamInfo
          break
        case 'share':
          // 分享
          break
        case 'stopExam':
          // 暂停考试
          this.stopExam(command.uid)
          break
      }
    },
    // 停止考试
    async stopExam(uid) {
      const params = {
        examUid: uid
      }
      const result = await stopExam(params)
      if (result.code == 200) {
        this.$message.success('操作成功!')
        this.initExamList()
      }
    },
    // 发布考试
    async release(data) {
      const params = {
        examUid: data.uid
      }
      const result = await releaseExam(params)
      if (result.code == 200) {
        this.$message.success('操作成功!')
        this.initExamList()
      }
    },
    // 单个删除
    singleDelete(data) {
      this.deleteExam([data.uid])
    },
    async deleteExam(ids) {
      const result = await deleteExam(ids)
      if (result.code == 200) {
        this.$message.success('删除成功')
        this.initExamList()
      }
    },
    // 获取考试列表
    async initExamList(trainExamUid) {
      const params = {
        currentPage: this.page,
        PageSize: this.pageSize,
        trainExamUid: trainExamUid || null
      }
      const result = await examList(params)
      if (result.code == 200) {
        this.examManagementData = result.data.records
        this.examManagementData.forEach(item => {
          item.trainModuleContentList.length > 0 ? item.name = item.trainModuleContentList.map(it => it.name).join(' ; ') : item.name = '-'
        })
        this.total = result.data.total
      }
      return result
    },
    // 编辑考试
    async editExam(data) {
      // 获取考试详情
      const params = {
        trainExamUid: data.uid
      }
      const result = await examList(params)
      if (result.code == 200) {
        this.examParentThis.editExamInfo = result.data.records[0]
      }
      // 编辑标识
      this.examParentThis.editorFlag = 2
      this.examParentThis.flag = 1
    },
    // 添加考试
    addExam() {
      // 添加标识
      this.examParentThis.editorFlag = 1
      this.examParentThis.flag = 1
    },
    // 搜索
    search() {
      this.page = 1
      // 刷新考试列表
      this.initExamList()
    },
    // 前往题库
    goToQuestionBank() {
      this.$router.push('/content/questionBank')
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.initExamList()
      this.$store.dispatch("updatePageSize",val)
    },
    handleCurrentChange(val) {
      this.page = val
      this.initExamList()
      this.$store.dispatch("updateCurrentPage",val)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-dropdown-menu-exam{

}
.examHome {
    .pagination{
        display: flex;
        justify-content: end;
        margin-top: 20px;
    }
    .examTable {
        .operation {
            padding: 0;
            margin: 0 auto;
            list-style: none;
            display: flex;
            width: max-content;
            .el-popover__reference-wrapper .el-popover__reference::after{
                content: "";
                height: 14px;
                border-right: 1px solid #ebe8e8;
                right: 0;
                position: absolute;
            }
            li {
                color: #2a75ed;
                cursor: pointer;
                float: left;
                padding: 0 15px;
                display: flex;
                align-items: center;
                position: relative;
                justify-content: center;

                &::after{
                    content: "";
                    height: 14px;
                    border-right: 1px solid #ebe8e8;
                    right: 0;
                    position: absolute;
                }
                &:last-child::after {
                    border: none;
                }
            }
            .el-dropdown {
                li::after {
                    border: none;
                }
            }
        }
    }

    .btn-head {
        margin-bottom: 20px;
    }
}
</style>
