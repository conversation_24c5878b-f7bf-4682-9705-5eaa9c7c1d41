import Layout from "../../views/layout/Layout";
import routers from "../../router";
import _import from "@/router/_import";

import { deepMenu, shoudLoadComponent, findPByChildId, getFullRouterUrl } from '@/utils/deepMenuUtil'

function getMenuRouter() { 
  return [
    {
      meta: {
        uid: 1
      },
      menuType: 0,
      path: "/login",
      fullConcatUrlObj: {
        parentUrl: '/login',
        fullConcatUrl: '/login'
      },
      componentUrl: '/login/index',
      component: _import(`/login/index`),
      hidden: true
    },
    {
      meta: {
        uid: 2
      },
      fullConcatUrlObj: {
        parentUrl: '/404',
        fullConcatUrl: '/404'
      },
      menuType: 0,
      path: "/404",
      componentUrl: '/404',
      // component: () => import("@/views/404"),
      hidden: true,
      redirect: "/",//404页面直接重定向到首页
    },
    {
      meta: {
        uid: 3
      },
      fullConcatUrlObj: {
        parentUrl: '/401',
        fullConcatUrl: '/401'
      },
      menuType: 0,
      path: "/401",
      componentUrl: '/401',
      component: _import(`/401`),
      hidden: true
    },
    {
      meta: {
        uid: 4
      },
      menuType: 2,
      path: "/",
      fullConcatUrlObj: {
        parentUrl: '/',
        fullConcatUrl: '/'
      },
      component: Layout,
      redirect: "/data/dashboard",
      name: "首页",
      children: [
        {
          fullConcatUrlObj: {
            parentUrl: '/',
            fullConcatUrl: '/data/dashboard'
          },
          menuType: 0,
          componentUrl: '/dashboard/index',
          path: "/data/dashboard",
          component: _import(`/dashboard/index`),
          meta: { title: "仪表盘", icon: "dashboard", uid: 5 }
        }
      ]
    },
    { path: "*", redirect: "/404", hidden: true, }
  ]
}

const router = {
  state: {
    menuInfo: {},
    menuRouter: getMenuRouter(),

    allMenu: {}, // 分类后的一级菜单列表
    menuCategoryList: [], // 当前二级菜单列表
    categoryList: [] // 分类列表
  },

  mutations: {
    SET_CATEGORY_LIST: (state, categoryList) => {
      state.categoryList = categoryList;
    },
    SET_MENU_ROUTER: (state, menuRouter) => {
      state.menuRouter = menuRouter;
    },

    SET_ALL_MENU: (state, allMenu) => {
      // menus根据按钮字典值对应分类好的菜单(Map类型)
      state.allMenu = allMenu;
    },

    SET_ALL_MENU_CATEGORY: (state, menuCategoryList) => {
      // console.log(menuCategoryList)
      // menuCategoryList为顶部按钮点击筛选后的菜单
      state.menuCategoryList = menuCategoryList;
    },
    SET_MENU_INFO: (state, menuInfo) => { 
      state.menuInfo = menuInfo;
    }
  },

  actions: {
    setCategoryList({ commit }, list) {
      commit("SET_CATEGORY_LIST", list);
    },
    // menus根据按钮字典值对应分类好的菜单(Map类型)
    setAllMenu({ commit }, menus) {
      commit("SET_ALL_MENU", menus);
    },
    setMenuCategoryList({ commit }, menus) {
      // menus为顶部按钮点击筛选后的菜单
      commit("SET_ALL_MENU_CATEGORY", menus);
    },
    setRouter({ commit }, menus) {
      // 默认菜单路由
      var list = getMenuRouter();
      let deepMenuAfter = deepMenu(menus),setFirstMenuBack = deepMenu(menus);
      // 递归设置路由路径
      function deepSetFullUrl(menu) { 
        menu.forEach(item => {
          item.fullConcatUrlObj = getFullRouterUrl(item.uid, deepMenuAfter);
          if (item.children && item.children.length) { 
            deepSetFullUrl(item.children);
          }
        });
      }
      deepSetFullUrl(deepMenuAfter);
      // console.log("路由结果", deepMenuAfter);
      //添加到路由中
      list.push(...deepMenuAfter);
      // console.log("routes",list);
      commit("SET_MENU_ROUTER", list);
      // 保存所有菜单
      sessionStorage.setItem("menuRouter", JSON.stringify(list));

      // 递归修改一级菜单(修复一级菜单无法跳转)
      setFirstMenuBack.forEach(item => {
        // 菜单类型
        if (shoudLoadComponent(item)) {
          // 菜单
          item.children = [{
            ...item,
            component: _import(item.componentUrl)
          }];
          item.component = Layout;
          item.path = new Date().getTime() + '' + Math.random() * 100;
        }
      })
      // console.log('setFirstMenuBacksetFirstMenuBacksetFirstMenuBack', setFirstMenuBack)
      routers.addRoutes([...getMenuRouter(), ...setFirstMenuBack]);
    }
  }
};

const getChild = menu => {
  var list = [];
  menu.forEach(item => {
    list.push({
      path: item.url,
      name: item.name,
      component: _import(item.url),
      meta: { title: item.name, icon: item.icon }
    });
  });
  return list;
};
const setMenuCategory = (types, menus) => {
  var map = new Map();
  types.forEach(item => {
    menus.forEach(menuItem => {
      if (item.dictValue == menuItem.menuCategory) {
        let list = [];
        list.push(menuItem);
        if (map.get(item.dictValue)) {
          let itemList = map.get(item.dictValue);
          let newArr = list.concat(itemList);
          map.set(item.dictValue, newArr);
        } else {
          map.set(item.dictValue, list);
        }
      }
    });
  });
  return map;
};

export default router;
