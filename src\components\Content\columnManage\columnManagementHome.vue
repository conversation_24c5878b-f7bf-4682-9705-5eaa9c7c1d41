<template>
    <div class="column-management">
        <!-- 专栏搜索栏 -->
        <template>
            <div class="management-box">
                <div>
                    <el-button type="primary" size="mini" @click="addColumn">新建</el-button>
                </div>
                <el-form size="mini" :inline="true" :model="columnManagementSearchForm">
                    <div class="search-box">
                        <el-form-item label="专栏：">
                            <el-input v-model="columnManagementSearchForm.keyword" placeholder="请输入专栏名称"></el-input>
                        </el-form-item>
                        <el-form-item label="上架状态：">
                            <el-select v-model="columnManagementSearchForm.status" placeholder="请选择状态">
                                <el-option label="全部状态" value=" "></el-option>
                                <el-option label="上架" :value="1"></el-option>
                                <el-option label="下架" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="search">
                            <el-button type="primary" size="mini" @click="search">筛选</el-button>
                            <template v-if="uids.length">
                                <el-button type="primary" size="mini" @click="batchUp">上架</el-button>
                                <el-button type="primary" size="mini" @click="batchDown">下架</el-button></el-button>
                                <el-button type="danger" size="mini" @click="bacthDelete">删除</el-button>
                            </template>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
        </template>
        <!-- 表格区域 -->
        <template>
            <el-table @selection-change="handleSelectionChange" class="columnTable" :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
                :cell-style="{ textAlign: 'center' }" :data="columnManagementData" style="width: 100%">
                 <el-table-column
                type="selection"
                width="55">
                </el-table-column>
                <el-table-column type="index" align="center" label="序号" width="60" />
                <el-table-column prop="title" label="名称" width="180">
                </el-table-column>
                <el-table-column prop="summary" show-overflow-tooltip label="简介">
                </el-table-column>
                <el-table-column prop="content" show-overflow-tooltip label="详情">
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间">
                </el-table-column>
                <el-table-column prop="status" label="上架状态" width="180">
                    <template slot-scope="scope">
                        <span>{{scope.row.status==1?'已发布':'未上架'}}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="name" label="所属组合课" width="180">
                </el-table-column> -->
                <!-- <el-table-column prop="name" label="内涵商品" width="180">
                </el-table-column> -->
                <el-table-column fixed="right" prop="address" label="操作">
                    <template slot-scope="scope">
                        <ul class="operation">
                            <li @click="detailColumn(scope.row)">关联课程</li>
                            <li @click="editorColumn(scope.row)">编辑</li>
                            <!-- <li v-if="scope.row.status === 1" @click="downItem(scope.row)">下架</li>
                            <li v-if="scope.row.status === 0" @click="upItem(scope.row)">上架</li> -->
                            <!-- <el-popconfirm @confirm="deleteItem(scope.row)" title="确定要删除该专栏吗？">
                              <li slot="reference">删除</li>
                            </el-popconfirm> -->
                            <!-- <li @click="columnShare(scope.row)">分享</li> -->
                        </ul>
                    </template>
                </el-table-column>
            </el-table>
        </template>

        <!-- 分享弹窗 -->
        <template>
            <el-dialog class="el-qr-dialog" title="二维码" :visible.sync="shareDialogVisible" width="22%" center>
                <div class="qr-code"></div>
            </el-dialog>
        </template>
        <!-- 分页 -->
        <template>
            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="homepageListModel.currentPage" :page-sizes="[5, 10, 20, 30]" :page-size="homepageListModel.pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="homepageListModel.total">
                </el-pagination>
            </div>
        </template>
    </div>
</template>
<script>
import {delColumn, downColumn, getColumnPageList, shelfColumn} from '@/api/content/column'
import {mapMutations} from 'vuex'

export default {
    inject: ['columnManagementThis'],
    data() {
        return {
            uids: [],
            selectList:[],
            shareDialogVisible:false,
            columnManagementData: [
            ],
            total: 0,
            page: 1,
            pageSize: 10,
            columnManagementSearchForm: {
                keyword: "",
                status: ""
            },
            columnManagementHomeForm: {
                stauts: "",
                keyword:""
            },
            homepageListModel: {
                currentPage: 1,
                pageSize: 5,
                total:0
            }
        }
    },
    mounted() {
        this.initColumnByTraining();
        this.innitColumnList();
    },
    methods: {
        handleSelectionChange(datas) { 
            this.uids = datas.map(item => item.uid);
            this.selectList = datas;
        },
        bacthDelete() { 
            if (this.uids.length == 0) return this.$message.warning('请选择要删除的专栏'); 
            // 确认弹窗
             this.$confirm('此操作将永久删除所选数据, 是否继续?', '温馨提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.delColumn(this.uids);
            }).catch(() => {
                        
            });
        },
        batchUp() { 
            if (this.uids.length == 0) return this.$message.warning('请选择要上架的专栏');
            let flag = this.selectList.some(item => item.status == 1);
            if(flag) return this.$message.warning('请选择未上架的专栏');
            this.shelfColumn(this.uids);
        },
        batchDown() { 
            if (this.uids.length == 0) return this.$message.warning('请选择要下架的专栏');
            let flag = this.selectList.some(item => item.status == 0);
            if(flag) return this.$message.warning('请选择未下架的专栏');
            this.downColumn(this.uids);
        },
        ...mapMutations({
            setColumnInfo:'column/setColumnInfo'
        }),
        // 下架单项
        downItem(data) {
            this.downColumn([data.uid]);
        },
        // 下架专栏
        async downColumn(ids) {
            let result = await downColumn(ids);
            if (result.code == this.$ECode.SUCCESS) {
                this.$message.success('下架成功');
                // 刷新专栏列表
                this.innitColumnList();
            } else {
                this.$message.warning(result.message);
            }
        },
      // 上架单项
      upItem(data) {
        this.shelfColumn([data.uid]);
      },
      // 上架专栏
      async shelfColumn(ids) {
        let result = await shelfColumn(ids);
        if (result.code == this.$ECode.SUCCESS) {
          this.$message.success('上架成功');
          // 刷新专栏列表
          this.innitColumnList();
        } else {
          this.$message.warning(result.message);
        }
      },
      // 删除单项
      deleteItem(data) {
        this.delColumn([data.uid]);
      },
      // 删除专栏
      async delColumn(ids) {
        let result = await delColumn(ids);
        if (result.code == this.$ECode.SUCCESS) {
          this.$message.success('删除成功');
          // 刷新专栏列表
          this.innitColumnList();
        } else {
          this.$message.warning(result.message);
        }
      },
        // 专栏数据
        async innitColumnList() {
            let params = {
                status: this.columnManagementSearchForm.status,
                keyword: this.columnManagementSearchForm.keyword,
                currentPage:this.homepageListModel.currentPage,
                pageSize:this.homepageListModel.pageSize
            }
            let result = await getColumnPageList(params);
            if (result.code == this.$ECode.SUCCESS) {
                this.homepageListModel.total = result.data.total;
                this.columnManagementData = result.data.records;
            }
        },
        // 训练营接数据
        initColumnByTraining() {

        },
        columnShare() {
            this.shareDialogVisible = true;
        },
        realSubmit() {

        },
        addColumn() {
            this.columnManagementThis.flag = 1;
        },
        detailColumn(data) {
            this.setColumnInfo(data);
            // 添加标识
            this.columnManagementThis.oprationFlag = 1;
            // 添加专栏
            this.columnManagementThis.flag = 3;
        },
        editorColumn(data) {
            // 保存专栏信息到vuex
            this.setColumnInfo(data);
            // 编辑标识
            this.columnManagementThis.oprationFlag = 2;
            // 编辑专栏
            this.columnManagementThis.flag = 4;
        },
        // 搜索
        search() {
            this.innitColumnList();
        },
        handleSizeChange(val) {
            this.homepageListModel.pageSize = val;
            this.innitColumnList();
        },
        handleCurrentChange(val) {
            this.homepageListModel.currentPage = val;
            this.innitColumnList();
        }
    }
}
</script>
<style lang="scss" scoped>
.column-management {

    .management-box {
        display: flex;
        justify-content: space-between;
    }

    padding: 20px;
    .pagination{
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    .el-qr-dialog{
        /deep/ .el-dialog__body{
            display: flex;
            justify-content: center;
            .qr-code {
                width: 150px;
                height: 150px;
                background: rebeccapurple;
            }
        }
    }
    .columnTable{
        .operation{
            padding: 0;
            margin: 0 auto;
            list-style: none;
            display: flex;
            width: max-content;
            li {
                color: #2a75ed;
                cursor: pointer;
                float: left;
                padding: 0 15px;
                display: flex;
                align-items: center;
                position: relative;
                justify-content: center;
                &::after {
                    content: "";
                    height: 14px;
                    border-right: 1px solid #ebe8e8;
                    right: 0;
                    position: absolute;
                }
                &:last-child::after {
                     border: none;
                }
            }
        }
    }
    .column-management-searchForm {
        .search-box {
            min-height: 80px;
            padding: 10px 0 1px 20px;
            background: #EBEEF5;
            margin: 20px 0;

            .search {
                display: block;
            }
        }
    }
}
</style>
