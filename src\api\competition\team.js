import request from '@/utils/request'

export function teamPage(data) {
  return request({
    url: process.env.ADMIN_API + '/team/page',
    method: 'post',
    data
  })
}
export function teamUserPage(data) {
  return request({
    url: process.env.ADMIN_API + '/team/teamUserPage',
    method: 'post',
    data
  })
}
export function addCompetition(params) {
  return request({
    url: process.env.ADMIN_API + '/competitionManage/add',
    method: 'post',
    data: params
  })
}
