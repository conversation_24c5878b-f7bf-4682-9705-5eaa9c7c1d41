import request from "@/utils/request";

//学员列表
export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/examCenterScore/getList",
    method: "post",
    data:params
  });
}

//学员导入
export function inputExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/inputExcel",
    method: "get",
    params
  });
}

// 导出
export function downloadRecordExcel(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/examCenterScore/exportExcel",
    method: "post",
    data: params,
    responseType: "blob"
  });
}

// 列表
export function getSignList(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/getList",
    method: "post",
    data: params,
  });
}

// 报名审核-审批接口
export function approval(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/trainStudentMsg/checkDocument",
    method: "get",
    params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/trainStudentMsg/edit",
    method: "post",
    data: params
  });
}


// 考试审核-审批接口
export function checkExamSignUp(data = {}) {
  return request({
    url: process.env.ADMIN_API + "/trainExamSignUp/checkExamSignUp",
    method: "post",
    data
  });
}

// 删除
export function delExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/trainExamSignUp/deleteBatch",
    method: "post",
    data: params
  });
}

// 导出
export function downloadExcel(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/trainExamSignUp/downloadExcel",
    method: "post",
    data: params,
    responseType: "blob"
  });
}

// 导出
export function downSingUpRelationList(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/trainSignUp/downSingUpRelationList",
    method: "post",
    data: params,
    responseType: "blob"
  });
}