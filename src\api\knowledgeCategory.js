import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/deleteBatch",
    method: "post",
    data: params
  });
}

export function categoryList() {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/categoryList",
    method: "post"
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeCategory/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
