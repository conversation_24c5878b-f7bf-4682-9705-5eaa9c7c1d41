#app {

  // 主体区域
  .main-container {
    height: 100%;
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: 250px !important;
    position: relative;

    // width: 100%;
    // overflow: auto;
  }

  // 侧边栏
  .sidebar-container {
    transition: width 0.28s;
    min-width: 170px !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    //reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .scrollbar-wrapper {
      height: calc(100% + 15px);

      .el-scrollbar__view {
        height: 100%;
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 130px !important;

      .el-menu-item {
        height: 40px;
        line-height: 40px;
        padding: 0 0 0 10px !important;
      }
      .el-submenu__title{
        color: #999;
        font-size: 12px;
        height: 40px;
        line-height: 40px;
        padding: 0 0 0 10px !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      // width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding-left: 10px !important;
      position: relative;

      .el-tooltip {
        padding: 0 10px !important;
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 0 0 10px !important;
        height: 40px;
        line-height: 40px;
        color: #999;
        font-size: 12px;

        .el-submenu__icon-arrow {
          // display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          padding: 0 0 0 10px !important;
          display: flex;
          align-items: center;
          height: 40px;
          line-height: 40px;
          color: #999;
          font-size: 12px;

          i {
            margin-right: 0 !important;
          }

          // &>span {
          //   height: 0;
          //   width: 0;
          //   overflow: hidden;
          //   visibility: hidden;
          //   display: inline-block;
          // }
        }
      }
    }
  }

  .sidebar-container .nest-menu .el-submenu>.el-submenu__title,
  .sidebar-container .el-submenu .el-menu-item {
    width: 100% !important;
    height: 40px;
    line-height: 40px;
    padding-left: 10px !important;
  }

  .el-menu--collapse .el-menu .el-submenu {
    width: 100% !important;
    min-width: 100% !important;
  }

  //适配移动端
  .mobile {
    // .main-container {
    //   margin-left: 0px;
    // }

    // .sidebar-container {
    //   transition: transform .28s;
    //   width: 180px !important;
    // }

    // &.hideSidebar {
    //   .sidebar-container {
    //     transition-duration: 0.3s;
    //     transform: translate3d(-180px, 0, 0);
    //   }
    // }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}