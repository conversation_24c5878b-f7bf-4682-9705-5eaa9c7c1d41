<template>
    <div>
        <el-dialog :visible="infoDialog" width="70%" :title="infoItem.title" :show-close="false">
            <div class="creat_time">
                <div class="time">发布时间:{{ infoItem.createTime }}</div>
                <div class="div_info">{{ infoItem.type | filterType }}</div>
                <div class="clone_btn" @click="infoDialog = false">
                <i class="el-icon-close"></i>
                </div>
            </div>
            <div class="content ql-editor" v-html="infoItem.detail">

            </div>


        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        infoItem: {
            type: Object,
            default: () => { return {} }
        }
    },
    filters: {
        filterType: function (val) {
            if (val == 1) {
                return '国赛';
            } else if (val == 2) {
                return '省赛';
            } else if (val == 3) {
                return '行业赛';
            } else if (val == 4) {
                return '企业赛';
            }
        }
    },
    data() {
        return {
            infoDialog: false,
        }
    },
}
</script>

<style lang="scss" scoped>
/deep/.el-dialog__body {
    padding: 5px 20px;


    .creat_time {
        height: 40px;
        border-bottom: 1px solid #ccc;
        display: flex;
        flex-direction: row;
        align-items: center;

        .time {
            font-size: 14px;
            color: #666;
        }

        .div_info {
            margin: -3px 20px;
            border: 1px solid#006eff !important;
            padding: 5px 6px;
            border-radius: 5px;
            line-height: 14px;
            color: #006eff !important;
            height: 25px;

        }

        .clone_btn {
            cursor: pointer;
            flex: 1;
            justify-content: end;
            display: flex;
            margin-bottom: 105px;
        }
    }

    .content {
        margin-top: 20px;

        img {
            width: 100%;
        }

        iframe {
            width: 100%;
        }
    }


}
</style>