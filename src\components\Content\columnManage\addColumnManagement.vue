<template>
    <div class="add-column-management">
        <el-form :rules="addColumnManagementRule" label-position="right" class="addColumnManagementForm" ref="addColumnManagementForm" label-width="100px" :model="addColumnManagementForm">
            <div class="model-title">
                <span>基本信息</span>
            </div>
            <el-form-item label="名称" prop="name">
                <el-input v-model="addColumnManagementForm.name"></el-input>
            </el-form-item>
            <el-form-item label="封面" prop="pictureUid">
                <el-upload class="avatar-uploader"
                :auto-upload="false"
                :on-change="changeCover" action="" :show-file-list="false">
                    <img v-if="addColumnManagementForm.imageUrl" :src="addColumnManagementForm.imageUrl" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </el-form-item>
            <el-form-item label="简介" prop="summary">
                <el-input type="textarea" rows="6" v-model="addColumnManagementForm.summary"></el-input>
            </el-form-item>
            <el-form-item label="详情" prop="content">
                <el-input type="textarea" rows="6" v-model="addColumnManagementForm.content"></el-input>
            </el-form-item>
            <!-- <div class="model-title">
                <span>课程内容</span>
            </div> -->
            <!-- <el-form-item label="上传电子证书">
                <el-upload class="upload-demo" action=""
                    :on-change="eBookFileCover"
                    :on-remove="eBookRemove"  multiple :limit="1"
                    :file-list="addColumnManagementForm.eBookFileList">
                    <el-button size="small" type="primary">选择文件</el-button>
                </el-upload>
            </el-form-item> -->
            <!-- <el-form-item label="内容保护设置">
                <el-checkbox
                :true-label="1"
                :false-label="0"
                v-model="addColumnManagementForm.contentSafe">
                    开启水印
                    <span>&nbsp;&nbsp;开启后，学员在浏览课程时会显示用户名及用户ID</span>
                </el-checkbox>
            </el-form-item> -->
            <!-- <el-form-item label="相关资料">
                <el-upload class="upload-demo" action="" :on-change="eBookFileCover" :on-remove="eBookRemove" multiple :limit="1"
                    :file-list="addColumnManagementForm.eBookFileList">
                    <el-button size="small" type="primary">添加资料</el-button>
                </el-upload>
            </el-form-item> -->
            <div class="model-title">
                <span>所属组合课</span>
            </div>
            <el-form-item label="所属组合课">
                <el-button @click="addCombinationClass">添加视频/训练营</el-button>
            </el-form-item>
            <!-- 所选择的文件组合课 -->
            <div class="column-and-trainList">
                <div class="course-item" v-for="item in columnAndTrainList" :key="item.uid">
                    <span>{{ item.name }}</span>
                    <el-tag>{{ item.type == 1 ? '训练营' : item.type == 2?'视频' : '专栏' }}</el-tag>
                </div>
            </div>
            <div class="model-title">
                <span>商品信息</span>
            </div>
            <el-form-item label="排序规则">
                <el-radio-group v-model="addColumnManagementForm.updateOrder">
                    <el-radio :label="0">最新添加的在前</el-radio>
                    <el-radio :label="1">最新添加的在后</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="售卖方式">
                <el-checkbox :true-label="1" :false-label="0" v-model="addColumnManagementForm.seleWay">
                    开启水印
                    <span>&nbsp;&nbsp;客户可以通过店铺或链接的方式单独购买该商品</span>
                </el-checkbox>
            </el-form-item> -->
            <!-- <el-form-item label="">
                <el-radio-group v-model="addColumnManagementForm.seleWayType">
                    <el-radio :label="0">免费</el-radio>
                    <el-radio :label="1">付费</el-radio>
                    <el-radio :label="2">加密</el-radio>
                    <el-radio :label="3">指定学院</el-radio>
                </el-radio-group>
            </el-form-item> -->
            <!-- <el-form-item label="" class="express-box">
                <div class="express">
                    <el-form-item label="有效期:">
                        <el-radio-group v-model="addColumnManagementForm.expressType">
                            <el-radio :label="0">长期有效</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
            </el-form-item> -->
            <!-- <el-form-item label="商品分组" class="shop-group">
                <el-select v-model="addColumnManagementForm.shopCateId" placeholder="请选择">
                    <el-option label="区域一" value="shanghai"></el-option>
                </el-select>
                <span class="refresh">刷新</span>
            </el-form-item> -->
            <div class="model-title">
                <span>状态设置</span>
            </div>
            <el-form-item label="价格">
              <el-radio-group v-model="addColumnManagementForm.sellingprice">
                <el-input v-model="addColumnManagementForm.sellingprice"></el-input>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="上架设置">
              <el-radio-group v-model="addColumnManagementForm.putWayStatus">
                <el-radio :label="1">立即上架</el-radio>
                <el-radio :label="0">暂不上架</el-radio>
              </el-radio-group>
            </el-form-item>
<!--            <el-form-item label="" class="more-set" v-if="addColumnManagementForm.putawaySetType==0">-->
<!--                <div class="more-set-box">-->
<!--                    <span>更多设置</span>-->
<!--                    &lt;!&ndash; <el-radio-group class="more-set-radio" v-model="addColumnManagementForm.putWayStatus">-->
<!--                        <el-radio :label="0">定时下架</el-radio>-->
<!--                        <el-radio :label="1">暂不下架</el-radio>-->
<!--                    </el-radio-group> &ndash;&gt;-->
<!--                    <el-checkbox :true-label="1" :false-label="0" v-model="addColumnManagementForm.putWayStatus">-->
<!--                        定时下架-->
<!--                    </el-checkbox>-->
<!--                    <el-form-item label="" v-if="(addColumnManagementForm.putWayStatus ==1)">-->
<!--                        <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="addColumnManagementForm.timingSoldOutTime" type="datetime" placeholder="选择日期时间">-->
<!--                        </el-date-picker>-->
<!--                    </el-form-item>-->
<!--                </div>-->
<!--            </el-form-item>-->
            <el-form-item label="隐藏设置">
                <el-checkbox :true-label="1" :false-label="0" v-model="addColumnManagementForm.hideSet">
                    隐藏
                    <span>&nbsp;&nbsp;不可通过搜索或列表进行访问，仅可通过链接方式访问</span>
                </el-checkbox>
            </el-form-item>
            <el-form-item label="销售状态">
                <el-checkbox :true-label="1" :false-label="0" v-model="addColumnManagementForm.saleStatus">
                    暂停销售
                </el-checkbox>
            </el-form-item>
            <el-form-item label="是否推荐">
              <el-radio-group v-model="addColumnManagementForm.level">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否试听">
                <el-radio-group v-model="addColumnManagementForm.hasTrySee">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="关联证书" prop="certificateUid">
                <el-select v-model="addColumnManagementForm.certificateUid" size="small" placeholder="关联证书" filterable>
                    <el-option v-for="item in trainCertificateList" :key="item.uid" :label="item.shortName" :value="item.uid" />
                </el-select>
            </el-form-item>
        </el-form>
        <div class="btn" :style="{ width: sidebar.opened?'calc(100% - 180px)':'calc(100% - 35px)'}">
            <el-button @click="cencel">取消</el-button>
            <el-button @click="realAdd">{{ columnManagementThis.flag==1?'确认添加':'确认编辑'}}</el-button>
        </div>
        <!-- 添加专栏/训练营弹窗 -->
        <template>
            <el-dialog class="columnAndTrainDialog" :title="addType==2?'添加视频':'添加训练营'" :visible.sync="columnAndTrainDialogVisible" width="38%" center>
                <el-tabs v-model="addType" @tab-click="addColumnType">
                    <!-- <el-tab-pane label="专栏" name="0">
                        <el-form :inline="true" size="mini" :model="columnModel.searchForm">
                            <el-form-item label="">
                                <el-input v-model="columnModel.searchForm.keyword" placeholder="专栏名称"></el-input>
                            </el-form-item>
                            <el-form-item label="">
                                <el-button @click="columnSearch">搜索</el-button>
                            </el-form-item>
                        </el-form>
                        <el-table v-loadMore="columnLoad" tooltip-effect="dark" @selection-change="columnSelectionChange" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" :data="columnModel.columnList" height="300" style="width: 100%">
                            <el-table-column type="selection" width="55">
                            </el-table-column>
                            <el-table-column prop="title" label="标题" width="180">
                            </el-table-column>
                            <el-table-column prop="summary" label="简介" width="180">
                            </el-table-column>
                            <el-table-column prop="content" label="详情">
                            </el-table-column>
                        </el-table>
                    </el-tab-pane> -->
                    <el-tab-pane label="视频" name="2">
                        <el-table v-loadMore="videoLoad" @selection-change="videoSelectionChange" tooltip-effect="dark" :cell-style="{ textAlign: 'center' }"
                            :header-cell-style="{ textAlign: 'center' }" :data="videoModel.videoList" height="300"
                            style="width: 100%">
                            <el-table-column type="selection" width="55">
                            </el-table-column>
                            <el-table-column show-overflow-tooltip="" prop="uid" label="uid" width="180">
                            </el-table-column>
                            <el-table-column prop="enrollment" label="封面">
                                <template slot-scope="scope">
                                    <img width="100" :src="scope.row.fileUrl" alt="">
                                </template>
                            </el-table-column>
                            <el-table-column prop="title" label="名称" width="180">
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="训练营" name="1">
                        <!-- 搜索表单 -->
                        <el-form :inline="true" size="mini" :model="trainingModel.searchForm">
                            <el-form-item label="">
                                <el-input v-model="trainingModel.searchForm.campName" placeholder="训练营名称"></el-input>
                            </el-form-item>
                            <el-form-item label="">
                                <el-button @click="trainingSearch">搜索</el-button>
                            </el-form-item>
                        </el-form>
                        <el-table @selection-change="trainingSelectionChange" tooltip-effect="dark" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" :data="trainingModel.trainingList" height="300" style="width: 100%">
                            <el-table-column type="selection" width="55">
                            </el-table-column>
                            <el-table-column prop="campName" label="名称" width="180">
                            </el-table-column>
                            <el-table-column prop="campPeriodNumber" label="营期数" width="180">
                            </el-table-column>
                            <el-table-column prop="enrollment" label="报名数">
                            </el-table-column>
                        </el-table>
                        <el-pagination
                            background
                            style="text-align: right; margin: 10px 0"
                            @current-change="handleCurrentChange"
                            :current-page.sync="trainingModel.trainingPage"
                            :page-size="trainingModel.trainingPageSize"
                            layout="total, prev, pager, next"
                            :total="trainingModel.total">
                        </el-pagination>
                    </el-tab-pane>
                </el-tabs>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="columnAndTrainDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="columnAndTrainDialogVisible = false">确 定</el-button>
                </span>
            </el-dialog>
        </template>
    </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/commonUtil'
import { videoList } from "@/api/content/video";
import { getColumnPageList, addColumn } from '@/api/content/column'
import { getCampList } from '@/api/content/trainingCamp'
import { getTrainCertificateList } from '@/api/authentication'
import { getToken } from '@/utils/auth'
import { uploadFile } from '@/api/upload'
export default {
    inject: ['columnManagementThis'],
    data() {
        return {
            trainCertificateList: [],
            videoSelect:[],
            columnSelect: [],
            trainingSelect:[],
            addType: '2',
            columnAndTrainDialogVisible:false,
            columnModel: {
                total:0,
                columnPage: 1,
                columnPageSize: 5,
                columnList: [],
                searchForm: {
                    keyword:''
                }
            },
            trainingModel: {
                total:0,
                trainingPage: 1,
                trainingPageSize: 5,
                trainingList: [],
                searchForm: {
                    campName:""
                }
            },
            videoModel: {
                total: 0,
                videoPage: 1,
                videoPageSize: 5,
                videoList: []
            },
            addColumnManagementForm: {
                sellingprice: '',
                hasTrySee:0,
                level: 1,
                certificateUid: "",
                updateOrder:0,
                saleStatus:0,
                imageUrl: '',
                pictureUid:'',
                eBookFileList: [],
                contentSafe: 0,
                summary: "",
                content:"",
                seleWay: 0,
                seleWayType: 0,
                expressType: 0,
                putWayStatus:0,
                putawaySetType: 1,
                moreSetStatus: 0,
                hideSet: 0,
                name:"",
                timingSoldOutTime: "",
                campIds: "",
                bigColumnIds:"",
            },
            addColumnManagementRule: {
                name: [{ required: true, message: '请输入训练营名称', trigger: 'blur' }],
                pictureUid: [{ required: true, message: '请上传封面图片', trigger: 'blur' }],
                summary: [{ required: true, message: '请输入训练营简介', trigger: 'blur' }],
                content: [{ required: true, message: '请输入训练营详情', trigger: 'blur' }],
            }
        }

    },
    computed: {
        sidebar() {
            return this.$store.state.app.sidebar;
        },
        ...mapGetters(['columnInfo']),
        // 所属组合课列表
        columnAndTrainList: ({ columnSelect, trainingSelect,videoSelect }) => {
            return [...columnSelect, ...trainingSelect, ...videoSelect];
        }
    },
    watch: {
        columnAndTrainList(newVal, oldVal) {

            this.addColumnManagementForm.bigColumnIds = newVal.filter(item => {
                return item.type==3;
            }).map(item => {
                return item.uid;
            }).join(",");
            this.addColumnManagementForm.campIds = newVal.filter(item => {
                return item.type==1;
            }).map(item => {
                return item.uid;
            }).join(",");
        }
    },
    async mounted() {
        this.dataReSet();
        this.getCertificateList();
        this.intiColumnData();
        this.initTrainingData();
        this.initVideoInfo();
    },
    methods: {
        handleCurrentChange(val) {
            this.trainingModel.trainingPage = val
            this.initTrainingData()
        },
        // 关联认证列表
        getCertificateList() {
            getTrainCertificateList().then(res => {
                if (res.code == this.$ECode.SUCCESS) {
                    this.trainCertificateList = res.data;
                }
            })
        },
        // 滑到底部进行加载
        columnLoad() {
            debounce(async () => {
                if (this.columnModel.columnList.length >= this.columnModel.total) return;
                this.columnModel.columnPage++;
                let params = {
                    currentPage: this.columnModel.columnPage,
                    pageSize: this.columnModel.columnPageSize,
                    keyword: this.columnModel.searchForm.keyword
                }
                let result = await getColumnPageList(params);
                if (result.code == this.$ECode.SUCCESS) {
                    this.columnModel.total = result.data.total;
                    this.columnModel.columnList.push(...result.data.records);
                }
            })();
        },
        videoLoad() {
            debounce(async () => {
                if (this.videoModel.videoList.length >= this.videoModel.total) return;
                this.videoModel.videoPage++;
                let params = {
                    pageSize: this.videoModel.videoPageSize,
                    currentPage: this.videoModel.videoPage,
                };
                let result = await videoList(params);
                if (result.code == this.$ECode.SUCCESS) {
                    this.videoModel.total = result.data.total;
                    this.videoModel.videoList.push(...result.data.records);
                }
            })();
        },
        columnSelectionChange(data) {
            // 专栏
            this.columnSelect = data.map(item => {
                return {
                    name:item.title,
                    ...item,
                    type:3
                }
            });
        },
        // 视频
        videoSelectionChange(data) {
            this.videoSelect = data.map(item => {
                return {
                    name: item.title,
                    ...item,
                    type: 2,
                }
            });
        },
        // 训练营
        trainingSelectionChange(data) {
            this.trainingSelect = data.map(item => {
                return {
                    name: item.campName,
                    ...item,
                    type:1,
                }
            });
        },
        columnSearch() {
            this.columnModel.columnPage = 1;
            this.intiColumnData();
        },
        trainingSearch() {
            this.trainingModel.columnPage = 1;
            this.initTrainingData();
        },
        addColumnType(val) {

        },
        cencel() {
            this.columnManagementThis.flag = 2;
        },
        async realAdd() {
            let trainModuleContentList = this.columnAndTrainList.map(item => {
                return {
                    contentType: item.type,
                    contentUid: item.uid,
                    name: item.type == 1 ? item.campName : item.title
                }
            });
            let params = {
                level: this.addColumnManagementForm.level,
                sellingPrice: this.addColumnManagementForm.sellingprice ? this.addColumnManagementForm.sellingprice:'' ,
                certificateUid: this.addColumnManagementForm.certificateUid,
                hasTrySee: this.addColumnManagementForm.hasTrySee,
                title: this.addColumnManagementForm.name,
                summary: this.addColumnManagementForm.summary,
                fileUid: this.addColumnManagementForm.pictureUid,
                content: this.addColumnManagementForm.content,
                updateOrder: this.addColumnManagementForm.updateOrder,
                bigColumn: this.addColumnManagementForm.bigColumnIds,
                camp: this.addColumnManagementForm.campIds,
                stopSelling: this.addColumnManagementForm.stopSelling,
                hide: this.addColumnManagementForm.hideSet,
                status: this.addColumnManagementForm.putWayStatus,
                timerDownTime: this.addColumnManagementForm.timingSoldOutTime,
                trainModuleContentList
            };
            this.$refs['addColumnManagementForm'].validate(async (valid) => {
                if (valid) {
                    let result = await addColumn(params);
                    if (result.code === this.$ECode.SUCCESS) {
                        this.columnManagementThis.flag = 2;
                        this.$message.success('添加专栏成功');
                        this.intiColumnData();
                    } else {
                        this.$message.warning(result.message);
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        async intiColumnData() {
            let params = {
                currentPage: this.columnModel.columnPage,
                pageSize: this.columnModel.columnPageSize,
                keyword: this.columnModel.searchForm.keyword
            }
            let result = await getColumnPageList(params);
            if (result.code == this.$ECode.SUCCESS) {
                this.columnModel.total = result.data.total;
                this.columnModel.columnList = result.data.records;
            }
        },
        async initTrainingData() {
            let params = {
                currentPage: this.trainingModel.trainingPage,
                pageSize: this.trainingModel.trainingPageSize,
                keyword: this.trainingModel.searchForm.campName
            }
            let result = await getCampList(params);
            if (result.code == 200) {
                this.trainingModel.total = result.data.total;
                this.trainingModel.trainingList = result.data.records;
            }
        },
        // 加载视频库信息
        async initVideoInfo() {
            let params = {
                pageSize: this.videoModel.videoPageSize,
                currentPage: this.videoModel.videoPage,
            };
            let result = await videoList(params);
            if (result.code == this.$ECode.SUCCESS) {
                this.videoModel.total = result.data.total;
                this.videoModel.videoList = result.data.records;
            } else {
                this.$commonUtil.message.error(res.message);
            }
        },
        // 数据初始化
        dataReSet() {
            this.columnModel.total = 0;
            this.columnModel.columnPage = 1;
            this.columnModel.columnPageSize = 5;
            this.trainingModel.total = 0;
            this.trainingModel.columnPage = 1;
            this.trainingModel.columnPageSize = 5;
            this.trainingModel.searchForm.campName = '';
            this.columnModel.searchForm.keyword = '';
            this.addColumnManagementForm = {
                sellingprice: '',
                level: 1,
                certificateUid: "",
                updateOrder:0,
                saleStatus:0,
                imageUrl: '',
                pictureUid:'',
                eBookFileList: [],
                contentSafe: 0,
                summary: "",
                content:"",
                seleWay: 0,
                seleWayType: 0,
                expressType: 0,
                putWayStatus:0,
                putawaySetType: 1,
                moreSetStatus: 0,
                hideSet: 0,
                name:"",
                timingSoldOutTime: "",
                campIds: "",
                bigColumnIds:"",
            }
        },
        addCombinationClass() {
            this.columnAndTrainDialogVisible = true;
        },
        eBookRemove() {

        },
        eBookFileCover(file, fileList) {

        },
        // 封面上传
        changeCover(file, fileList) {
            console.warn(file)
            let whiteList = ['image/jpeg', 'image/png', 'image/gif'];
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
                this.$message.warning("图片大小不能超过2M");
                return;
            }
            if (!whiteList.includes(file.raw.type)) {
                this.$message.warning("请上传指定文件格式");
                return;
            }
            let formData = new FormData();
            formData.append("file", file.raw);
            formData.append("token", getToken());
            formData.append("source", "picture");
            formData.append("projectName", "blog");
            formData.append("sortName", "admin");
            formData.append("file", file.raw);
            uploadFile(formData).then(res => {
                if (res.code == this.$ECode.SUCCESS) {
                    this.addColumnManagementForm.pictureUid = res.data[0].uid;
                    this.addColumnManagementForm.imageUrl = res.data[0].url;
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    border: 1px dashed #d9d9d9;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
.add-column-management{
    padding: 20px 20px 60px 20px;
    .columnAndTrainDialog{
        /deep/ .el-dialog__body{
            padding: 0 25px;
        }
    }
    .btn{
        transition: all .5s;
        z-index: 99;
        width: calc(100% - 180px);
        right: 0;
        position: fixed;
        bottom: 0;
        background: white;
        box-shadow: 0 0 10px rgb(209, 209, 209);
        align-items: center;
        display: flex;
        justify-content: center;
        height: 60px;
    }
    .addColumnManagementForm{
        .column-and-trainList{
            .course-item{
                justify-content: space-between;
                border-radius: 5px;
                display: flex;
                align-items: center;
                margin: 10px 0;
                width: 400px;
                height: 50px;
                padding: 20px;
                background: #f7f5f5;
            }
        }
        .more-set{
            .more-set-box{
                width:  700px;
                height: 180px;
                padding: 20px 0 20px 20px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                background: #f5f5f5;
                span {
                    color: rgb(150, 150, 150);
                    font-size: 18px;
                    margin-bottom: 10px;
                }
                .more-set-radio{
                    display: flex;
                    flex-direction: column;
                    .el-radio{
                        margin-bottom: 15px;
                    }
                }
            }
        }
        .shop-group{
            .refresh{
                cursor: pointer;
                color: #409EFF;
                font-size: 14px;
                margin-left:15px;
            }
        }
        .express-box{
            .express{
                width: 450px;
                height: 80px;
                display: flex;
                align-items: center;
                background: #f5f5f5;
            }
        }
        .model-title {
            font-size: 19px;
            margin-bottom: 40px;
            color: #525252;
        }
    }
}
/deep/.el-table__body-wrapper {
    overflow-y: auto;
}
</style>
