<template>
  <div class="experiment-env">
    <el-form ref="formData" :model="formData" :rules="rules" label-width="100px" class="demo-formData">
      <el-form-item label="登陆方式" prop="loginType">
        <el-radio-group v-model="formData.loginType">
          <el-radio-button label="密码" />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model="formData.username" maxlength="20" show-word-limit disabled />
      </el-form-item>
      <el-form-item label="密码" prop="security">
        <el-input v-model="formData.security" show-password minlength="8" maxlength="30" show-word-limit
          placeholder="8-30个字符，且同时包含其中三项（大写字母、小写字母、数字、特殊符号）" class="security_input" />
      </el-form-item>
      <el-form-item label="数量" prop="count">
        <el-input v-model="formData.count" type="number" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'EditLogin',
  components: {},
  // eslint-disable-next-line vue/require-prop-types
  props: ['isSubmitLogin', 'formData'],
  data() {
    var validateCount = (rule, value, callback) => {
      if (value < 1 || value > 500) {
        callback(new Error('数量范围为1-500'))
      } else {
        callback()
      }
    }
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        // this.$refs.ruleForm.validateField('checkPass');
        const reg =
          /((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,30}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,30}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{8,30}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{8,30}$))/
        if (!reg.test(value)) {
          callback(
            new Error(
              '请输入8-30个字符，且同时包含其中三项（大写字母、小写字母、数字、特殊符号）'
            )
          )
        } else {
          const tes = /^[^\u4e00-\u9fa5]+$/
          if (!tes.test(value)) {
            callback(new Error('请勿输入中文！'))
          } else {
            callback()
          }
        }
      }
    }
    return {
      rules: {
        security: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' }
        ],
        count: [{ validator: validateCount, trigger: 'blur' }]
      }
    }
  },
  watch: {
    isSubmitLogin: function (newVal) {
      console.log(newVal)
      if (newVal) {
        this.$parent.disFlag = true
        // 提交
        this.$refs['formData'].validate(async (valid) => {
          if (valid) {
            console.log('通过验证')
            this.$emit('formSuccess')
            this.$emit('_formOk')
          } else {
            this.$parent.disFlag = false
            this.$emit('formError')
            return false
          }
        })
      }
    }
  },
  async mounted() { },
  methods: {
    async _confirm() {
      this.topologyId = this.currentRow.id
      this.formData.topology_id = this.currentRow.id
      this.formData.topology_name = this.currentRow.name
    }
  }
}
</script>

<style lang="scss" scoped>
.experiment-env {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  box-sizing: border-box;

  >div {
    width: 100%;
    min-height: 30px;
    // @include flex(flex-start);
    display: flex;
    align-items: flex-start;
    font-size: 14px;

    label {
      display: inline-block;
      text-align: right;
      line-height: 30px;
      width: 70px;
      height: 30px;
      vertical-align: top;
    }
  }

  .el-form::v-deep {
    .security_input {
      input {
        &::-webkit-input-placeholder {
          font-size: 12px;
        }

        &::-moz-placeholder {
          font-size: 12px;
        }

        &:-ms-input-placeholder {
          font-size: 12px;
        }
      }
    }
  }
}

.el-table {
  .testPaperTableDataRadio::v-deep {
    .el-radio__label {
      display: none;
    }
  }
}

.dialog::v-deep {
  .el-dialog {
    margin-top: 6vh !important;
  }
}
</style>
