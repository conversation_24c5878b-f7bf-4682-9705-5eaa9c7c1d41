<template>
    <div class="w-100 flex ai-center">
        <div class="flex-col w-100">
            <el-form>
                <div class="flex jc-between">
                    <div class="flex ai-center">
                        <el-form-item label="" v-if="type != 3">
                            <el-input v-model="mobile" size="small" type="number" :min="0" maxlength="11" :clearable="true"
                                placeholder="请输入用户联系方式" @clear="searchUserPhone(1)" />
                        </el-form-item>
                        <el-form-item class="ml-10" label="" v-if="[1, 4].includes(type)">
                            <el-input v-model="realName" size="small" placeholder="请输入用户姓名/昵称" :clearable="true"
                                @clear="searchUserPhone(1)" />
                        </el-form-item>
                        <el-form-item class="ml-10" label="" v-if="[3].includes(type)">
                            <el-input v-model="realName" size="small" placeholder="请输入讲者姓名" :clearable="true"
                                @clear="searchUserPhone(1)" />
                        </el-form-item>
                        <el-form-item class="ml-10" label="" v-if="[3].includes(type)">
                            <el-input v-model="mobile" size="small" placeholder="请输入讲者联系方式" :clearable="true"
                                @clear="searchUserPhone(1)" />
                        </el-form-item>
                        <el-form-item label="" v-if="type == 2" style="margin-left:10px">
                            <el-input v-model="name" size="small" placeholder="输入讲者昵称" :clearable="true" />
                        </el-form-item>
                        <el-form-item label="" v-if="type == 2 || type == 3" style="margin-left:10px">
                            <el-select size="mini" v-model="category" :clearable="true" placeholder="请选择讲者分类">
                                <el-option v-for="item in speakerOptions" :key="item.uid" :label="item.name"
                                    :value="item.uid">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="">
                            <el-button icon="el-icon-search" size="mini" type="primary" @click="searchUserPhone(1)">
                                查询
                            </el-button>
                        </el-form-item>
                    </div>
                    <!-- {{ type }} -->
                </div>
            </el-form>
            <div class="mt-20 w-100">
                <el-table :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
                    :cell-style="{ textAlign: 'center' }" :data="relevanceList" style="width: 100%;">
                    <el-table-column prop="avatarUrl" v-if="[1, 4].includes(type)" label="用户头像">
                        <template slot-scope="scope">
                            <img :src="scope.row.avatarUrl || 'https://cdn.wuzhiing.cn/hyfile/300.jpg'"
                                style="width:40px;height:40px;border-radius:50%;" alt="" srcset="">
                        </template>
                    </el-table-column>
                    <el-table-column prop="nickName" v-if="[1].includes(type)" label="用户昵称">
                    </el-table-column>
                    <el-table-column prop="realName" v-if="[1].includes(type)" label="用户姓名">
                    </el-table-column>
                    <el-table-column prop="nickName" v-if="[4].includes(type)" label="用户昵称">
                    </el-table-column>
                    <el-table-column prop="realName" v-if="[4].includes(type)" label="用户姓名">
                    </el-table-column>
                    <el-table-column prop="name" v-if="[3].includes(type)" label="讲者姓名">
                    </el-table-column>
                    <!-- <el-table-column :prop="nikeObj[type]" :label="`${[1, 4].includes(type) ? '用户姓名' : '讲者姓名'}`">
                        <template slot-scope="{row}">
                            <span>{{ row[nikeObj[type]] || '--' }}</span>
                        </template>
                    </el-table-column> -->
                    <el-table-column :prop="mobileObj[type]" v-if="type == 2 || type == 3" label="联系方式">
                    </el-table-column>
                    <!-- <el-table-column :prop="nikeObj[type]" :label="`${[1, 4].includes(type) ? '用户昵称' : '讲者昵称'}`">
                    </el-table-column> -->
                    <el-table-column prop="mobile" v-if="[1, 4].includes(type)" label="联系电话">
                    </el-table-column>
                    <el-table-column prop="wechatNumber" label="微信号" v-if="type == 2 || type == 3">
                    </el-table-column>
                    <el-table-column prop="intro" label="讲者简介" v-if="type == 2 || type == 3">
                    </el-table-column>
                    <el-table-column prop="categoryName" label="讲者分类" v-if="type == 2 || type == 3">
                    </el-table-column>
                    <el-table-column prop="gender" label="性别" width="60" v-if="type != 3">
                        <template slot-scope="scope">
                            {{ gender[scope.row.gender] || scope.row.gender || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" align="left">
                        <template slot-scope="scope">
                            <!-- 讲者 -->
                            <div style="width: max-content;margin: 0 auto;" class="flex jc-center" @click.stop=""
                                v-if="[4].includes(type)">
                                <p class="cursor" :style="`color:${scope.row.hasBoundSpeaker ? '#d7dddd' : '#006EFF'};`"
                                    @click="relevanceUser(scope.row)">选择
                                </p>
                            </div>
                            <div style="width: max-content;margin: 0 auto;" class="flex jc-center" @click.stop=""
                                v-if="[1].includes(type)">
                                <p class="cursor" :style="`color:${scope.row.boundSpeakerUid ? '#d7dddd' : '#006EFF'};`"
                                    @click="relevanceUser(scope.row)">选择
                                </p>
                            </div>
                            <div style="width: max-content;margin: 0 auto;" class="flex" @click.stop=""
                                v-if="type == 2 || type == 3">
                                <p class="cursor" style="color:#006EFF;" @click="relevanceUser(scope.row)">选择
                                </p>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="mt-10 flex jc-end w-100">
                <el-pagination @size-change="handleRelevanceSizeChange" @current-change="handleRelevanceCurrentChange"
                    :current-page="relevanceParams.currentPage" :page-sizes="[5, 10, 20, 30, 40, 50, 60, 100]"
                    :page-size="relevanceParams.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import { getList } from '@/api/lecturer.js'
import { debounce } from "@/utils/commonUtil";
import { getUserByPhoneSpeaker, getUserCreator, getUserByPhoneCreater, getUserByPhoneEnterpriseSpeak } from '@/api/enterprisesService.js';
export default {
    name: '',
    components: {

    },
    data() {
        return {
            realName: '',
            name: '',
            category: '',
            mobileObj: {
                1: 'userName',
                2: 'mobile',
                3: 'mobile',
                4: 'userName'
            },
            nameObj: {
                1: 'realName',
                2: 'userName',
                3: 'userName',
                4: 'realName',
            },
            nikeObj: {
                1: 'nickName',
                2: 'name',
                3: 'name',
                4: 'nickName'
            },
            gender: {
                "1": "男",
                "2": "女"
            },
            mobile: '',
            total: 0,
            relevanceParams: {
                pageSize: 5,
                currentPage: 1,
            },
            speakerOptions: [],
            relevanceList: [],//关联用户列表
        }
    },
    watch: {
        mobile: function (newValue, oldValue) {
            // 使用正则表达式来限制只能输入数字
            const reg = /^(0|[1-9]\d{0,10})?$/;
            if (!reg.test(newValue)) {
                // 非数字则将输入值重置为之前的值
                this.mobile = oldValue || '';
            }
        }
    },
    computed: {

    },
    created() {

    },
    props: {
        // 1讲者 2企业创建人 3企业讲者
        type: {
            type: Number,
            default: 1
        },
        //创建人uid 企业讲者时传
        uid: {
            type: String,
            default: ''
        },
        enterpriseUid: {
            type: String,
            default: ''
        }
    },
    mounted() {
        this.searchUserPhone(1);
        if (this.type == 2 || this.type == 3) {
            this.initCategoryList1()
        }
    },
    methods: {
        async initCategoryList1() {
            let params = {
                name: '',
                status: 1,
                type: 1,
                currentPage: 1,
                pageSize: 9999999
            }
            let result = await getList(params);
            if (result.code === this.$ECode.SUCCESS) {
                this.speakerOptions = [{ uid: '', name: '全部' }, ...result.data.records];
            }
        },
        //关联用户分页
        handleRelevanceSizeChange(val) {
            this.relevanceParams.pageSize = val;
            this.searchUserPhone();
        },
        //关联用户分页
        handleRelevanceCurrentChange(val) {
            this.relevanceParams.currentPage = val;
            this.searchUserPhone();
        },
        //根据号码查询用户
        searchUserPhone(num = 0) {
            const reqObj = {
                1: getUserByPhoneSpeaker,
                2: getUserByPhoneCreater,
                3: getUserByPhoneEnterpriseSpeak,
                4: getUserCreator
            }
            const req = reqObj[this.type]
            let params = {}
            //讲者
            if (this.type == 1) {
                params = { ...this.relevanceParams, userName: this.mobile, realName: this.realName }
            }
            //更换实验室主任
            if (this.type == 4) {
                params = { ...this.relevanceParams, userName: this.mobile, realName: this.realName }
            }
            //企业创建人
            if (this.type == 2) {
                params = { ...this.relevanceParams, phone: this.mobile, name: this.name, category: this.category }
            }
            //企业讲者
            if (this.type == 3) {
                params = { ...this.relevanceParams, phone: this.mobile, name: this.realName, category: this.category, enterpriseUid: this.enterpriseUid || '', uid: this.uid || '' }
            }
            if (num == 1) {
                this.relevanceParams.currentPage = 1
            }
            // console.log('当前类型', this.type, '当前参数', params)
            req(params).then(res => {
                if (res.code == 200) {
                    const { records, total } = res.data
                    this.relevanceList = records
                    this.total = total
                }
                console.log('获取号码列表', this.relevanceList)
            }).catch(rej => {
                console.log('获取号码列表失败', rej)
            })
        },
        //关联用户
        relevanceUser(e) {
            console.log('关联用户', e)
            if ([4].includes(this.type)) {
                if (e.hasBoundSpeaker) {
                    this.$message({
                        message: '该用户已关联,请重新选择',
                        type: 'warning'
                    });
                    return
                }
                this.$emit('relevance', e);
            }
            if ([1].includes(this.type)) {
                if (e.boundSpeakerUid) {
                    this.$message({
                        message: '该用户已关联,请重新选择',
                        type: 'warning'
                    });
                    return
                }
                this.$emit('relevance', e);
            }
            if (this.type == 2 || this.type == 3) {
                this.$emit('relevance', e)
            }
        },
    },
}
</script>

<style lang="scss" scoped></style>