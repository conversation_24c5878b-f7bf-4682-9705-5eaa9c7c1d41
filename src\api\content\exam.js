import request from '@/utils/request'
import axios from 'axios'

// 添加考试
export function addExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/add',
    method: 'post',
    data: params
  })
}

// 获取考试列表
export function examList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/getList',
    method: 'get',
    params
  })
}

// 删除考试
export function deleteExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/deleteBatchIds',
    method: 'post',
    data: params
  })
}

// 编辑考试
export function editExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/edit',
    method: 'post',
    data: params
  })
}

// 发布考试
export function releaseExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/releaseExam',
    method: 'get',
    params
  })
}

// 停止考试
export function stopExam(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/stopExam',
    method: 'get',
    params
  })
}

// 客观题分析
export function objectiveQuestionAnalysis(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/objectiveQuestionAnalysis',
    method: 'get',
    params
  })
}

// 成绩排名
export function scoreRanking(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/scoreRanking',
    method: 'get',
    params
  })
}

// 考试概况
export function examOverview(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/examOverview',
    method: 'get',
    params
  })
}

// 查看批阅详情
export function getReviewDetail(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/getReviewDetail',
    method: 'get',
    params
  })
}

// 查询批阅列表
export function getReviewList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/trainExam/getReviewList',
    method: 'get',
    params
  })
}

// 考试-统计分析-导出试卷
export function outputPaper(params = {}) {
  return axios({
    url: process.env.ADMIN_API + '/trainExam/outputPaper',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 考试-统计分析-导出成绩单
export function exportTranscript(params = {}) {
  return axios({
    url: process.env.ADMIN_API + '/trainExam/exportTranscript',
    method: 'post',
    responseType: 'blob',
    data: params
  })
}

// 考试-获取证书编号
export function getNoConfig(params = {}) {
  return request({
    url: process.env.ADMIN_API + `/trainCertificateUser/getNoConfig`,
    method: 'get',
    params
  })
}

// 考试-证书审核
export function certificateAudit(params = {}) {
  return request({
    url: process.env.ADMIN_API + `/trainCertificateUser/audit`,
    method: 'post',
    data: params
  })
}
