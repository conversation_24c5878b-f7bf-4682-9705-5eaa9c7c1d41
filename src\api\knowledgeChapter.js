import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeChapter/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeChapter/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeChapter/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeChapter/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeChapter/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/knowledgeChapter/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
