<template>
  <div class="dialog-confi">
    <el-dialog
      v-if="dialogFlag"
      :title="title"
      :visible.sync="dialogFlag"
      append-to-body
      :before-close="_beforeClose"
      center
      width="40%"
    >
      <!-- 内容区域 -->
      <div v-if="virtualList.length" class="dialog_content">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="云主机：">
            <svg-icon icon-class="computer" class-name="card-panel-icon" />
            {{ virtualList[0].facilityName }}
          </el-form-item>
          <el-form-item label="已挂载硬盘：">
            <!-- <span>暂无</span> -->
            <div v-for="(item, index) in form.volumeUsedList" :key="index" class="volume_used">
              <span>{{ item.name }}</span>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                circle
                @click="deleteUsedVolume(item, index)"
              />
            </div>
          </el-form-item>
          <el-form-item label="添加云硬盘：">
            <el-select
              v-model="form.volumeIdList"
              multiple
              filterable
              placeholder="检索云硬盘"
              class="_input"
            >
              <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="dialog-footer" style="text-align:center">
          <el-button @click="cancal">取 消</el-button>
          <el-button :disabled="disFlag" type="primary" style="margin-left:120px" @click="sure">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  cloudHostVolume,
  volumesMountable,
  volumesAttach,
  volumesDetach
} from '@/api/sourceLibrary/virtualApi'
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      dialogFlag: true,
      form: {
        volumeIdList: [],
        volumeUsedList: [],
        deleteUsedVolumeList: []
      },
      rules: {},
      options: [],
      disFlag: false
    }
  },
  created() {
    this.virtualList = this.list
    this.getOptionsList()
    this.getVolumesList()
  },
  methods: {
    // 删除已挂载的运营盘
    deleteUsedVolume(item, index) {
      this.form.deleteUsedVolumeList.push(item)
      this.form.volumeUsedList.splice(index, 1)
    },
    // 查询云主机已挂载的云硬盘
    getVolumesList() {
      cloudHostVolume(this.virtualList[0].id)
        .then((res) => {
          if (res.code == 200) {
            this.form.volumeUsedList = res.data.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 查询可挂载的云硬盘下拉框
    getOptionsList() {
      volumesMountable(this.virtualList[0].id)
        .then((res) => {
          if (res.code == 200) {
            this.options = res.data.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    _beforeClose() {
      this.$emit('cancelDialog', true)
    },
    cancal() {
      this.dialogFlag = false
      this.$emit('cancelDialog', true)
    },
    sure() {
      this.disFlag = true
      // 删除
      const list = []
      if (
        this.form.deleteUsedVolumeList &&
        this.form.deleteUsedVolumeList.length
      ) {
        this.form.deleteUsedVolumeList.forEach(item => {
          const idAdd = this.virtualList[0].id + '/' + item.volume_id
          const flag = volumesDetach(idAdd)
            .then((res) => {
              res.name = '删除云硬盘' + item.name
              this.$parent.msgList = res
              this.$emit('msgShow')
              return res
            })
            .catch((err) => {
              console.log(err)
            })
          list.push(flag)
        })
        // Promise.all(list).then(res => {
        //   this.$emit('updateList')
        //   this.cancal()
        // })
      }
      // 添加
      if (this.form.volumeIdList && this.form.volumeIdList.length) {
        this.form.volumeIdList.forEach(item => {
          const idAdd = this.virtualList[0].id + '/' + item
          const flag = volumesAttach(idAdd)
            .then((res) => {
              res.name = '添加云硬盘'
              this.$parent.msgList = res
              this.$emit('msgShow')
              return res
            })
            .catch((err) => {
              console.log(err)
            })
          list.push(flag)
        })
      }
      Promise.all(list).then(res => {
        this.disFlag = false
        this.$emit('updateList')
        this.cancal()
      })
    }
  }
}
</script>
<style lang="scss"  scoped>
.dialog-confi {
  ._input ::v-deep {
    width: 500px;
  }
  .volume_used {
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: space-between;
    align-items: flex-start;
  }
}
</style>
