export default {
    data() {
        return {
            subjectTypeArr: [
                { type: 0, name: '单选题',totalNum:0 },
                { type: 1, name: '多选题',totalNum:0 },
                { type: 2, name: '不定选项题',totalNum:0 },
                { type: 3, name: '判断题',totalNum:0 }
                // { type: 4, name: '填空题' },
                // { type: 5, name: '问答题' },
                // { type: 6, name: '材料题' }
            ],
            difficultys: [
                { index: -1, label: '全部' },
                { index: 1, label: '简单' },
                { index: 3, label: '困难' }
            ],
            stars: [
                { index: -1, label: '全部' },
                { index: 1, label: '一星' },
                { index: 2, label: '二星' },
                { index: 3, label: '三星' },
                { index: 4, label: '四星' },
                { index: 5, label: '五星' }
            ],
            questionTypes: [
                { index: -1, label: '全部' },
                { index: 0, label: '单选' },
                { index: 1, label: '多选题' },
                { index: 3, label: '判断题' },
                { index: 2, label: '不定项选择题' }
            ],
        };
    },
    methods: {
        // 返回题目类型
        handleType(type) {
            let name = ''
            this.subjectTypeArr.some(item => {
                if (type === item.type) {
                    name = item.name
                    return true
                }
            })
            return name
        },
        showDifficultysLabel(data) {
            if (!data.difficultLevel) return '--';
            let targetStarObj = this.difficultys.find(item => item.index == data.difficultLevel);
            return targetStarObj ? targetStarObj.label : '--';
        },
        showStarsLabel(data) {
            if (!data.starLevel) return '--';
            let targetStarObj = this.stars.find(item => item.index == data.starLevel);
            return targetStarObj ? targetStarObj.label : '--';
        },
    },
}