<template>
  <div class="app-container f fd-c">
    <!-- 查询和其他操作 -->
    <div class="filter-container" style="margin: 10px 0 10px 0;">
      <div class="left">
        <div style="margin-right:10px">
          <el-select v-model="searchForm.role" size="small" clearable placeholder="全部角色"
            style="width:140px  margin-right:10px" @change="adminList()" @clear="adminList()">
            <el-option v-for="item in roleOptions" :key="item.uid" :label="item.roleName" :value="item.uid" />
          </el-select>
        </div>
        <div style="margin-right:10px">
          <el-select v-model="searchForm.status" size="small" clearable placeholder="全部状态"
            style="width:140px  margin-right:10px" @change="adminList()" @clear="adminList()">
            <el-option label="已停用" value="0" />
            <el-option label="已启用" value="1" />
          </el-select>
        </div>
        <el-input v-model="keyword" size="mini" clearable class="filter-item" style="width: 200px; margin-right:10px"
          placeholder="请输入手机号" />
        <el-button v-permission="'/admin/getList'" size="mini" class="filter-item" type="primary" icon="el-icon-search"
          @click="handleFind">
          查找
        </el-button>
      </div>
      
      <el-button v-permission="'/admin/add'" v-if="uid === '7621746caa93ce605e2de7143a3787b5'" size="mini"
        class="filter-item" type="primary" icon="el-icon-plus" @click="handleAdd">
        添加员工
      </el-button>
    </div>
    <el-table :data="tableData" height="auto" class="f1" style="width: 100%">
      <el-table-column type="selection" />

      <el-table-column label="用户头像">
        <template slot-scope="scope">
          <div style="cursor: pointer;">
            <el-image v-if="scope.row.photoList" :src="scope.row.photoList[0]
              ? scope.row.photoList[0]
              : 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
              " :preview-src-list="[
    scope.row.photoList[0]
      ? scope.row.photoList[0]
      : 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
  ]" style="width: 60px; height: 60px" />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="员工账号">
        <template slot-scope="scope">
          <div style="display: flex;align-items: center;" @click.stop="checkPhone(scope.row, 0)">
            <span>{{ scope.row.userName }}</span>
            <i class="el-icon-view" v-permission="'/admin/showMobile'"
              style="margin-left: 15px;font-size: 16px;cursor:pointer;"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="姓名">
        <template slot-scope="scope">
          <span>{{ scope.row.nickName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="角色">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.role" type="danger">{{
            scope.row.role.roleName
          }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="添加人">
        <template slot-scope="scope">
          <div style="display: flex;align-items: center;" @click.stop="checkPhone(scope.row, 1)">
            <span>{{ scope.row.operaUser }}</span>
            <i class="el-icon-view" v-permission="'/admin/showMobile'"
              style="margin-left: 15px;font-size: 16px;cursor:pointer;"></i>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="添加时间" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>

      <el-table-column label="登录次数">
        <template slot-scope="scope">
          <span>{{ scope.row.loginCount }}</span>
        </template>
      </el-table-column>

      <!--      <el-table-column label="登录IP" >-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{ scope.row.lastLoginIp }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <!--      <el-table-column label="最后登录时间" >-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{ scope.row.lastLoginTime }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <el-table-column label="账号状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 1" type="success">已启用</el-tag>
          <el-tag v-if="scope.row.status == 0" type="info">已停用</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <div ref="operation" style="display:flex;justify-content:space-between;">
            <el-button v-if="scope.row.status === 1 &&
              uid === '7621746caa93ce605e2de7143a3787b5' && scope.row.uid != '7621746caa93ce605e2de7143a3787b5'
              " type="info" size="small" @click="handleAdminUserStatus(scope.row)">
              停用
            </el-button>
            <el-button v-if="scope.row.status === 0 &&
              uid === '7621746caa93ce605e2de7143a3787b5' && scope.row.uid != '7621746caa93ce605e2de7143a3787b5'
              " type="success" size="small" @click="handleAdminUserStatus(scope.row)">
              启用
            </el-button>
            <el-button v-permission="'/admin/edit'" v-if="(uid === '7621746caa93ce605e2de7143a3787b5' && scope.row.uid != '7621746caa93ce605e2de7143a3787b5')
              || (uid !== '7621746caa93ce605e2de7143a3787b5' && scope.row.uid === uid)" type="primary" size="small"
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button v-permission="'/admin/delete'"
              v-if="uid === '7621746caa93ce605e2de7143a3787b5' && scope.row.uid != '7621746caa93ce605e2de7143a3787b5'"
              type="danger" size="small" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </div>
          <!--          <el-button @click="handRest(scope.row)" type="warning" size="small" v-permission="'/admin/restPwd'">重置密码</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <!--分页-->
    <div class="block">
      <el-pagination :current-page.sync="currentPage" :page-size="pageSize" :total="total"
        style="float: right; margin-top:10px" layout="total, prev, pager, next, jumper"
        @current-change="handleCurrentChange" />
    </div>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item :label-width="formLabelWidth" label="员工账号" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入11位数手机号" type="number" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="title == '增加管理员'" :gutter="24">
          <el-col :span="24">
            <el-form-item :label-width="formLabelWidth" label="设置密码" prop="passWord">
              <el-input v-model="form.passWord" placeholder="请输入密码" show-password />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item :label-width="formLabelWidth" label="员工姓名" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入员工姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item :label-width="formLabelWidth" label="员工头像">
          <div v-if="form.photoList" class="imgBody">
            <i v-show="icon" class="el-icon-error inputClass" @click="deletePhoto()" @mouseover="icon = true" />
            <img :src="form.photoList[0]" @mouseover="icon = true" @mouseout="icon = false">
          </div>
          <div v-else class="uploadImgBody" @click="checkPhoto">
            <i class="el-icon-plus avatar-uploader-icon" />
          </div>
        </el-form-item>

        <el-row v-if="uid === '7621746caa93ce605e2de7143a3787b5' && form.uid !== '7621746caa93ce605e2de7143a3787b5'"
          :gutter="24">
          <el-col :span="24">
            <el-form-item :label-width="formLabelWidth" label="选择角色" prop="roleUid">
              <!-- <el-select v-model="form.roleUid" placeholder="选择角色">
                <el-option v-for="item in roleOptions" :key="item.uid" :label="item.roleName" :value="item.uid"/>
              </el-select> -->
              <el-select v-model="form.roleUid" multiple placeholder="选择角色">
                <el-option v-for="item in roleOptions" :key="item.uid" :label="item.roleName" :value="item.uid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <avatar-cropper v-show="imagecropperShow" :key="imagecropperKey" :width="300" :height="300" :url="url" lang-type="zh"
      @close="close" @crop-upload-success="cropSuccess" />
  </div>
</template>

<script>
import {
  addAdmin,
  deleteAdmin,
  editAdmin,
  getAdminList,
  restPwdAdmin,
  updateAdminUserStatus,
  chekStaffPhone
} from '@/api/admin'

import { getRoleList } from '@/api/role'
import { getListByDictType } from '@/api/sysDictData'
import AvatarCropper from '@/components/AvatarCropper'

export default {
  components: {
    AvatarCropper
  },
  data() {
    return {
      // 图片上传路径
      url: process.env.PICTURE_API + '/file/cropperPicture',
      tableData: [],
      roleOptions: [], // 角色候选框
      loading: false, // 搜索框加载状态
      roleData: [], // 角色列表
      roleValue: [], // 选择的角色列表
      keyword: '',
      searchForm: {
        role: null,
        status: null
      },
      currentPage: 1,
      pageSize: 10,
      total: 0, // 总数量
      title: '增加管理员',
      dialogFormVisible: false, // 控制弹出框
      formLabelWidth: '120px',
      isEditForm: false,
      form: {},
      imagecropperKey: 0,
      imagecropperShow: false, // 控制图片选择器的显示
      photoList: [],
      icon: false, // 控制删除图标的显示
      genderDictList: [], // 字典列表
      rules: {
        roleUid: [ { type: 'array', required: true, message: '请选择角色', trigger: ['blur', 'change'] },],
        userName: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          {
            required: true,
            pattern: /^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/,
            message: '请输入正确11位数手机号码'
          }
        ],
        nickName: [
          { required: true, message: '员工姓名不能为空', trigger: 'blur' },
          {
            required: true,
            pattern: /^[\u4E00-\u9FA5]+$/,
            message: '只允许输入中文'
          }
        ],
        passWord: [
          { required: true, message: '密码不能不能为空', trigger: 'blur' },
          {
            pattern: /^(?![A-Za-z0-9]+$)(?![a-z0-9#?!@$%^&*-.]+$)(?![A-Za-z#?!@$%^&*-.]+$)(?![A-Z0-9#?!@$%^&*-.]+$)[a-zA-Z0-9#?!@$%^&*-.]{9,16}$/,
            message: '请您设置9-16位数的密码，包含数字、大小写字母、特殊字符'
          }
        ],
        dictValue: [
          { required: true, message: '字典键值不能为空', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在1到20个字符' }
        ],
        gender: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
        email: [
          {
            pattern: /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,
            message: '请输入正确的邮箱'
          }
        ],
        mobile: [
          {
            required: false,
            pattern: /^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$/,
            message: '请输入正确的手机号码'
          }
        ],
        qqNumber: [
          { pattern: /[1-9]([0-9]{5,11})/, message: '请输入正确的QQ号码' }
        ]
      },
      uid: ''
    }
  },
  created() {
    this.uid = window.localStorage.getItem('huanyu-admin-i')
    console.log('this.uid', this.uid)
    this.getDictList()
    this.adminList()
    this.roleList()
  },
  methods: {
    //查看手机号
    checkPhone(e, type) {
      
      if (!e.encryUuserName) {// 当前加密字段
        e.encryUuserName = e.userName
        e.encryOperaUser = e.operaUser
      }

      chekStaffPhone({ uid: e.uid, type }).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          if (type == 0) {
            e.userName = e.userName == res.data ? e.encryUuserName : res.data
          }
          if (type == 1) {
            e.operaUser = e.operaUser == res.data ?  e.encryOperaUser : res.data
          }
        }
      }).catch(rej => {
        this.$message.error(rej.message)
      })
    },
    adminList: function () {
      var params = {}
      params.keyword = this.keyword
      params.roleUid = this.searchForm.role
      params.status = this.searchForm.status
      params.currentPage = this.currentPage
      params.pageSize = this.pageSize
      getAdminList(params).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          const tableData = response.data.records
          for (let a = 0; a < tableData.length; a++) {
            tableData[a].maxStorageSize =
              tableData[a].maxStorageSize / 1024 / 1024
          }
          this.tableData = tableData
          // 设置操作栏长度
          this.$nextTick(() =>
            this.$store.dispatch(
              'setWidth',
              this.$refs.operation.children.length
            )
          )
          this.currentPage = response.data.current
          this.pageSize = response.data.size
          this.total = response.data.total
        }
      })
    },
    /**
     * 字典查询
     */
    getDictList: function () {
      var params = {}
      params.dictType = 'sys_user_sex'
      getListByDictType(params).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.genderDictList = response.data.list
          // 设置默认值
          if (response.data.defaultValue) {
            this.genderDefaultValue = response.data.defaultValue
          }
        }
      })
    },
    // 角色远程搜索函数
    roleList: function () {
      var params = {}
      params.currentPage = 1
      params.pageSize = 9999
      getRoleList(params).then(response => {
        this.roleOptions = response.data.records
      })
    },
    cropSuccess(resData) {
      this.imagecropperShow = false
      this.imagecropperKey = this.imagecropperKey + 1
      const photoList = []
      photoList.push(resData[0].url)
      this.form.photoList = photoList
      this.form.avatar = resData[0].uid
    },
    close() {
      this.imagecropperShow = false
    },
    deletePhoto: function () {
      this.form.photoList = null
      this.form.ava = ''
      this.icon = false
    },
    checkPhoto() {
      this.photoList = []
      this.avatar = ''
      this.imagecropperShow = true
    },
    getFormObject: function () {
      var formObject = {
        uid: null,
        gender: this.genderDefaultValue
      }
      return formObject
    },
    handleFind: function () {
      this.adminList()
    },
    handleAdd: function () {
      this.title = '增加管理员'
      this.dialogFormVisible = true
      this.form = this.getFormObject()
      this.isEditForm = false
    },
    async handleEdit(row) {
      let { data: phone } = await chekStaffPhone({ uid: row.uid, type: 0 })
      // console.log('phone', phone)
      row.userName = phone
      this.title = '编辑管理员'
      this.dialogFormVisible = true
      this.isEditForm = true
      this.form = Object.assign({}, {
        ...row,
        roleUid: row.roleUid.split(',')
      })
      console.log("this.form", this.form);
      this.form.passWord = ''
      this.roleValue = []
      var roleList = []
      // 设置选择的角色列表
      if (row.roleList) {
        row.roleList.forEach(element => {
          roleList.push(element.uid)
        })
        this.roleValue = roleList
      }
    },
    handRest: function (row) {
      var that = this
      this.$confirm('此操作将会将该用户密码重置为默认密码, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const params = {}
          params.uid = row.uid
          restPwdAdmin(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消重置')
        })
    },
    //  计算文件大小
    calculateFileSize(size) {
      const B = 1024
      const KB = Math.pow(1024, 2)
      const MB = Math.pow(1024, 3)
      const GB = Math.pow(1024, 4)
      if (!size) {
        return '_'
      } else if (size < KB) {
        return (size / B).toFixed(0) + 'KB'
      } else if (size < MB) {
        return (size / KB).toFixed(1) + 'MB'
      } else if (size < GB) {
        return (size / MB).toFixed(2) + 'GB'
      } else {
        return (size / GB).toFixed(3) + 'TB'
      }
    },
    handleAdminUserStatus: function (row) {
      var title = '停用'
      if (row.status == 1) {
        title = '启用'
      }
      this.$confirm('此操作将' + title + '该员工账号, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const params = new URLSearchParams()
          var adminUids = []
          adminUids.push(row.uid)
          params.append('adminUids', adminUids)
          updateAdminUserStatus(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
            this.adminList()
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消操作')
        })
    },
    handleDelete: function (row) {
      this.$confirm('此操作将删除该员工并不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const params = new URLSearchParams()
          var adminUids = []
          adminUids.push(row.uid)
          params.append('adminUids', adminUids)
          deleteAdmin(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
            this.adminList()
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消删除')
        })
    },
    handleCurrentChange: function (val) {
      this.currentPage = val
      this.adminList()
    },
    submitForm: function () {
      this.$refs.form.validate(valid => {
        if (!valid) {
          console.log('校验出错')
        } else {
          // 切割role uid数组
          const params = JSON.parse(JSON.stringify(this.form))
          params.roleUid = params.roleUid && params.roleUid.length > 0 ? (params.roleUid.join(",")) : ''
          if (this.isEditForm) {
            editAdmin(params).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message)
                this.dialogFormVisible = false
                this.adminList()
              } else {
                // this.$commonUtil.message.error(response.message)
              }
            })
          } else {
            addAdmin(params).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message)
                this.dialogFormVisible = false
                this.adminList()
              } else {
                // this.$commonUtil.message.error(response.message)
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 130px);
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin: 0, 0, 0, 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.imgBody {
  width: 100px;
  height: 100px;
  border: solid 2px #ffffff;
  float: left;
  position: relative;
}

.uploadImgBody {
  margin-left: 5px;
  width: 100px;
  height: 100px;
  border: dashed 1px #c0c0c0;
  float: left;
  position: relative;
}

.uploadImgBody :hover {
  border: dashed 1px #00ccff;
}

.inputClass {
  position: absolute;
}

img {
  width: 100px;
  height: 100px;
}

.filter-container {
  display: flex;
  justify-content: space-between;

  .left {
    display: flex;
    align-items: center;
  }
}
</style>
