<template>
    <div class="training-camp-component">
        <div class="training-camp-content">
            <!-- 训练营搜索头部 -->
            <div class="head">
                <el-button @click="addtrainingCampModel" type="primary">新建训练营</el-button>
                <div class="search">
                    <el-input placeholder="请输入训练营名称" v-model="trainingCampModel.trainingCampName">
                        <template slot="prepend">
                            <i class="el-icon-search"></i>
                        </template>
                    </el-input>
                    <el-button class="search-btn" @click="trainingCampSearch">搜索</el-button>
                    <el-button v-if="uids.length" style="float:right;" type="danger" @click="batchDelete">删除</el-button>
                </div>
            </div>
            <p>训练营列表（{{trainingCampModel.total}}条）</p>
            <!-- 训练营表格 -->
            <div class="training-camp-table-box">
                <el-table  @cell-mouse-enter="trainingCampCellMouseEnter" class="training-camp-table"
                    @selection-change="handleSelectionChange"
                    :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
                    :cell-style="{ textAlign: 'center' }"
                    :data="trainingCampModel.trainingCampData" style="width: 100%">
                     <el-table-column
                    type="selection"
                    width="55">
                    </el-table-column>
                    <el-table-column type="index" align="center" label="序号" width="60" />
                    <el-table-column prop="campName" label="训练营名称" width="180">
                        <template slot-scope="scope">
                            <span v-show="!scope.row.editorFlag">{{scope.row.campName}}</span>
                            <i @click="editorName(scope.row)" v-show="!scope.row.editorFlag" class="el-icon-edit"></i>
                            <el-input :ref="scope.row.uid" @keyup.enter.native="trainingCampBlur(scope.row)"
                                @blur="trainingCampBlur(scope.row)" v-if="scope.row.editorFlag"
                                v-model="scope.row.campName"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="campPeriodNumber" label="营期数" width="180">
                    </el-table-column>
                    <el-table-column prop="enrollment" label="报名人数">
                    </el-table-column>
                    <el-table-column prop="address" width="200" label="操作">
                        <template slot-scope="scope">
                            <ul class="operation">
                                <li @click="opeRationPeriodManagement(scope.row)">营期管理</li>
                                <!-- <li @click="detailItem(scope.row)">详情</li> -->
                                <!-- <el-popconfirm
                                @confirm="deleteItem([scope.row.uid])"
                                title="确定要删除该训练营吗？"
                                >
                                    <li slot="reference">删除</li>
                                </el-popconfirm> -->
                            </ul>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <!-- 分页 -->
        <template>
            <div class="el-pagination">
                <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="trainingCampModel.page"
                :page-sizes="[5, 10, 20, 50]"
                :page-size="trainingCampModel.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="trainingCampModel.total">
                </el-pagination>
            </div>
        </template>
        <!-- 新建训练营弹窗 -->
        <el-dialog @closed="trainingCampDialogClose" title="添加训练营" :visible.sync="trainingCampModel.trainingCampDialogVisible" width="30%" center>
            <el-form :rules="trainingCampModel.addtrainingFormRule" label-width="100px" ref="addtrainingForm" :model="trainingCampModel.addtrainingForm">
                <el-form-item label="训练营名称" prop="name">
                    <el-input v-model="trainingCampModel.addtrainingForm.name"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="trainingCampModel.trainingCampDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="realAddTrainingCamp">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {delCamp,addCamp,editCamp,getCampList} from '@/api/content/trainingCamp'
import {mapMutations} from 'vuex'
export default {
    inject:['parentThis'],
    data() {
        return {
            isAddCampPeriod:true,
            trainingCampModel: {
                pageSize:5,
                page:1,
                total:0,
                addtrainingForm: {
                    name: ""
                },
                addtrainingFormRule:{
                    name:[{required:true,message:'请输入训练营名称',trigger:'blur'}]
                },
                trainingCampData: [],
                trainingCampDialogVisible: false,
                name: "训练营名称",
                currentRowId: "",
                tabType: "trainingCamp",
                trainingCampName: ""
            },
            uids:[]
        }
    },
    computed: {
    },
    mounted() {
        this.initTrainingCampData();
    },
    methods: {
        handleSelectionChange(datas) {
            this.uids=datas.map(item=>item.uid);
        },
        batchDelete() { 
            if (this.uids.length == 0) return this.$message.warning('请选择要删除的训练营');
            this.$confirm('此操作将永久删除该训练营, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
                }).then(async () => {
                    await this.deleteItem(this.uids);
                }).catch(() => {
                           
                });
        },
        ...mapMutations({
            'setTrainingCampInfo':'trainingCamp/setTrainingCampInfo'
        }),
        handleSizeChange(val){
            this.trainingCampModel.pageSize=val;
            this.initTrainingCampData();
        },
        handleCurrentChange(val){
            this.trainingCampModel.page=val;
            this.initTrainingCampData();
        },
        // 营期管理
        opeRationPeriodManagement(data){
            // 保存当前训练营信息到vuex
            this.setTrainingCampInfo(data);
            // 切换到营期管理界面
            this.parentThis.flag=1;
        },
        // 弹窗关闭触发
        trainingCampDialogClose(){
            this.dataReSet();
            // 重置表单校验
            this.$refs['addtrainingForm'].clearValidate();
        },
        // 数据重置
        dataReSet(){
            this.trainingCampModel.addtrainingForm={
                name: ""
            };
        },
        // 确认添加
        realAddTrainingCamp(){
            this.$refs['addtrainingForm'].validate(async (valid) => {
            if (valid) {
                let params={
                    campName:this.trainingCampModel.addtrainingForm.name
                }
                let result=await addCamp(params);
                if(result.code==200){
                    this.$message.success("新建训练营成功");
                    // 刷新数据
                    this.initTrainingCampData();
                    this.trainingCampModel.trainingCampDialogVisible=false;
                }
            } else {
                console.log('error submit!!');
                return false;
            }
            });
        },
        // 添加训练营
        addtrainingCampModel() {
            this.trainingCampModel.trainingCampDialogVisible = true;
        },
        // 编辑训练营
        async editCamp(data){
            let params={
                uid:data.uid,
                campName:data.campName
            }
            let result=await editCamp(params);
            return result;
        },
        // 失去焦点
        async trainingCampBlur(data) {
            let result=await this.editCamp(data);
            data.editorFlag = false;
        },
        // 关闭所有名字编辑框
        closeAllEdirot() {
            // 先关闭所有的编辑框
            this.trainingCampModel.trainingCampData.forEach(item => {
                item.editorFlag = false;
            })
        },
        // 编辑名称
        async editorName(data) {
            this.closeAllEdirot();
            // 打开对应的编辑框
            data.editorFlag = true;
            // 自动focus
            await this.$nextTick();
            this.$refs[data.uid].focus();
        },
        async initTrainingCampData() {
            let params={
                currentPage:this.trainingCampModel.page,
                pageSize:this.trainingCampModel.pageSize,
                campName:this.trainingCampModel.trainingCampName
            }
            let result=await getCampList(params);
            console.warn(result);
            if(result.code==200){
                this.trainingCampModel.total=result.data.total;
                this.trainingCampModel.trainingCampData = result.data.records.map(item => {
                    return {
                        ...item,
                        editorFlag: false
                    }
                });
            }
        },
        trainingCampCellMouseEnter(row, column, cell, event) {
            console.log(row, column, cell)
        },
        // 详情
        detailItem(data) {

        },
        // 删除
        async deleteItem(uids) {
            let result=await delCamp(uids);
            if(result.code==200){
                this.$message.success('删除成功!');
                this.initTrainingCampData();
            }
        },
        // 训练营搜索
        trainingCampSearch() {
            this.trainingCampModel.page=1;
            this.initTrainingCampData();
        },
        // tab切换
        handleClick(data) {
            console.log(data)
        }
    }
}
</script>
<style lang="scss" scoped>
.training-camp-component {
    padding-bottom: 20px;
    .el-pagination{
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    .training-camp-content {
        padding: 0 20px;

        .head {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .search {
                display: flex;
                align-items: center;

                .search-btn {
                    margin-left: 10px;
                }
            }
        }

        .training-camp-table-box {
            .training-camp-table {
                .operation {
                    padding: 0;
                    margin: 0 auto;
                    list-style: none;
                    display: flex;
                    width: max-content;

                    li {
                        color: #2a75ed;
                        cursor: pointer;
                        float: left;
                        padding: 0 15px;
                        display: flex;
                        align-items: center;
                        position: relative;
                        justify-content: center;

                        &::after {
                            content: "";
                            height: 14px;
                            border: 1px solid #ebe8e8;
                            right: 0;
                            position: absolute;
                        }
                        &:last-child::after {
                            border: none;
                        }
                    }
                }
            }
        }
    }
}
</style>
