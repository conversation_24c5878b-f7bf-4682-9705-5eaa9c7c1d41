<template>
  <div class="content-manage-child">
    <div class="top">
      <div class="left">
        <el-form
          :inline="true"
          :model="searchParams"
          class="search-form"
          label-position="right"
          label-width="80px"
        >
          <el-form-item label="案例状态">
            <el-select
              v-model="searchParams.status"
              size="small"
              placeholder="请选择案例状态"
              clearable
              @change="getDataList()"
              @clear="getDataList()"
            >
              <el-option :value="1" label="已发布"/>
              <el-option :value="0" label="已下架"/>
            </el-select>
          </el-form-item>
          <el-form-item label="时间筛选">
            <el-date-picker
              v-model="searchParams.date"
              size="small"
              type="daterange"
              range-separator="——"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
              @change="getDataList()"
              @clear="getDataList()"
            />
            <!-- <el-date-picker
              size="small"
              v-model="searchParams.date"
              type="date"
              placeholder="选择日期"
              clearable
              @change="getDataList()"
              @clear="getDataList()"
            >
            </el-date-picker> -->
          </el-form-item>
          <el-form-item label="企业名称">
            <el-input
              v-model="searchParams.companyName"
              size="small"
              placeholder="请输入企业名称"
              clearable
              @change="getDataList()"
              @clear="getDataList()"
            />
          </el-form-item>
          <el-form-item label="">
            <el-button
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="getDataList()"
            >
              搜索
            </el-button>
            <el-button
              :disabled="ids.length == 0"
              size="mini"
              type="danger"
              @click="deleteCase(null)"
            >删除</el-button
            >
            <el-button size="mini" type="primary" @click="openEditCase(null)">
              添加案例
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <!-- <el-button
          @click="deleteCase(null)"
          size="mini"
          type="danger"
          :disabled="ids.length == 0"
          >删除</el-button
        >
        <el-button size="mini" @click="openEditCase(null)" type="primary">
          添加案例
        </el-button> -->
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="dataList"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55"/>
      <el-table-column align="center" prop="companyName" label="企业名称">
        <template slot-scope="scope">
          <div :title="scope.row.companyName" class="ellipsis" >
            {{ scope.row.companyName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="introduction" label="企业简介">
        <template slot-scope="scope">
          <div :title="scope.row.introduction" class="ellipsis" >
            {{ scope.row.introduction }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="jumpLink" label="跳转链接">
        <template slot-scope="scope">
          <el-link :href="scope.row.jumpLink" target="_blank">
            {{ scope.row.jumpLink ? scope.row.jumpLink : "" }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="imgPath" width="120px" label="案例配图">
        <template slot-scope="scope">
          <el-image
            :src="scope.row.imgPath"
            style="width: 100px"
            fit="contain"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" prop="createTime" width="160px" label="添加时间"/>
      <el-table-column align="center" prop="status" width="90px" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 0" style="color:#FF3F3F">
            已下架
          </span>
          <span v-else-if="scope.row.status == 1" style="color:#15B564">
            已发布
          </span>
          <span v-else style="color:#FF3F3F"> 异常 </span>
      </template></el-table-column
      >
      <el-table-column :width="240 + 'px'" label="操作" fixed="right">
        <template slot-scope="scope">
          <div ref="operation">
            <el-button type="warning" size="mini" @click="setStatus(scope.row)">
              {{ scope.row.status == 1 ? "下架" : "上架" }}
            </el-button>
            <el-button type="primary" size="mini" @click="openEditCase(scope)">
              编辑
            </el-button>
            <!-- <el-button type="danger" size="mini" @click="deleteCase(scope.row)">
              删除
            </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部分页栏 -->
    <el-pagination
      :current-page="searchParams.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchParams.limit"
      :total="total"
      style="float: right; margin-top: 10px"
      class="bottom"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog
      :title="isEdit ? '编辑案例信息' : '添加案例信息'"
      :visible.sync="editDialog"
      width="700px"
    >
      <el-form
        ref="editForm"
        :rules="addFormRules"
        :model="editForm"
        label-width="80px"
      >
        <el-form-item label="企业名称" prop="companyName">
          <el-input
            v-model="editForm.companyName"
            style="width: 100%;"
            maxlength="30"
            class="company-name"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="企业简介" prop="introduction">
          <el-input
            :rows="3"
            v-model="editForm.introduction"
            type="textarea"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="跳转链接" prop="jumpLink">
          <el-input v-model="editForm.jumpLink"/>
        </el-form-item>
        <el-form-item label="案例配图" prop="imgUid">
          <div class="icon-main">
            <div class="icon-left">
              <div v-if="showIcon" class="upload_img">
                <img
                  :src="editForm.imgPath"
                  style="width: 100px; height: 100px"
                  @mouseover="icon = true"
                  @mouseout="icon = false"
                >
                <i
                  v-show="icon"
                  class="el-icon-error close-icon"
                  @mouseover="icon = true"
                  @click="showIcon = false"
                />
              </div>
              <div v-else class="uploadImgBody" @click="iconDialog = true">
                <i class="el-icon-plus avatar-uploader-icon" />
              </div>
            </div>
            <div class="icon-right">
              <!-- 建议尺寸60px*60px，JPG、PNG、JEPG格式，图片小于2M -->
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="editCase">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="iconDialog" width="400px">
      <el-upload
        ref="upload"
        :action="uploadPictureHost"
        :data="otherData"
        :on-error="_uploadImgError"
        :headers="{
          Authorization:$GetToken()
        }"
        :before-upload="_beforeUpload"
        :on-success="_uploadImgSuccess"
        :show-file-list="false"
        name="file"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  add,
  edit,
  deleteData,
  deleteBatch,
  setStatus
} from '@/api/customerCase'
import { getToken } from '@/utils/auth'
export default {
  data() {
    var validateImgPath = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请上传案例配图'))
      } else {
        callback()
      }
    }
    return {
      icon: false,
      showIcon: false,
      isEdit: false,
      iconDialog: false,
      searchParams: {
        date: null,
        companyName: null,
        status: null,
        limit: 10,
        page: 1
      },
      total: 10,
      loading: false,
      dataList: [],
      ids: [],
      uploadPictureHost: null,
      editDialog: false,
      editForm: {
        companyName: '',
        introduction: '',
        imgUid: '',
        jumpLink: '',
        uid: null,
        sort: null
      },
      addFormRules: {
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
          { max: 30, message: '标题最多十个30个字符', trigger: 'blur' }
        ],
        introduction: [
          { required: true, message: '请输入企业简介', trigger: 'blur' },
          { max: 100, message: '描述最多100 个字符', trigger: 'blur' }
        ],
        imgUid: [
          { required: true, validator: validateImgPath, trigger: 'change' },
          { required: true, message: '请上传案例配图', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 图片上传地址
    this.uploadPictureHost = process.env.PICTURE_API + '/file/cropperPicture'
    this.otherData = {
      source: 'picture',
      userUid: 'uid00000000000000000000000000000000',
      adminUid: 'uid00000000000000000000000000000000',
      projectName: 'blog',
      sortName: 'admin',
      token: getToken()
    }
    this.getDataList()
  },
  mounted() {
    // 设置操作栏长度
    this.$nextTick(() => {
      // this.$store.dispatch("setWidth", this.$refs.operation.children.length);
    })
  },
  methods: {
    // 获取Case列表
    async getDataList() {
      const endTime = this.searchParams.date
        ? new Date(JSON.parse(JSON.stringify(this.searchParams.date[1])))
        : null
      var params = {
        currentPage: this.searchParams.page,
        pageSize: this.searchParams.limit,
        companyName: this.searchParams.companyName,
        status: this.searchParams.status
      }

      params.createTime = this.searchParams.date
        ? this.dateToStr(this.searchParams.date[0])
        : null

      params.endTime = this.searchParams.date
        ? this.dateToStr(new Date(endTime.setDate(endTime.getDate() + 1)))
        : null

      getList(params).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.total = res.data.total
          this.dataList = res.data.records
        } else {
          this.$commonUtil.message.error(res.message)
        }
      })
    },

    // 日期转字符串格式
    dateToStr(date) {
      var year = date.getFullYear() // 年
      var month = date.getMonth() // 月
      var day = date.getDate() // 日
      var hours = date.getHours() // 时
      var min = date.getMinutes() // 分
      var second = date.getSeconds() // 秒
      return (
        year +
        '-' +
        (month + 1 > 9 ? month + 1 : '0' + (month + 1)) +
        '-' +
        (day > 9 ? day : '0' + day) +
        ' ' +
        (hours > 9 ? hours : '0' + hours) +
        ':' +
        (min > 9 ? min : '0' + min) +
        ':' +
        (second > 9 ? second : '0' + second)
      )
    },

    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.searchParams.limit = newSize
      this.getDataList()
    },

    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.searchParams.page = newSize
      this.getDataList()
    },

    // 编辑
    async openEditCase(scope) {
      if (scope == null) {
        this.showIcon = false
        this.editForm.companyName = ''
        this.editForm.introduction = ''
        this.editForm.imgUid = ''
        this.editForm.jumpLink = ''
        this.editForm.uid = null
        this.editForm.sort = null
        this.isEdit = false
      } else {
        this.isEdit = true
        this.editForm = Object.assign({}, scope.row)
        this.showIcon =
          scope.row.imgPath != undefined && scope.row.imgPath != null
      }
      this.editDialog = true
    },

    async editCase() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        var params = {
          uid: this.editForm.uid,
          companyName: this.editForm.companyName,
          introduction: this.editForm.introduction,
          imgUid: this.editForm.imgUid,
          jumpLink: this.editForm.jumpLink,
          sort: this.editForm.sort
        }
        if (this.isEdit) {
          edit(params).then(res => {
            if (res.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(res.message)
              this.getDataList()
              this.editDialog = false
            } else {
              this.$commonUtil.message.error(res.message)
            }
          })
        } else {
          add(params).then(res => {
            if (res.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(res.message)
              this.getDataList()
              this.editDialog = false
            } else {
              this.$commonUtil.message.error(res.message)
            }
          })
        }
      })
    },
    // 删除
    async deleteCase(row) {
      var configResult = await this.$confirm(
        '此操作将永久删除该Case图片, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(err => {
        return err
      })
      if (configResult !== 'confirm') {
        return this.$message.info({ message: '已经取消删除', duration: 1000 })
      }
      // row == null ? this.ids : [row]
      if (row == null) {
        deleteBatch(this.ids).then(res => {
          if (res.code == this.$ECode.SUCCESS) {
            this.$commonUtil.message.success(res.data)
            this.getDataList()
          } else {
            this.$commonUtil.message.error(res.data)
          }
        })
      } else {
        deleteData(row).then(res => {
          if (res.code == this.$ECode.SUCCESS) {
            this.$commonUtil.message.success(res.data)
            this.getDataList()
          } else {
            this.$commonUtil.message.error(res.data)
          }
        })
      }
    },

    // 表格前勾选框
    handleSelectionChange(val) {
      this.ids = val
    },

    // 启用停用
    setStatus(row) {
      row.status = row.status == 1 ? 0 : 1
      setStatus(row).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.$commonUtil.message.success(
            row.status == 1 ? '上架成功' : '下架成功'
          )
          this.getDataList()
          this.editDialog = false
        } else {
          this.$commonUtil.message.error(res.message)
        }
      })
    },

    // 上传封面图片
    _uploadImgError(err, file, fileList) {
      this.$message.error('文件上传失败，请重试！')
    },
    _uploadImgSuccess(response, file) {
      if (response.code == this.$ECode.SUCCESS) {
        this.editForm.imgUid = response.data[0].uid
        this.editForm.imgPath = response.data[0].url
        this.showIcon = true
        this.iconDialog = false
      } else {
        this.$alert(response.data, '提示', {
          confirmButtonText: '确定'
        })
      }
    },
    _beforeUpload(file) {
      const types = ['image/jpg', 'image/png', 'image/jpeg']
      const isJPG = types.includes(file.type)
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg或png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    }
  }
}
</script>

<style lang="scss" scoped>
.content-manage-child {
  padding: 20px;
  .top {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .right {
      display: flex;
      justify-content: space-between;
    }
  }
  /deep/ .el-dialog {
    .el-dialog__header {
    }
    .el-dialog__body {
      .el-form {
        .el-form-item {
          .el-form-item__content {
            .company-name {
              width: 280px;
              input {
              }
            }
            .el-textarea {
              //   width: 500px;
            }
          }
        }
      }
    }
  }
}
.addImg {
  .avatar-uploader-icon {
    border: 1px solid balck;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
}

.upload_img {
  width: 100px;
  height: 100px;
  position: relative;

  .close-icon {
    // display: none;
    right: 2px;
    top: 2px;
    position: absolute;
    font-size: 24px;
    display: inline-block;
    z-index: 8;
    cursor: pointer;
  }
  // img:hover + .close-icon {
  //   font-size: 24px;
  //   display: inline-block;
  //   z-index: 8;
  // }
}
.uploadImgBody {
  margin-left: 5px;
  width: 100px;
  height: 100px;
  border: dashed 1px #c0c0c0;
  float: left;
  position: relative;
}
.uploadImgBody :hover {
  border: dashed 1px #00ccff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.icon-main {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .icon-right {
    margin-left: 20px;
    color: #bbbbbb;
  }
}

/deep/ .search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
