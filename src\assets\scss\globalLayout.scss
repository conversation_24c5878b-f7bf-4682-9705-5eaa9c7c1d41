// flex相关
.flex {
  display: flex;
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-align {
  display: flex;
  align-items: center;
}
.flex-align-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around {
  display: flex;
  justify-content: space-around;
}
.flex-align-around {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-inline-start {
  display: flex;
  justify-content: flex-start;
}
.flex-inline-end {
  display: flex;
  justify-content: flex-end;
}
.flex-inline-start-align {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-inline-end-align {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-inline-start {
  display: flex;
  align-items: flex-start;
}
.flex-inline-end {
  display: flex;
  align-items: flex-end;
}
.flex-between-column {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.flex-between-column-align {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
}
.flex-around-column {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
}
.flex-around-column-align {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  align-items: center;
}

// margin 相关
.margin {
  margin: 10px;
}
.x-margin {
  margin: 20px;
}
.xx-margin {
  margin: 30px;
}
.s-margin {
  margin: 5px;
}
.margin-left {
  margin-left: 10px;
}
.x-margin-left {
  margin-left: 20px;
}
.xx-margin-left {
  margin-left: 30px;
}
.s-margin-left {
  margin-left: 5px;
}
.margin-right {
  margin-right: 10px;
}
.x-margin-right {
  margin-right: 20px;
}
.xx-margin-right {
  margin-right: 30px;
}
.s-margin-right {
  margin: 5px;
}
.margin-top {
  margin-top: 10px;
}
.x-margin-top {
  margin-top: 20px;
}
.xx-margin-top {
  margin-top: 30px;
}
.s-margin-top {
  margin-top: 5px;
}
.margin-bottom {
  margin-bottom: 10px;
}
.x-margin-bottom {
  margin-bottom: 20px;
}
.xx-margin-bottom {
  margin-bottom: 30px;
}
.s-margin-bottom {
  margin-bottom: 5px;
}
.margin-inline {
  margin-inline: 10px;
}
.x-margin-inline {
  margin-inline: 20px;
}
.xx-margin-inline {
  margin-inline: 30px;
}
.s-margin-inline {
  margin-inline: 5px;
}
.margin-block {
  margin-block: 10px;
}
.x-margin-block {
  margin-block: 20px;
}
.xx-margin-block {
  margin-block: 30px;
}
.s-margin-block {
  margin-block: 5px;
}

// padding 相关
.padding {
  padding: 10px;
}
.x-padding {
  padding: 20px;
}
.xx-padding {
  padding: 30px;
}
.s-padding {
  padding: 5px;
}
.padding-left {
  padding-left: 10px;
}
.x-padding-left {
  padding-left: 20px;
}
.xx-padding-left {
  padding-left: 30px;
}
.s-padding-left {
  padding-left: 5px;
}
.padding-right {
  padding-right: 10px;
}
.x-padding-right {
  padding-right: 20px;
}
.xx-padding-right {
  padding-right: 30px;
}
.s-padding-right {
  padding: 5px;
}
.padding-top {
  padding-top: 10px;
}
.x-padding-top {
  padding-top: 20px;
}
.xx-padding-top {
  padding-top: 30px;
}
.s-padding-top {
  padding-top: 5px;
}
.padding-bottom {
  padding-bottom: 10px;
}
.x-padding-bottom {
  padding-bottom: 20px;
}
.xx-padding-bottom {
  padding-bottom: 30px;
}
.s-padding-bottom {
  padding-bottom: 5px;
}
.padding-inline {
  padding-inline: 10px;
}
.x-padding-inline {
  padding-inline: 20px;
}
.xx-padding-inline {
  padding-inline: 30px;
}
.s-padding-inline {
  padding-inline: 5px;
}
.padding-block {
  padding-block: 10px;
}
.x-padding-block {
  padding-block: 20px;
}
.xx-padding-block {
  padding-block: 30px;
}
.s-padding-block {
  padding-block: 5px;
}

.dialog-button {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
/deep/.el-button-width {
  padding: 6px;
  width: 100px;
  height: 30px;
}



//wangbinlin

 //弹性布局
//双轴
.f{display: flex; display:-webkit-flex;}
.fd-r{flex-direction: row;}
.fd-rr{flex-direction: row-reverse;}
.fd-c{flex-direction: column;}
.fd-cr{flex-direction: column-reverse;}
//轴换行
.fw-w{flex-wrap:wrap;}
.fw-wr{flex-wrap:wrap-reverse;}
//主轴对齐方式
.jc-s{justify-content:flex-start}
.jc-e{justify-content:flex-end}
.jc-c{justify-content:center}
.jc-sa{justify-content:space-around}
.jc-sb{justify-content:space-between}
//交叉轴对齐方式
.ai-s{align-items: flex-start;}
.ai-e{align-items: flex-end;}
.ai-c{align-items:center;}
//多主轴对齐方式
.ac-s{align-content: flex-start;}
.ac-e{align-content: flex-end;}
.ac-c{align-content: center;}
.ac-sa{align-content: space-around;}
.ac-sb{align-content: space-between;}
//子项目尺寸自适应
.f1{flex:1}

//网格布局
.g{display:grid; display:-moz-grid;} 

//圆角
.rh{border-radius: 50%;}

//光标
.cursor-p{cursor: pointer;}
.cursor-nd{cursor: no-drop}

//文字颜色
.f-c-1{color: #333;}
.f-c-2{color: #666;}
.f-c-3{color: #999;}
.f-c-4{color: #ccc;}
.f-c-n{color: #333;}
.f-c-p{color: #448ace;}
.f-c-s{color: rgb(0, 195, 0);}
.f-c-w{color: orange}
.f-c-e{color: red}

//文字大小
@for $var from 12 to 48 {
  .f-s-#{$var}{
    font-size: #{$var}px;
  }
}
//文字粗细
.f-w-1{font-weight: 600;}
.f-w-2{font-weight: 900;}
.f-w-bold{font-weight: bold;}
.f-w-bolder{font-weight: bolder;}

//hover 悬停的文字颜色
.hc-s:hover {
  //蓝色
  color: #006eff
}

.hc-e:hover {
  //橙色
  color: orange;

}

.hc-w:hover {
  //红色
  color: #DE2525
}

//hover 悬停的边框颜色

.hbc-s:hover {
  //蓝色
  border: 1px solid #006eff;

}

.hbc-e:hover {
  //橙色
  border: 1px solid orange;

}

.hbc-w:hover {
  //红色
  border: 1px solid #DE2525;

} 

//背景色
.b-c-n{
  background-color: #ccc;
}

//网格布局
@for $v from 5 through 30{
	.gg#{$v}{
		grid-gap:#{$v}px;
	}
}

@mixin gtc($v){
	grid-template-columns: $v;
}
@mixin gtr($v){
	grid-template-rows: $v;
}

$range:(5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90,95,100);

 //间隔
$marginandpadding:(m:margin,p:padding);
$direction:(t:top,r:right,b:bottom,l:left);

@each $r in $range{
	//按数字范围循环 间隔
	@each $key,$map in $marginandpadding{
		//遍历$marginandpadding  m5{ maring:5px}  p5{padding:5px}
		 .#{$key}#{$r}{
			 #{$map}:#{$r}px
		 }
		//mh= margin在水平间隔  mv=margin在垂直间隔  padding同理
		 .#{$key}h{
			 #{$map}-left:#{$r}px;
			 #{$map}-right:#{$r}px;
		 }
		 .#{$key}v{
		 			 #{$map}-top:#{$r}px;
		 			 #{$map}-bottom:#{$r}px;
		 }

	  @each $dkey,$d in $direction{
		  //遍历$direction    mt5{ maring-top:5px}  pt5{padding-top:5px}
	  	 .#{$key}#{$dkey}#{$r}{
	  		 #{$map}-#{$d}:#{$r}px
	  	 }
	  }   

	}
	
 	//按数字范围循环 圆角
	.r#{$r}{
		border-radius: #{r}px;
	}
}



// //背景图片
@mixin bgImg($w,$h,$url){
	width: $w;
	height: $h;
	background-image: url($url);
	background-size: cover;
	background-repeat: no-repeat;
}

