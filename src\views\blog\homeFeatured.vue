<!-- 首页精选 -->
<template>
  <div class="app-container f fd-c ">
    <!-- 查询和其他操作 -->
    <div
      v-permission="'/blogSort/getList'"
      class="filter-containe f fd-r ai-c"
      style="margin: 10px 0 10px 0;"

    >
      <el-input
        v-model="keyword"
        clearable
        class="filter-item mr10"
        style="width: 200px;"
        placeholder="请输入分类名"
        @keyup.enter.native="handleFind"
      />
      <el-select
        v-model="laboratoryType"
        class="mr10"
        clearable
        placeholder="请选择"
        @change="blogSortList()"
        @clear="blogSortList()"
      >
        <el-option label="个人实验室" value="1" />
        <el-option label="企业实验室" value="0" />
      </el-select>
      <el-button
        v-permission="'/blogSort/getList'"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFind"
      >查找</el-button
      >
      <el-button
        v-permission="'/blogSort/add'"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleAdd"
      >添加分类</el-button
      >
      <el-button
        v-if="multipleSelection.length"
        v-permission="'/blogSort/deleteBatch'"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDeleteBatch"
      >删除</el-button
      >
    </div>

    <el-table
      :data="tableData"
      :default-sort="{ prop: 'createTime', order: 'ascending' }"
      class="f1"
      height="auto"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="changeSort"
    >
      <el-table-column type="selection" />
      <el-table-column label="序号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="分类名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortName }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :sort-by="['sort']"
        label="排序"
        align="center"
        prop="sort"
        sortable="custom"
      >
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.sort }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="分类介绍" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.content }}</span>
        </template>
      </el-table-column>

      <el-table-column label="实验室类型" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.laboratoryType == 1 ? "个人实验室" : "企业实验室" }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="icon图片" align="center">
        <template slot-scope="scope">
          <!-- <i :class="scope.row.icon"></i> -->
          <el-image
            v-if="scope.row.iconUrl"
            :src="scope.row.iconUrl"
            style="width: 40px; height: 40px"
            fit="contain"
          />
        </template>
      </el-table-column>

      <el-table-column
        :sort-by="['clickCount']"
        label="点击数"
        align="center"
        prop="clickCount"
        sortable="custom"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.clickCount }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :sort-orders="['ascending', 'descending']"
        label="添加时间"
        align="center"
        prop="createTime"
        sortable="custom"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 1" type="success">已启用</el-tag>
          <el-tag v-if="scope.row.status == 0" type="info">已停用</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        :width="this.$store.getters.operationButtonWidth + 'px'"
        label="操作"
        fixed="right"
      >
        <template slot-scope="scope">
          <div ref="operation">
            <el-button
              v-permission="'/blogSort/stick'"
              :type="scope.row.isTop == 1 ? 'warning' : 'success'"
              size="small"
              @click="handleStick(scope.row)"
            >
              {{ scope.row.isTop == 1 ? "取消置顶" : "置顶" }}
            </el-button>

            <el-button
              :type="scope.row.status == 1 ? 'info' : 'success'"
              size="small"
              @click="setStop(scope.row)"
            >
              {{ scope.row.status == 1 ? "停用" : "启用" }}
            </el-button>
            <!-- <el-button
            v-if="scope.row.status == 1"
            size="small"
            type="info"
            @click="setStop(scope.row)"

          >
            停用
          </el-button>
          <el-button
            v-if="scope.row.status == 0"
            type="success"
            size="small"
            @click="setStop(scope.row)"
          >
            启用
          </el-button> -->

            <el-button
              v-permission="'/blogSort/edit'"
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >编辑</el-button
            >
            <!-- <el-button
              v-permission="'/blogSort/delete'"
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >删除</el-button
            > -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!--分页-->
    <div class="block">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :total="total"
        style="float: right; margin-top:10px"
        layout="total, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item
          :label-width="formLabelWidth"
          label="分类名称"
          prop="sortName"
          style="width:600px"
        >
          <el-input v-model="form.sortName" auto-complete="off" />
        </el-form-item>

        <el-form-item
          :label-width="formLabelWidth"
          label="分类介绍"
          prop="content"
          style="width:600px"
        >
          <el-input v-model="form.content" auto-complete="off" />
        </el-form-item>

        <el-form-item
          :label-width="formLabelWidth"
          label="实验室类型"
          prop="laboratoryType"
        >
          <el-radio-group v-model="form.laboratoryType" size="small">
            <el-radio :label="1" border>个人实验室</el-radio>
            <el-radio :label="0" border>企业实验室</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :label-width="formLabelWidth" label="图标" prop="icon">
          <div v-if="iconShow" class="upload_img">
            <img
              :src="form.iconUrl"
              style="width:100px;height:100px"
              @mouseover="icon = true"
              @mouseout="icon = false"
            >
            <i
              v-show="icon"
              class="el-icon-error close-icon"
              @mouseover="icon = true"
              @click="iconShow = false"
            />
          </div>
          <div v-else class="uploadImgBody" @click="iconDialog = true">
            <i class="el-icon-plus avatar-uploader-icon" />
          </div>

        </el-form-item>

        <el-form-item
          :label-width="formLabelWidth"
          label="是否置顶"
          prop="isTop"
        >
          <el-radio-group v-model="form.isTop" size="small">
            <el-radio :label="1" border>是</el-radio>
            <el-radio :label="0" border>否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          :label-width="formLabelWidth"
          label="是否启用"
          prop="status"
        >
          <el-radio-group v-model="form.status" size="small">
            <el-radio :label="1" border>是</el-radio>
            <el-radio :label="0" border>否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          :label-width="formLabelWidth"
          label="是否为精选分类"
          prop="status"
        >
          <el-radio-group v-model="form.homeFixed" size="small">
            <el-radio :label="1" border>是</el-radio>
            <el-radio :label="0" border>否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :label-width="formLabelWidth" label="排序" prop="sort">
          <el-input v-model="form.sort" auto-complete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="iconDialog" width="400px">
      <el-upload
        ref="upload"
        :action="uploadPictureHost"
        :data="otherData"
        :on-error="_uploadImgError"
        :before-upload="_beforeUpload"
        :on-success="_uploadImgSuccess"
        :headers="{
          Authorization:$GetToken()
        }"
        :show-file-list="false"
        name="file"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import {
  addBlogSort,
  blogSortByCite,
  blogSortByClickCount,
  deleteBatchBlogSort,
  editBlogSort,
  getBlogSortList,
  stickBlogSort
} from '@/api/blogSort'
import { getToken } from '@/utils/auth'
import IconsDialog from '../../components/IconsDialog'

export default {
  components: {
    IconsDialog
  },
  data() {
    const checkedIcon = (rule, value, callback) => {
      if (value == null) {
        callback(new Error('请上传icon'))
      } else {
        callback()
      }
    }
    return {
      icon: false,
      iconUrl: null,
      iconShow: false,
      iconDialog: false,
      iconsVisible: false, // 是否显示icon选择器
      multipleSelection: [], // 多选，用于批量删除
      tableData: [],
      keyword: '',
      laboratoryType: null,
      currentPage: 1,
      pageSize: 10,
      total: 0, // 总数量
      uploadPictureHost: null,
      title: '增加分类',
      dialogFormVisible: false, // 控制弹出框
      formLabelWidth: '120px',
      isEditForm: false,
      orderByDescColumn: '',
      orderByAscColumn: '',
      homeFixedActive: 1,
      homeFixed: [
        { id: 0, label: '非首页精选' },
        { id: 1, label: '首页精选' }
      ],
      form: {
        uid: null,
        content: '',
        sortName: '',
        laboratoryType: null,
        icon: null,
        isTop: 0
      },
      rules: {
        sortName: [
          { required: true, message: '分类名称不能为空', trigger: 'blur' },
          { min: 1, max: 10, message: '长度在1到10个字符' }
        ],
        sort: [
          { required: true, message: '排序字段不能为空', trigger: 'blur' },
          { pattern: /^[0-9]\d*$/, message: '排序字段只能为自然数' }
        ],
        laboratoryType: [
          { required: true, message: '实验室类型不能为空', trigger: 'blur' }
        ],
        icon: [{ checkedIcon, trigger: 'blur' }]
      }
    }
  },
  watch: {
    dialogFormVisible() {
      if (!this.dialogFormVisible) {
        this.iconUrl = null
        this.iconShow = false
      }
    }
  },
  created() {
    this.blogSortList()
    // 图片上传地址
    this.uploadPictureHost = process.env.PICTURE_API + '/file/cropperPicture'
    this.otherData = {
      source: 'picture',
      userUid: 'uid00000000000000000000000000000000',
      adminUid: 'uid00000000000000000000000000000000',
      projectName: 'blog',
      sortName: 'admin',
      token: getToken()
    }
  },
  methods: {
    // 从后台获取数据,重新排序
    changeSort(val) {
      // 根据当前排序重新获取后台数据,一般后台会需要一个排序的参数
      if (val.order == 'ascending') {
        this.orderByAscColumn = val.prop
        this.orderByDescColumn = ''
      } else {
        this.orderByAscColumn = ''
        this.orderByDescColumn = val.prop
      }
      this.blogSortList()
    },
    blogSortList: function() {
      var params = {}
      params.keyword = this.keyword
      params.currentPage = this.currentPage
      params.pageSize = this.pageSize
      params.orderByDescColumn = this.orderByDescColumn
      params.orderByAscColumn = this.orderByAscColumn
      params.laboratoryType = this.laboratoryType
      params.homeFixed = this.homeFixedActive
      getBlogSortList(params).then(response => {
        this.tableData = response.data.records
        // 设置操作栏长度
        this.$nextTick(() =>
          this.$store.dispatch('setWidth', this.$refs.operation.children.length)
        )
        this.currentPage = response.data.current
        this.pageSize = response.data.size
        this.total = response.data.total
      })
    },
    getFormObject: function() {
      var formObject = {
        uid: null,
        content: null,
        clickCount: 0,
        sort: 0,
        isTop: 0,
        laboratoryType: 1,
        homeFixed: 0,
        status: 1
      }
      return formObject
    },
    handleFind: function() {
      this.currentPage = 1
      this.blogSortList()
    },
    handleAdd: function() {
      this.title = '增加分类'
      this.dialogFormVisible = true
      this.form = this.getFormObject()
      this.isEditForm = false
    },
    // 通过点击量排序
    handleBlogSortByClickCount: function() {
      this.$confirm(
        '此操作将根据点击量对所有的标签进行降序排序, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          blogSortByClickCount().then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
              this.blogSortList()
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消批量排序')
        })
    },
    // 通过点击量排序
    handleBlogSortByCite: function() {
      this.$confirm(
        '此操作将根据博客引用量对所有的标签进行降序排序, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          blogSortByCite().then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
              this.blogSortList()
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消批量排序')
        })
    },
    handleEdit: function(row) {
      this.title = '编辑分类'
      this.dialogFormVisible = true
      this.isEditForm = true
      if (row.icon) {
        this.iconShow = true
      }
      this.form = Object.assign({}, row)
      if (row.isTop == undefined) this.$set(this.form, 'isTop', 0)
    },
    handleStick: function(row) {
      this.$confirm('此操作将会把该标签放到首位, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var params = {}
          params.uid = row.uid
          stickBlogSort(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
              this.blogSortList()
            } else {
              this.$commonUtil.message.error(response.message)
            }
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消置顶')
        })
    },
    handleDelete: function(row) {
      var that = this
      this.$confirm('此操作将把分类删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var params = []
          params.push(row)
          deleteBatchBlogSort(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
            that.blogSortList()
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消删除')
        })
    },
    handleDeleteBatch: function() {
      var that = this
      if (that.multipleSelection.length <= 0) {
        this.$commonUtil.message.error('请先选中需要删除的内容!')
        return
      }
      this.$confirm('此操作将把选中的分类删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteBatchBlogSort(that.multipleSelection).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
            that.blogSortList()
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消删除！')
        })
    },
    handleCurrentChange: function(val) {
      this.currentPage = val
      this.blogSortList()
    },
    submitForm: function() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          console.log('校验失败')
          return
        } else {
          if (this.isEditForm) {
            editBlogSort(this.form).then(response => {
              console.log(response)
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message)
                this.dialogFormVisible = false
                this.blogSortList()
              } else {
                this.$commonUtil.message.error(response.message)
              }
            })
          } else {
            addBlogSort(this.form).then(response => {
              console.log(response)
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message)
                this.dialogFormVisible = false
                this.blogSortList()
              } else {
                this.$commonUtil.message.error(response.message)
              }
            })
          }
        }
      })
    },

    // 启用停用
    setStop(row) {
      var params = row
      params.status = row.status == 1 ? 0 : 1
      editBlogSort(params).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.$commonUtil.message.success(response.message)
          this.tagList()
        } else {
          this.$commonUtil.message.error(response.message)
        }
      })
    },

    // 改变多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 选择图标
    setIcon(val) {
      this.form.icon = val
    },

    handlePreview() {},
    // 上传封面图片
    _uploadImgError(err, file, fileList) {
      this.$message.error('文件上传失败，请重试！')
    },
    _uploadImgSuccess(response, file) {
      if (response.code == this.$ECode.SUCCESS) {
        this.form.icon = response.data[0].uid
        this.iconShow = true
        this.form.iconUrl = response.data[0].url
        this.iconDialog = false
      } else {
        this.$alert(response.data, '提示', {
          confirmButtonText: '确定'
        })
      }
    },
    _beforeUpload(file) {
      const types = ['image/jpg', 'image/png', 'image/jpeg']
      const isJPG = types.includes(file.type)
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg或png 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    }
  }
}
</script>

<style lang="scss" scoped>

.app-container{
  height: calc(100vh - 130px);
}
.upload_img {
  width: 100px;
  height: 100px;
  position: relative;
  .close-icon {
    // display: none;
    right: 2px;
    top: 2px;
    position: absolute;
    font-size: 24px;
    display: inline-block;
    z-index: 8;
    cursor: pointer;
  }
  // img:hover + .close-icon {
  //   font-size: 24px;
  //   display: inline-block;
  //   z-index: 8;
  // }
}
.uploadImgBody {
  margin-left: 5px;
  width: 100px;
  height: 100px;
  border: dashed 1px #c0c0c0;
  float: left;
  position: relative;
}
.uploadImgBody :hover {
  border: dashed 1px #00ccff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
