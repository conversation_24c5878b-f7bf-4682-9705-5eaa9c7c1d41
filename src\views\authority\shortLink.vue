<template>
  <div class="short-link-container">
    <el-row>
        <el-form :model="sortLinkForm" class="demo-form-inline">
          <el-form-item label="源链接地址">
            <el-col :span="6">
                <el-input v-model="sortLinkForm.originLink" placeholder="请输入源链接地址"></el-input>
            </el-col>
            <el-button style="margin-left: 5px;" type="primary" @click="createShortLink">生成短链</el-button>
          </el-form-item>
          <el-form-item v-if="sortLinkForm.shortLink" label="短链接地址">
            <el-col :span="6">
                <el-input disabled v-model="sortLinkForm.shortLink" placeholder="短链地址"></el-input>
                <span style="display: none;" id="copy-link">{{ sortLinkForm.shortLink }}</span>
            </el-col>
            <el-button style="margin-left: 5px;" type="primary" @click="handleCopy">复制链接</el-button>
          </el-form-item>
        </el-form>
    </el-row>
  </div>
</template>
<script>
import { generateShortLink } from "@/api/shortLink";
export default{
    data(){
        return {
            sortLinkForm: {
                originLink: '',
                shortLink: ''
            }
        }
    },
    methods: {
        // 链接复制
        handleCopy() {
            let input = document.createElement("input");
            let el = document.getElementById("copy-link"); // 获取元素
            input.value = el.innerText; // 设置复制内容
            document.body.appendChild(input); // 添加临时实例
            input.select(); // 选择实例内容
            document.execCommand("copy"); // 执行复制
            document.body.removeChild(input); // 删除临时实例
            this.$message({
                message: '复制成功',
                type: 'success'
            });
        },
        async createShortLink() {
            let params = {
                longLink:this.sortLinkForm.originLink
            }
            let result = await generateShortLink(params);
            if (result.code == this.$ECode.SUCCESS) { 
                let localHref = localStorage.getItem("localPictureBaseUrl");
                localHref && (
                    localHref = localHref.replace(/8443\/img/ig, '')
                );
                this.sortLinkForm.shortLink = `${localHref}/sl/${result.data}`;
                this.$message({
                    message: '生成短链成功',
                    type: 'success'
                });
            }
        }
    },
}
</script>
<style lang='scss' scoped>
.short-link-container{
    padding: 20px 20px;
    /deep/ .is-disabled{
        background-color: transparent !important;
    }
    /deep/ .el-input__inner{
        background-color: transparent !important;
    }
}
</style>