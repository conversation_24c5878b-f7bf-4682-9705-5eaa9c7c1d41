<template>
  <div class="dialog">
    <el-dialog
      append-to-body
      v-if="dialogFlag"
      :title="title"
      :is-center="isCenter"
      :visible.sync="dialogFlag"
      :destroy-on-close="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="50%"
    >
      <!-- 内容区域 -->
      <div class="dialog_content">
        <slot/>
      </div>
      <span v-if="showFooter" slot="footer" class="dialog-footer">
        <el-button v-if="!hideCancel" @click="_cancel">取 消</el-button>
        <el-button :disabled="disFlag" type="primary" style="margin-left:120px" @click="_submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    'append-to-body': {
      type: Boolean,
      default: false
    },
    // dialog显隐标志位
    dialogVisible: {
      type: Boolean,
      default: false
    },
    // 确定按钮方法
    submit: {
      type: Function,
      default: null
    },
    // 取消按钮方法
    cancel: {
      type: Function,
      default: null
    },
    hideCancel: {
      type: Boolean,
      default: null
    },
    // 标题
    title: {
      type: String,
      default: null
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    isCenter: {
      type: Boolean,
      default: false
    },
    // 禁用确定按钮标志位
    disFlag: {
      type: Boolean,
      default: false
    },
    // 是否是系统镜像操作
    isImage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogFlag: false
    }
  },
  watch: {
    dialogVisible: {
      deep: true,
      immediate: true,
      handler(newval) {
        this.dialogFlag = newval
      }
    }
  },
  created() {
    this.dialogFlag = this.dialogVisible
  },
  methods: {
    // 确定按钮方法
    _submit() {
      this.submit()
    },
    // 关闭弹框前操作
    handleClose() {
      if (this.isImage) {
        this.$confirm(
          '关闭后配置信息不会保留！你还要继续吗?',
          '确认关闭窗口吗?',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(async() => {
            this.dialogFlag = false
            this.cancel()
          })
          .catch(() => {})
      } else {
        this.dialogFlag = false
        this.cancel()
      }
    },
    // 取消
    _cancel() {
      this.dialogFlag = false
      this.cancel()
    }
  }
}
</script>
<style lang="scss">
.dialog {
  &::v-deep {
    .el-dialog {
      margin-top: 4vh !important;
      .el-dialog__body {
        height: 88vh !important;
      }
    }
  }
  .dialog_item {
    width: 100%;
    height: 40%;

    .disVirtualList {
      display: flex;
      width: 100%;
      height: auto;
      flex-wrap: wrap;

      .disVirtual_content {
        width: 32.3%;
        height: 40px;
        line-height: 40px;
        padding: 0 5px;
        margin: 0 1% 5px 0;
        box-sizing: border-box;
        position: relative;

        .status_class {
          position: absolute;
          right: 10px;
          top: 13px;
          color: #294261;
        }
      }

      .disVirtual {
        background: #f5f5f5;
      }
    }
  }

  .info_message {
    width: 60%;
    height: 50px;
    border: 1px solid #999;
    background: #f5f5f5;
    padding-left: 20px;
    box-sizing: border-box;
    margin-top: 20px;
  }

  .el-form-item {
    margin-bottom: 20px;
  }
  .el-dialog__footer {
    text-align: center;
  }
}
</style>
