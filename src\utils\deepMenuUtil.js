import Layout from "@/views/layout/Layout";
import layoutChild from '@/views/layout/layoutChild'
import _import from "@/router/_import";

/**
 * 根据子id查找其父级
 * @param {要查找的id} id 
 * @param {数据源} list 
 * @param {*} result 
 * @returns 
 */
function findPByChildId(id, list = [], property="uid", result = []) {
    for (let i = 0; i < list.length; i += 1) {
        const item = list[i]
        // 找到目标
        if (item[property] === id) {
            // 加入到结果中
            result.push(item)
            // 因为可能在第一层就找到了结果，直接返回当前结果
            if (result.length === 1) return result
            return true
        }
        // 如果存在下级节点，则继续遍历
        if (item.children) {
            // 预设本次是需要的节点并加入到最终结果result中
            result.push(item)
            const find = findPByChildId(id, item.children,property, result)
            // 如果不是false则表示找到了，直接return，结束递归
            if (find) {
                return result
            }
            // 到这里，意味着本次并不是需要的节点，则在result中移除
            result.pop()
        }
    }
    // 如果都走到这儿了，也就是本轮遍历children没找到，将此次标记为false
    return false
}


/**
 * 获取菜单路由url
 * @param {后端返回的url字段()} url 
 * @returns 
 */
function getRouterUrl(url) {
    // href外链
    let reg = /(https:|http:)/ig;
    if (!url) return '';
    if (reg.test(url)) return url;
    // 取路径最后一层
    let splitArr = url.split('/');
    return splitArr[splitArr.length - 1];
}
/**
 * 
 * @param {单个路由对象} router 
 * @returns 
 */
function shoudLoadComponent(router) {
    let isJumpExternalUrlNullPanel = Object.prototype.toString.call(router.isJumpExternalUrl);
    // href外链
    let reg = /(https:|http:)/ig;
    // 菜单
    if (router.menuType == 0) {
        // 跳转外链链接
        if ((isJumpExternalUrlNullPanel !== '[object Null]' && router.isJumpExternalUrl == 1) || reg.test(router.path)) {
            return false;
        } else if (router.isJumpExternalUrl == 0) {
            // 不跳转外链
            return true;
        }
        return true;
    } else if (router.path === '*') {
        return false;
    } else {
        return false;
    }
}

/**
 * 获取动态拼接路由路径
 * @param {} menus 
 * @returns 
 */
function getFullRouterUrl(id, menus) { 
    let reg = /(https:|http:)/ig;
    let paths,result = findPByChildId(id, menus);
    if (result) {
        paths = result.map(item => item.path);
    } else { 
        return {
            parentUrl: '未找到该节点',
            fullConcatUrl: '未找到该节点'
        };
    }
    if (result.length > 1) {
        // 外链
        if (reg.test(paths[paths.length - 1])) {
            return {
                parentUrl: paths[paths.length - 1],
                fullConcatUrl: paths[paths.length - 1]
            }
        }
        return {
            parentUrl: paths.slice(0, paths.length - 1).join('/'),
            fullConcatUrl: paths.join('/')
        }
    } else { 
        // 外链
        if (reg.test(paths[0])) { 
            return {
                parentUrl: paths[0],
                fullConcatUrl: paths[0]
            }
        }
        return {
            parentUrl: paths.join('/'),
            fullConcatUrl: paths.join('/')
        }
    }
}

/**
 * 递归后台返回的菜单
 * @param {后台返回的菜单列表} menus 
 */
function deepMenu(menus) {
    let deepMenuAfter = [];
    // 递归菜单
    function deepRouterList(children, initCollects = [], menuCategory) {
        if (!children) return [];
        for (let i = 0; i < children.length; i++) {
            let curObj = {
                isShow: children[i].isShow,
                isJumpExternalUrl: children[i].isJumpExternalUrl,//是否跳转外链
                menuType: children[i].menuType,//菜单类型
                path: getRouterUrl(children[i].url),//用于设置子路由（路由路径)
                targetUrl: children[i].url,//组件跳转路径(拼接路径)
                componentUrl: children[i].url,//类型为菜单的，页面文件路径
                name: children[i].name,
                uid: children[i].uid,
                meta: {
                    dictValue: menuCategory,
                    sort: children[i].sort,
                    uid: children[i].uid,
                    title: children[i].name,
                    icon: children[i].icon,
                    menuType: children[i].menuType
                },
                children: deepRouterList(children[i].childCategoryMenu, [], menuCategory)
            }
            // 目录
            if (children[i].menuType == 2) {
                curObj.component = layoutChild;
            } else { 
                if (shoudLoadComponent(curObj)) {
                    curObj.component = _import(children[i].componentUrl || children[i].url)
                }
            }
            initCollects.push(curObj)
        }
        return initCollects;
    }
    menus.forEach(item => {
        let deepChildren = deepRouterList(item.childCategoryMenu, [], item.menuCategory);
        let routerItem = {
            isShow: item.isShow,
            isJumpExternalUrl: item.isJumpExternalUrl,
            componentUrl: item.url,
            targetUrl: item.url,
            path: item.url,
            menuType: item.menuType,
            component: Layout,
            // redirect: deepChildren.length == 0 ? item.url : deepChildren[0].path,
            name: item.name,
            uid: item.uid,
            meta: {
                uid: item.uid,
                dictValue: item.menuCategory,
                title: item.name,
                icon: item.icon,
                sort:item.sort,
                menuType: item.menuType,
            },
            children: deepChildren
        }
        deepMenuAfter.push(routerItem)
    });
    return deepMenuAfter;
}

export {
    deepMenu,
    shoudLoadComponent,
    getFullRouterUrl,
    findPByChildId
}
