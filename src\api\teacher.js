import request from "@/utils/request";

// 教师信息列表
export function list(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/getList",
        method: "post",
        data: params
    });
}
//添加教师
export function add(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/add",
        method: "post",
        data: params
    });
}
//编辑教师
export function editor(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/edit",
        method: "post",
        data: params
    });
}
//删除教师
export function deleteTeacher(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/deleteBatch",
        method: "post",
        data: params
    });
}
//教师上下架
export function upOrDown(params) {
    return request({
        url: process.env.ADMIN_API + `/teacher/updateStatus?status=${params.status}`,
        method: "post",
        data:params.teacherUid
    });
}
//教师信息类别
export function category(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/getCategoryList",
        method: "get",
        params
    });
}
//教师信息排序
export function sort(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/sort",
        method: "post",
        data: params
    });
}
//教师信息详情
export function detail(params) {
    return request({
        url: process.env.ADMIN_API + "/teacher/getInfo",
        method: "get",
        params
    });
}
