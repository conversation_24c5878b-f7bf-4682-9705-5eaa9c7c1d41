<template>
  <div class="app-container">
    <textarea :id="editorId" rows="10" cols="80"></textarea>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import CKEDITOR from 'CKEDITOR';
export default {
  props: ["value", "height", "placeholder"],
  mounted() {
    // 配置ckeditor插件
    CKEDITOR.plugins.addExternal( 'confighelper', '/static/ckeditor/plugins/confighelper/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'codesnippet', '/static/ckeditor/plugins/codesnippet/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'panelbutton', '/static/ckeditor/plugins/panelbutton/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'floatpanel', '/static/ckeditor/plugins/floatpanel/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'colorbutton', '/static/ckeditor/plugins/colorbutton/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'markdown', '/static/ckeditor/plugins/markdown/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'colordialog', '/static/ckeditor/plugins/colordialog/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'dialog', '/static/ckeditor/plugins/dialog/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'dialogui', '/static/ckeditor/plugins/dialogui/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'smiley', '/static/ckeditor/plugins/smiley/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'widget', '/static/ckeditor/plugins/widget/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'lineutils', '/static/ckeditor/plugins/lineutils/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'eqneditor', '/static/ckeditor/plugins/eqneditor/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'justify', '/static/ckeditor/plugins/justify/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'tableresize', '/static/ckeditor/plugins/tableresize/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'wordcount', '/static/ckeditor/plugins/wordcount/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'notification', '/static/ckeditor/plugins/notification/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'video', '/static/ckeditor/plugins/video/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'fakeobjects', '/static/ckeditor/plugins/fakeobjects/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'liststyle', '/static/ckeditor/plugins/liststyle/', 'plugin.js' );
    CKEDITOR.plugins.addExternal( 'pasteUploadImage', '/static/ckeditor/plugins/pasteUploadImage/', 'plugin.js' );

    // 判断是否存在
    if (CKEDITOR.instances[this.editorId]) {
      CKEDITOR.remove(CKEDITOR.instances[this.editorId])
    }
    // 使用ckeditor替换textarea，设置代码块风格为 zenburn
    // 上传时，携带token信息，以便于被feign拦截后传递给huanyu-admin获取七牛云相关配置
    CKEDITOR.replace(this.editorId,
      {
        height: this.height,
        placeholder: this.placeholder,
        width: '100%',
        toolbar: 'toolbar_Full',
        codeSnippet_theme: 'zenburn',
        customConfig: '/static/ckeditor/config.js',
        filebrowserImageUploadUrl: process.env.PICTURE_API + '/file/ckeditorUploadFile?token=' + getToken(),
        filebrowserUploadUrl: process.env.PICTURE_API + '/file/ckeditorUploadFile?token=' + getToken(),
        pasteUploadFileApi: process.env.PICTURE_API + '/file/ckeditorUploadCopyFile?token=' + getToken(),
        extraPlugins: 'confighelper,codesnippet,panelbutton,floatpanel,colorbutton,markdown,colordialog,dialog,dialogui,smiley,widget,lineutils,eqneditor,justify,tableresize,wordcount,notification,video,fakeobjects,liststyle,pasteUploadImage'
      });

    this.editor = CKEDITOR.instances[this.editorId];
    this.editor.setData(this.textData); // 初始化内容
    // ckeditor中内容改变
    this.editor.on('change', this.onChange);
  },
  created() {
    this.textData = this.value;
  },
  beforeDestroy() {
    this.destroy();
  },
  watch: {
    value(val) {
      try {
        if (this.editor) {
          this.update(val);
        }
      } catch (e) {}
    }
  },
  data() {
    return {
      editor: null, // 编辑器对象
      textData: this.value, // 初始化内容
      editorId: 'editor-' + this.uuid() // 盒子ID
    }
  },
  methods: {
    uuid() {
      return Math.random().toString(36).substr(3, 10)
    },
    update(val) {
      if (this.textData !== val) {
        this.editor.setData(val, { internal: false })
      }
    },
    destroy() {
      try {
        let editor = window['CKEDITOR'];
        if (editor.instances) {
          for (let instance in editor.instances) {
            instance.destroy();
          }
        }
      } catch (e) {}
    },
    onChange() {
      let html = this.editor.getData();
      if (html !== this.value) {
        this.$emit('input', html);
        this.textData = html;
      }
    }
  }
}

</script>
