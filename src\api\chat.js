import request from "@/utils/request";
// WebSocket地址
export const WS_URL = process.env.WS_HOST + '/chat/webSocket'

// 获取在线用户列表
export function getOnlineList(params) {
    return request({
        url: process.env.ADMIN_API + '/chat/getOnlineList',
        method: 'get',
        params
    })
}

// 加载聊天记录列表
export function getChatRecordList(params) {
    return request({
        url: process.env.ADMIN_API + '/admin/userMsg/getChatList',
        method: 'post',
        data:params
    })
}
//更新客服配置
export function editorKefuConfig(params) {
    return request({
        url: process.env.ADMIN_API + '/chat/config/edit',
        method: 'post',
        data: params
    })
}
//获取客服配置
export function kefuInfo(params) {
    return request({
        url: process.env.ADMIN_API + '/chat/config/info',
        method: 'get',
        params
    })
}

//删除当前会话
export function deleteCurrentChat(params) {
    return request({
        url: process.env.ADMIN_API + '/chat/remove/info',
        method: 'get',
        params
    })
}