import request from '@/utils/request'
 
const api = process.env.ADMIN_API

// 目录列表 
export function getCatalogueList(data) {
  return request({
    url: api + '/ctf/practice/tier/list',
    method: 'get',
    data: data
  })
}

// 新增目录
export function addCatalogue(data) {
  return request({
    url: api + '/ctf/practice/tier/insert',
    method: 'post',
    data: data
  })
}

// 修改目录
export function editCatalogue(data) {
  return request({
    url: api + '/ctf/practice/tier/update',
    method: 'post',
    data: data
  })
}

// 删除目录
export function delCatalogue(data) {
  return request({
    url: api + '/ctf/practice/tier/delete',
    method: 'get',
    params: data
  })
}


// ctf列表 
export function getCTFList(data) {
  return request({
    url: api + '/ctf/practice/list',
    method: 'post',
    data: data
  })
}

// 新增ctf
export function addCTF(data) {
  return request({
    url: api + '/ctf/practice/insert',
    method: 'post',
    data: data
  })
}

// 修改ctf
export function editCTF(data) {
  return request({
    url: api + '/ctf/practice/update',
    method: 'post',
    data: data
  })
}

// 删除ctf
export function delCTF(data) {
  return request({
    url: api + '/ctf/practice/delete',
    method: 'get',
    params: data
  })
}

// 统计信息
export function statistics(data) {
  return request({
    url: api + '/ctf/practice/statistics',
    method: 'get',
    params: data
  })
}

// 攻克人名单
export function finishList(data) {
  return request({
    url: api + '/ctf/practice/finish',
    method: 'post',
    data: data
  })
}

// 挑战人名单
export function tryList(data) {
  return request({
    url: api + '/ctf/practice/try',
    method: 'post',
    data: data
  })
}

// 攻克人名单导出
export function finishExport(data) {
  return request({
    url: api + '/ctf/practice/finish/export',
    method: 'post',
    params: data,
    responseType: "blob"
  })
}

// 挑战人名单导出
export function tryExport(data) {
  return request({
    url: api + '/ctf/practice/try/export',
    method: 'post',
    params: data,
    responseType: "blob"
  })
}

// 目录拖拽排序
export function dragSort(data) {
  return request({
    url: api + '/ctf/practice/tier/move/sorted',
    method: 'post',
    data: data,
  })
}