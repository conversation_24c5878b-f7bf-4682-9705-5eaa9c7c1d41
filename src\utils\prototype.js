import commonUtil from "./commonUtil";
import { getToken } from "@/utils/auth";
import SocketIo from "@/utils/socket";
import { Base64 } from 'js-base64'


import { updateTimeShow } from "@/utils/formatDate";

export default {
  install(Vue, options) {
    (Vue.prototype.$downladResetFileName =
      commonUtil.FUNCTIONS.downladResetFileName),
      (Vue.prototype.$formatDate = commonUtil.FUNCTIONS.formatDate),
      (Vue.prototype.$commonUtil = commonUtil.FUNCTIONS);
    Vue.prototype.$ECode = commonUtil.ECode;
    Vue.prototype.$SysConf = commonUtil.SysConf;
    Vue.prototype.$GetToken = getToken;
    Vue.prototype.$SocketIo = SocketIo;
    Vue.prototype.$updateTimeShow = updateTimeShow;
    Vue.prototype.$base64 = Base64;
  }
};
