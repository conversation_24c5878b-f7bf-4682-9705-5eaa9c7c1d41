<template>
  <div class="edit-experiment-info">
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-formData"
    >
      <el-form-item label="资源群集" prop="clusterName">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.clusterName"
          class="header-search-select"
        >
          <el-option
            v-for="option in clusterNameOptions"
            :key="option.zoneName"
            :value="option.zoneName"
            :label="option.zoneName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="云主机类型" prop="specificationId">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.specificationId"
          class="header-search-select"
          @change="changeFlavor"
        >
          <el-option
            v-for="option in specificationNameOptions"
            :key="option.id"
            :value="option.id"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="cloudCategory">
        <el-radio-group v-model="formData.cloudCategory">
          <el-radio-button label="云主机"/>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="CPU" prop="vcpus">
        <el-radio-group v-model="formData.vcpus" @change="changeCpu">
          <el-radio-button v-for="item in cpuListAll" :label="item.name" :key="item.id"/>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内存" prop="ram">
        <el-radio-group v-model="formData.ram" @change="changeMemory">
          <el-radio-button v-for="item in memoryList" :label="item.name" :key="item.id"/>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="系统盘类型" prop="diskTypeId">
        <el-select
          ref="headerSearchSelect"
          v-model="formData.diskTypeId"
          class="header-search-select"
        >
          <el-option
            v-for="option in diskTypeNameOptions"
            :key="option.id"
            :value="option.id"
            :label="option.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="系统盘大小" prop="diskSize">
        <el-slider
          v-model="formData.diskSize"
          :min="20"
          :max="3000"
          style="width: calc(100% - 40px)"
        />
        <el-input
          v-model="formData.diskSize"
          style="width: calc(100% - 40px); margin-right: 10px"
          readonly
        />GB
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  getAvailabilityZoneList,
  getCloudHostType,
  getSystemType,
  treeListById
} from '@/api/sourceLibrary/virtualApi'
import { listLabel } from '@/api/sourceLibrary/admin/label'
export default {
  name: 'EditAdviser',
  components: {},
  // eslint-disable-next-line vue/require-prop-types
  props: ['isSubmitAdviser', 'formData'],
  data() {
    // ip校验
    var checkIp = (rule, value, callback) => {
      const reg =
        /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/
      if (!value) {
        callback()
      } else if (!reg.test(value)) {
        callback(new Error('ip格式不正确！'))
      } else {
        callback()
      }
    }
    return {
      // formData: {
      //   clusterName: '',
      //   specificationId: '',
      //   cloudCategory: '',
      //   cpu: '',
      //   memory: '',
      //   facilityBrand: '',
      //   facilityBrandTypeId: '',
      //   version: '',
      //   administerIp: '',
      //   connector: '',
      //   connector: '',
      //   diskSize: 0,
      //   diskVos: [{ diskSize: 0, diskTypeId: '', diskTypeId: '' }],
      // },
      optionProps: {
        value: 'id',
        label: 'label',
        children: 'children'
      },
      cpuList: [],
      cpuListAll: [],
      memoryList: [],
      diskTypeNameOptions: [],
      clusterNameOptions: [], // 资源集群
      specificationNameOptions: [], // 云主机类型
      facilityBrandOptions: [],
      specAllOptions: [],
      facilityTypeOptions: [],
      connectorOptions: [],
      productPortOptions: [],
      rules: {
        clusterName: [
          { required: true, message: '请输入资源集群', trigger: 'change' }
        ],
        specificationId: [
          { required: true, message: '请输入云主机类型', trigger: 'change' }
        ],
        cloudCategory: [
          { required: true, message: '请输入类型', trigger: 'change' }
        ],
        vcpus: [{ required: true, message: '请选择cpu', trigger: 'change' }],
        ram: [{ required: true, message: '请选择内存', trigger: 'change' }],
        diskTypeId: [
          { required: true, message: '请选择系统盘类型', trigger: 'change' }
        ],
        diskSize: [
          { required: true, message: '请选择系统盘大小', trigger: 'change' }
        ],
        facilityBrandId: [
          { required: true, message: '请选择品牌', trigger: 'change' }
        ],
        facilityBrandTypeId: [
          { required: true, message: '请选择型号', trigger: 'change' }
        ],
        connectorId: [
          { required: true, message: '请选择管理接口', trigger: 'change' }
        ],
        administerIp: [{ validator: checkIp, trigger: 'blur' }]
      },
      expId: ''
    }
  },
  watch: {
    isSubmitAdviser: function(newVal) {
      if (newVal) {
        // 提交
        this.$refs['formData'].validate(async(valid) => {
          console.log('this.formData', this.formData)
          if (valid) {
            // eslint-disable-next-line no-unused-vars
            const params = {
              chapterId: this.formData.chapterId,
              guide: this.formData.guide,
              id: this.expId
            }
            this.$emit('formOk')
            /* try {
              if (this.expId) {
                // 修改
                // await updateExperiment(params)
              } else {
                // 新增
                // await addExperiment(params)
              }
              this.$emit('formOk')
            } catch {
              this.$emit('formError')
            }*/
          } else {
            this.$message.error('请完善表单！')
            this.$emit('formError')
            return false
          }
        })
      }
    }
  },
  created() {
    this.getAvaList()
    this.getCloudTypeList()
    this.getSystemTypeList()
    this.getFacilityBrandList()
  },
  async mounted() {},
  methods: {
    // 品牌
    getFacilityBrandList() {
      const id = 102
      this.facilityBrandOptions = []
      treeListById(id)
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.facilityBrandOptions = res.data
            // if (this.formData.facilityBrandId) {
            this.changeFacilityBrandId()
            // }
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 改变品牌
    changeFacilityBrandId() {
      // this.formData.facilityBrandTypeId = null
      let filterData = this.facilityBrandOptions.filter(
        (ele) => ele.id == this.formData.facilityBrandId
      );
      this.facilityTypeOptions = filterData.length && filterData[0].children;
      if (this.facilityTypeOptions) {
        const facilityBrandTypeId = this.facilityTypeOptions.filter(
          (ele) => ele.id == this.formData.facilityBrandTypeId
        )
        if (!facilityBrandTypeId || !facilityBrandTypeId.length) {
          this.formData.facilityBrandTypeId = null
          this.formData.connectorId = null
        } else {
          this.changeFacilityType()
        }
      } else {
        this.formData.facilityBrandTypeId = null
        this.formData.connectorId = null
      }
    },
    // 改变型号
    changeFacilityType() {
      // this.formData.connectorId = null
      const arr = this.facilityBrandOptions.filter(
        (ele) => ele.id == this.formData.facilityBrandId
      )[0].children
      this.productPortOptions = arr.filter(
        (ele) => ele.id == this.formData.facilityBrandTypeId
      )[0].children
      this.connectorOptions = arr.filter(
        (ele) => ele.id == this.formData.facilityBrandTypeId
      )[0].children
      let connectorId
      if (this.productPortOptions && this.productPortOptions.length) {
        connectorId = this.productPortOptions.filter(
          (ele) => ele.id == this.formData.connectorId
        )
        this.formData.productPort = []
        for (const item of this.productPortOptions) {
          this.formData.productPort.push(item.id)
        }
      }
      if (!connectorId || !connectorId.length) {
        this.formData.connectorId = null
      }
      this.$forceUpdate()
    },
    // 改变品牌型号
    handleChange(val) {
      this.formData.facilityBrandId = val[val.length - 1]
      this.formData.productPort = []
      const params = {
        lableId: this.formData.facilityBrandId,
        pageNum: 1,
        pageSize: 10000
      }
      listLabel(params)
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.connectorOptions = res.rows
            this.productPortOptions = res.rows
            for (const item of this.productPortOptions) {
              this.formData.productPort.push(item.lableId)
            }
            this.$forceUpdate()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(() => {
        })
    },
    // 资源集群下拉框
    async getAvaList() {
      await getAvailabilityZoneList()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.clusterNameOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 云主机类型下拉框
    async getCloudTypeList() {
      await getCloudHostType()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.specificationNameOptions = res.data
            this.formData.specificationId = this.specificationNameOptions[0].id
            for (const item of this.specificationNameOptions) {
              const obj = {
                name: item.vcpus + 'CPU',
                id: item.vcpus
              }
              const arr = this.cpuListAll.filter((ele) => ele.id == obj.id)
              if (!arr || !arr.length) {
                this.cpuListAll.push(obj)
                this.cpuListAll.sort((a, b) => a.id - b.id)
              }
            }
            // if (this.formData.specificationId) {
            this.changeFlavor(this.formData.specificationId)
            // }
            console.log(this.cpuListAll)
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 系统盘类型下拉框
    async getSystemTypeList() {
      await getSystemType()
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.diskTypeNameOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    changeFlavor(val) {
      console.log(val)
      this.specAllOptions = []
      this.memoryList = []
      const arr = this.specificationNameOptions.filter((ele) => ele.id == val)
      console.log('arr', arr)
      this.formData.specificationName = arr[0].name
      if (arr && arr.length) {
        this.cpuList = this.cpuListAll.filter((ele) => ele.id <= arr[0].vCpus)
        this.formData.vcpus = arr[0].vcpus + 'CPU'
      }
      for (const item of this.specificationNameOptions) {
        const obj = {
          name: item.ram >= 1024 ? item.ram / 1024 + 'GB' : item.ram + 'MB',
          id: item.ram,
          cpu: item.vcpus
        }
        const memory = this.specAllOptions.filter((ele) => {
          return ele.name === obj.name && ele.cpu == arr[0].vcpus
        })
        if (!memory || !memory.length) {
          this.specAllOptions.push(obj)
        }
      }
      this.memoryList = this.specAllOptions.filter((ele) => {
        return ele.cpu == arr[0].vcpus
      })
      // console.log('this.memoryList', this.memoryList)
      this.memoryList.sort((a, b) => a.id - b.id)
      this.formData.ram =
        arr[0].ram >= 1024 ? arr[0].ram / 1024 + 'GB' : arr[0].ram + 'MB'
    },

    addDiskVos() {
      const obj = { diskSize: 0, diskTypeId: '' }
      if (this.formData.diskVos.length > 1) {
        return
      }
      this.formData.diskVos.push(obj)
    },
    changeDiskVos(item, index) {
      const arr = this.diskTypeNameOptions.filter(
        (ele) => ele.diskTypeId == item.diskTypeId
      )
      this.formData.diskVos[index].diskTypeId = arr[0].diskTypeId
    },
    deleteDiskVos(item, index) {
      this.formData.diskVos.splice(index, 1)
    },
    changeCpu(val) {
      this.memoryList = []
      const arr = this.specificationNameOptions.filter(
        (ele) => ele.vcpus == parseInt(val)
      )
      for (const item of arr) {
        const obj = {
          id: item.ram,
          name: item.ram >= 1024 ? item.ram / 1024 + 'GB' : item.ram + 'MB'
        }
        this.memoryList.push(obj)
        this.memoryList.sort((a, b) => a.id - b.id)
      }
      const arr1 = this.memoryList.filter((ele) => ele.name == this.formData.ram)
      if (!arr1 || !arr1.length) {
        this.formData.ram = this.memoryList[0].name
      }
      this.formData.specificationId = this.specificationNameOptions.filter(
        (ele) =>
          ele.vcpus == parseInt(val) &&
          ele.ram ==
            (this.formData.ram.indexOf('MB') != -1
              ? parseInt(this.formData.ram)
              : parseInt(this.formData.ram) * 1024)
      )[0].id
    },
    changeMemory(val) {
      let memory
      if (val.indexOf('MB') != -1) {
        memory = parseInt(val)
      } else {
        memory = parseInt(val) * 1024
      }
      // let memory = parseInt(val) < 1024 ? parseInt(val) * 1024 : parseInt(val)
      const arr = this.specificationNameOptions.filter(
        (ele) => ele.vcpus == parseInt(this.formData.vcpus) && ele.ram == memory
      )
      this.formData.specificationId = this.specificationNameOptions.filter(
        (ele) => ele.vcpus == parseInt(this.formData.vcpus) && ele.ram == memory
      )[0].id
      console.log(arr)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/scss/config.scss';
@import '~@/assets/scss/base.scss';
@import '~@/assets/scss/mixin.scss';
.edit-experiment-info {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  box-sizing: border-box;
  // @include paddingBoxSizing(0 0 0 30px);
  .el-form::v-deep {
    .el-upload,
    .el-upload__tip {
      display: inline-block;
      margin-left: 10px;
    }
    .title_text {
      // height: 40px;
      width: 100%;
      // line-height: 40px;
      padding: 0 10px;
      box-sizing: border-box;
      border: 1px solid #d5d5d5;
      background: #f5f5f5;
      margin-bottom: 20px;
      p {
        margin: 20px 0;
      }
    }
    .productPort_class {
      width: 100%;
    }
    .el-form-item__content {
      .el-radio-group {
        width: 94%;
        @include flex(flex-start, flex-start);
        flex-wrap: wrap;
        .el-radio-button {
          margin-bottom: 5px;
          .el-radio-button__inner {
            border: 0;
            border: 1px solid #dcdfe6;
            border-radius: 0;
            padding: 0;
            width: 86px;
            height: 32px;
            box-shadow: 0;
            @include flex(center);
          }
        }
      }
    }
  }
}
</style>
