import request from '@/utils/request'

// 获取试卷分页列表
export function getTestPaperList(params) {
  return request({
    url: process.env.ADMIN_API + '/train/paper/pageList',
    method: 'post',
    data: params
  })
}

// 获取试卷分页列表
export function getTitlePaperList(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/pageList',
    method: 'post',
    data: params
  })
}

export function getTitlePaperListV2(params) {
  return request({
    url: process.env.ADMIN_API + '/train/titleV2/pageList',
    method: 'post',
    data: params
  })
}

// 获取试卷详情
export function getTestPaperDetail(params) {
  return request({
    url: process.env.ADMIN_API + `/train/paper/details/${params.uid}`,
    method: 'get'
  })
}

// 添加试卷
export function addTestPaper(params) {
  return request({
    url: process.env.ADMIN_API + '/train/paper/add',
    method: 'post',
    data: params
  })
}

// 修改试卷
export function editTestPaper(params) {
  return request({
    url: process.env.ADMIN_API + '/train/paper/edit',
    method: 'post',
    data: params
  })
}

// 删除试卷
export function deleteTestPaper(params) {
  return request({
    url: process.env.ADMIN_API + `/train/paper/delete/${params.uid}`,
    method: 'get'
  })
}

// 转移试卷
export function moveTestPaper(params) {
  return request({
    url: process.env.ADMIN_API + '/train/paper/moving',
    method: 'post',
    data: params
  })
}

// 复制试卷
export function copyTestPaper(params) {
  return request({
    url: process.env.ADMIN_API + '/train/paper/copy',
    method: 'post',
    data: params
  })
}

// 获取试卷分页列表(通过节点uid数组)
export function getTitlePaperListByUidsV2(params) {
  return request({
    url: process.env.ADMIN_API + '/train/titleV2/listByCategoryUids',
    method: 'post',
    data: params
  })
}
