<template>
  <div class="app-container">
    <!-- 查询和其他操作 -->
    <div class="filter-container" style="margin: 10px 0 10px 0;">
      <el-button
        class="filter-item"
        type="primary"
        @click="handleAdd"
        icon="el-icon-edit"
        v-permission="'/categoryMenu/add'"
        >添加菜单</el-button
      >
      <el-button
        class="filter-item"
        type="primary"
        @click="reloadMenu"
        icon="el-icon-edit"
        >刷新菜单</el-button>
    </div>
    <el-table
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      :data="tableData"
      style="width: 100%;margin-bottom: 20px;"
      row-key="uid"
      border
      :tree-props="{ children: 'childCategoryMenu', hasChildren: 'hasChildren' }">
      <!-- <el-table-column
        label="序号"
        width="180">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="name"
        label="菜单名称"
        width="180">
      </el-table-column>
      <el-table-column label="菜单级别" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.menuType==2?'success': scope.row.menuType==0?'danger':'warning'">
              {{ showMenuLeavelLabel(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="菜单简介" width="200" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.summary }}</span>
          </template>
        </el-table-column>

        <el-table-column label="功能类别" align="center">
          <template slot-scope="scope">
            <span>
              {{ scope.row.functionType == 1 ? "前台" : "后台" }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="图标" width="50" align="center">
          <template slot-scope="scope">
            <i :class="scope.row.icon" />
          </template>
        </el-table-column>

        <el-table-column label="路由" width="250" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.url }}</span>
          </template>
        </el-table-column>

        <el-table-column label="是否显示" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              v-for="item in yesNoDictList"
              :key="item.uid"
              v-if="scope.row.isShow == item.dictValue"
              :type="item.listClass"
              >{{ item.dictLabel }}</el-tag
            >
          </template>
        </el-table-column>

        <el-table-column label="是否跳转外链" width="120" align="center">
          <template slot-scope="scope">
            <el-tag
              v-for="item in jumpExternalDictList"
              :key="item.uid"
              v-if="scope.row.isJumpExternalUrl == item.dictValue"
              :type="item.listClass"
              >{{ item.dictLabel }}</el-tag
            >
          </template>
        </el-table-column>

        <el-table-column label="排序" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="warning">{{ scope.row.sort }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" float="right" min-width="270">
          <template slot-scope="scope">
            <template v-if="scope.row.menuType != 1">
              <el-button
                @click="handleStick(scope.row)"
                type="warning"
                size="small"
                v-permission="'/categoryMenu/stick'"
                >置顶</el-button
              >
              <el-button
                @click="handleEdit(scope.row)"
                type="primary"
                size="small"
                v-permission="'/categoryMenu/edit'"
                >编辑</el-button
              >
              <el-button
                @click="handleDelete(scope.row)"
                type="danger"
                size="small"
                v-permission="'/categoryMenu/delete'"
                >删除</el-button
              >
            </template>
          </template>
        </el-table-column>
    </el-table>
    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item
          label="菜单名称"
          :label-width="formLabelWidth"
          prop="name"
        >
          <el-input v-model="form.name" auto-complete="off"></el-input>
        </el-form-item>

        <!-- <el-form-item
          label="菜单等级"
          :label-width="formLabelWidth"
          prop="menuLevel"
        >
          <el-select v-model="form.menuLevel" placeholder="请选择">
            <el-option
              v-for="item in menuLevelDictList"
              :key="item.uid"
              :label="item.dictLabel"
              v-if="item.dictValue != 3"
              :value="parseInt(item.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          label="菜单类型"
          :label-width="formLabelWidth"
          prop="menuType"
        >
            <el-radio-group v-model="form.menuType" size="small">
                <el-radio :label="2" border>目录</el-radio>
                <el-radio :label="0" border>菜单</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item
            label="选择父级"
            :label-width="formLabelWidth"
            prop="menuCategory"
          >
            <el-cascader
            @change="cascaderChange"
            filterable
            v-model="form.parentUid"
            :options="parentDirs"
            :props="{ checkStrictly: true, emitPath:false, value:'uid', label:'name', children:'childCategoryMenu' }"
            clearable></el-cascader>
            <!-- <el-select
              v-model="form.parentUid"
              clearable
              placeholder="请选择菜单类型"
            >
              <el-option
                v-for="item in menuCategoryList"
                :key="item.uid"
                :label="item.dictLabel"
                :value="parseInt(item.dictValue)"
              ></el-option>
            </el-select> -->
          </el-form-item>
        <el-form-item
          label="菜单分类"
          :label-width="formLabelWidth"
          prop="menuCategory"
        >
          <el-select
            v-model="form.menuCategory"
            clearable
            placeholder="请选择菜单类型"
          >
            <el-option
              v-for="item in menuCategoryList"
              :key="item.uid"
              :label="item.dictLabel"
              :value="parseInt(item.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>

        <!-- <el-form-item
          v-if="form.menuLevel == 2"
          label="父菜单名"
          :label-width="formLabelWidth"
          prop="parentUid"
        >
          <el-select
            v-model="form.parentUid"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="请输入父菜单名"
            :remote-method="remoteMethod"
            :loading="loading"
          >
            <el-option
              v-for="item in menuOptions"
              :key="item.uid"
              :label="item.name"
              :value="item.uid"
            ></el-option>
          </el-select>
        </el-form-item> -->

        <el-form-item
          label="菜单介绍"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input v-model="form.summary" auto-complete="off"></el-input>
        </el-form-item>

        <el-form-item label="图标" :label-width="formLabelWidth">
          <el-input v-model="form.icon" placeholder="请输入前图标名称">
            <el-button
              slot="append"
              icon="el-icon-setting"
              @click="openIconsDialog('prefix-icon')"
            >
              选择
            </el-button>
          </el-input>
        </el-form-item>

        <el-form-item
          label="功能类型"
          :label-width="formLabelWidth"
          prop="functionType"
        >
          <el-radio-group v-model="form.functionType" size="small">
            <el-radio :label="1" border>前台</el-radio>
            <el-radio :label="0" border>后台</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="是否跳转外链"
          :label-width="formLabelWidth"
          prop="isShow"
        >
          <el-radio-group v-model="form.isJumpExternalUrl" size="small">
            <el-radio
              v-for="item in jumpExternalDictList"
              :key="item.uid"
              :label="parseInt(item.dictValue)"
              border
              >{{ item.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="页面实际路径" label-width="123px">
          <el-input
            v-model="form.componentUrl"
            placeholder="组件、vue页面实际路径"
            auto-complete="off"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="路由" :label-width="formLabelWidth" prop="url">
          <el-input
            v-model="form.url"
            placeholder="跳转外链时，路由为外部URL"
            auto-complete="off"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="是否显示"
          :label-width="formLabelWidth"
          prop="isShow"
        >
          <el-radio-group v-model="form.isShow" size="small">
            <el-radio
              v-for="item in yesNoDictList"
              :key="item.uid"
              :label="parseInt(item.dictValue)"
              border
              >{{ item.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>

        <el-form-item label="排序" :label-width="formLabelWidth" prop="sort">
          <el-input v-model="form.sort" auto-complete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <icons-dialog
      :visible.sync="iconsVisible"
      :current="form.icon"
      @select="setIcon"
    />
  </div>
</template>

<script>
import { findTargetZIndex } from '@/utils/deepUtils'
import {addMenu, deleteMenu, editMenu, getAllMenu, getMenuList, stickMenu, getListNotMe} from "@/api/categoryMenu";
import {getListByDictTypeList} from "@/api/sysDictData";
import IconsDialog from "../../components/IconsDialog";
import { async } from 'q';

export default {
  components: {
    IconsDialog
  },
  data() {
    return {
      parentDirs:[],
      iconsVisible: false, // 是否显示icon选择器
      activeData: "", // 激活的图标
      showHeader: false, //是否显示表头
      tableData: [],
      keyword: "",
      menuLevel: "",
      currentPage: 1,
      pageSize: 10,
      total: 0, //总数量
      title: "增加菜单",
      dialogFormVisible: false, //控制弹出框
      formLabelWidth: "120px",
      isEditForm: false,
      menuLevelDictList: [], //菜单等级字典
      yesNoDictList: [], // 是否字典
      jumpExternalDictList: [], // 是否字典
      yesNoDefault: null,
      jumpExternalDefault: null,
      form: {
        menuLevel:1,
        menuType:'',
        componentUrl:'',
        uid: null,
        parentUid:'',
        name: "",
        summary: "",
        icon: "",
        url: "",
        sort: "",
        functionType: null
      },
      loading: false,
      menuOptions: [], //一级菜单候选项
      menuCategoryList: [],
      rules: {
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1到20个字符" }
        ],
        menuLevel: [
          { required: true, message: "菜单等级不能为空", trigger: "blur" }
        ],
        parentUid: [
          { required: true, message: "父菜单不能为空", trigger: "blur" }
        ],
        summary: [
          { required: true, message: "菜单简介不能为空", trigger: "blur" }
        ],
        icon: [{ required: true, message: "图标不能为空", trigger: "blur" }],
        url: [{ required: true, message: "路由不能为空", trigger: "blur" }],
        isShow: [
          { required: true, message: "显示字段不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "排序字段不能为空", trigger: "blur" },
          { pattern: /^[0-9]\d*$/, message: "排序字段只能为自然数" }
        ],
        functionType: [
          { required: true, message: "功能类型不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.initGetListNotMe();
    this.getDictList();
    this.menuList();
  },
  methods: {
    showMenuLeavelLabel(data) { 
      if (data.menuType == 1) {
        return '按钮';
      } else if (data.menuType == 0) {
        return `${data.menuLevel} 级菜单`;
      } else { 
        return `${data.menuLevel} 级目录`;
      }
    },
    cascaderChange() { 
      let menuIndex= findTargetZIndex(this.form.parentUid,this.parentDirs,'uid','childCategoryMenu')
      this.form.menuLevel = menuIndex;
    },
    // 刷新菜单
    async reloadMenu() { 
      await this.menuList();
      this.updateMenuCache();
    },
    async initGetListNotMe(uid) { 
      let params = {
        uid
      }
      let result = await getListNotMe(params);
      if (result.code == this.$ECode.SUCCESS) { 
        this.parentDirs = result.data;
        this.parentDirs.unshift({
          childCategoryMenu: [],
          uid: 1,
          name:'一级菜单/目录'
        })
      }
      console.log("getListNotMe", result);
    },
    menuList: function() {
      getAllMenu().then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          let tableData = response.data;
          // 递归更新菜单等级
          function deepSetZIndex(childrens) { 
            if (childrens.length) { 
              childrens.forEach(item => { 
                item.menuLevel = findTargetZIndex(item.uid, tableData, 'uid', 'childCategoryMenu');
                if (item.childCategoryMenu.length) { 
                  deepSetZIndex(item.childCategoryMenu);
                }
              })
            }
          }
          deepSetZIndex(tableData)
          this.tableData = tableData;
          this.menuOptions = tableData;
        }
      });
    },
    // 选择图标
    setIcon(val) {
      this.form.icon = val;
    },
    openIconsDialog(model) {
      this.iconsVisible = true;
      this.currentIconModel = model;
    },
    /**
     * 字典查询
     */
    getDictList: function() {
      var dictTypeList = [
        "sys_menu_level",
        "sys_yes_no",
        "sys_jump_external",
        "menu_category"
      ];
      getListByDictTypeList(dictTypeList).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          var dictMap = response.data;
          this.menuLevelDictList = dictMap.sys_menu_level.list;
          this.yesNoDictList = dictMap.sys_yes_no.list;
          this.jumpExternalDictList = dictMap.sys_jump_external.list;
          if (dictMap.sys_yes_no.defaultValue) {
            this.yesNoDefault = parseInt(dictMap.sys_yes_no.defaultValue);
          }
          if (dictMap.sys_jump_external.defaultValue) {
            this.jumpExternalDefault = parseInt(
              dictMap.sys_jump_external.defaultValue
            );
          }
          if (dictMap.menu_category) {
            this.menuCategoryList = dictMap.menu_category.list;
          }
        }
      });
    },
    getFormObject: function() {
      var formObject = {
        uid: null,
        name: "",
        summary: "",
        icon: "",
        menuLevel:1,
        url: "",
        sort: 0,
        parentUid:'',
        menuType: 0, //菜单类型  菜单
        isShow: this.yesNoDefault,
        isJumpExternalUrl: this.jumpExternalDefault
      };
      return formObject;
    },
    handleFind: function() {
      this.menuList();
    },
    handleAdd:async function() {
      this.title = "增加菜单";
      await this.initGetListNotMe();
      this.dialogFormVisible = true;
      this.form = this.getFormObject();
      this.isEditForm = false;
    },
    // 修改菜单缓存
    async updateMenuCache() { 
      await this.$store.dispatch("GetMenu").then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          let menuInfo = response.data;
          if (menuInfo && menuInfo.menuList) {
            this.$store.dispatch("setRouter", menuInfo.menuList);
          } else {
            this.$store.dispatch("setRouter", []);
          }
        }
      });
    },
    handleEdit: async function (row) {
      // 获取排除自身的所有目录
      await this.initGetListNotMe(row.uid)
      this.dialogFormVisible = true;
      this.isEditForm = true;
      this.title = "编辑菜单";
      this.form = Object.assign({}, row, {
        parentUid:row.parentUid ? row.parentUid:1
      });
    },
    handleStick: function(row) {
      this.$confirm("此操作将会把该标签放到首位, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {};
          params.uid = row.uid;
          stickMenu(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.menuList();
              this.$commonUtil.message.success(response.message);
            } else {
              this.$commonUtil.message.error(response.message);
            }
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消置顶");
        });
    },
    handleDelete: function(row) {
      this.$confirm("此操作将把菜单删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {};
          params.uid = row.uid;
          deleteMenu(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              // 修改菜单缓存
              this.updateMenuCache();
              this.$commonUtil.message.success(response.message);
            } else {
              this.$commonUtil.message.error(response.message);
            }
            this.menuList();
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消删除");
        });
    },
    //菜单远程搜索函数
    remoteMethod: function(query) {
      if (query !== "") {
        //这里只搜索一级菜单出来
        var params = new URLSearchParams();
        params.append("keyword", query);
        params.append("menuLevel", 1);
        params.append("pageSize", 100);
        getMenuList(params).then(response => {
          if (response.code == this.$ECode.SUCCESS) {
            this.menuOptions = response.data.data.records;
          }
        });
      } else {
        this.menuOptions = [];
      }
    },

    submitForm: function () {
      console.log(this.form);
      this.$refs.form.validate(valid => {
        if (!valid) {
          console.log("校验失败");
        } else {
          this.form.parentUid == 1 ? this.form.parentUid='' :null;
          // 编辑菜单
          if (this.isEditForm) {
            editMenu(this.form).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                // 修改菜单缓存
                this.updateMenuCache();
                this.$commonUtil.message.success(response.message);
                // 刷新菜单数据
                this.menuList();
                this.dialogFormVisible = false;
              } else {
                this.$commonUtil.message.error(response.message);
              }
            });
          } else {
            // 添加菜单
            addMenu(this.form).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                // 修改菜单缓存
                this.updateMenuCache();
                this.$commonUtil.message.success(response.message);
                this.dialogFormVisible = false;
                this.menuList();
              } else {
                this.$commonUtil.message.error(response.message);
              }
            });
          }
        }
      });
    }
  }
};
</script>
