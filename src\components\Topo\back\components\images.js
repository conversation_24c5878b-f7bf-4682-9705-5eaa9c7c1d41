export const physicalImages = [
  {
    label: '交换机组',
    image: require('@/assets/topo/physical/-s-交换机组.png')
  },
  {
    label: '数据库服务器',
    image: require('@/assets/topo/physical/-s-数据库服务器.png')
  },
  {
    label: '堡垒机',
    image: require('@/assets/topo/physical/堡垒机.png')
  },
  {
    label: '测量表计',
    image: require('@/assets/topo/physical/测量表计.png')
  },
  {
    label: '防火墙（网闸）',
    image: require('@/assets/topo/physical/防火墙（网闸）.png')
  },
  {
    label: '服务器',
    image: require('@/assets/topo/physical/服务器.png')
  },
  {
    label: '负载均衡',
    image: require('@/assets/topo/physical/负载均衡.png')
  },
  {
    label: '高可持续威胁攻击检测系统',
    image: require('@/assets/topo/physical/高可持续威胁攻击检测系统.png')
  },
  {
    label: '工控审计、日志审计',
    image: require('@/assets/topo/physical/工控审计、日志审计.png')
  },
  {
    label: '工控IDS',
    image: require('@/assets/topo/physical/工控IDS.png')
  },
  {
    label: '继电保护',
    image: require('@/assets/topo/physical/继电保护.png')
  },
  {
    label: '交换机',
    image: require('@/assets/topo/physical/交换机.png')
  },
  {
    label: '开关设备',
    image: require('@/assets/topo/physical/开关设备.png')
  },
  {
    label: '客户端',
    image: require('@/assets/topo/physical/客户端.png')
  },
  {
    label: '漏洞扫描系统',
    image: require('@/assets/topo/physical/漏洞扫描系统.png')
  },
  {
    label: '路由器',
    image: require('@/assets/topo/physical/路由器.png')
  },
  {
    label: '上网行为管理',
    image: require('@/assets/topo/physical/上网行为管理.png')
  },
  {
    label: '无线路由器',
    image: require('@/assets/topo/physical/无线路由器.png')
  },
  {
    label: '以太网交换机',
    image: require('@/assets/topo/physical/以太网交换机.png')
  },
  {
    label: '应用防火墙',
    image: require('@/assets/topo/physical/应用防火墙.png')
  },
  {
    label: 'DCS,PLC、FCS、WINCC、SCADA',
    image: require('@/assets/topo/physical/DCS,PLC、FCS、WINCC、SCADA.png')
  },
  {
    label: 'IDS入侵检测系统',
    image: require('@/assets/topo/physical/IDS入侵检测系统.png')
  },
  {
    label: 'IPS入侵防御系统',
    image: require('@/assets/topo/physical/IPS入侵防御系统.png')
  },
  {
    label: 'VPN网关',
    image: require('@/assets/topo/physical/VPN网关.png')
  }
]

export const virtualImages = [
  {
    label: '堡垒机',
    image: require('@/assets/topo/virtual/堡垒机.png')
  },
  {
    label: '测量表计',
    image: require('@/assets/topo/virtual/测量表计.png')
  },
  {
    label: '防火墙（网闸）',
    image: require('@/assets/topo/virtual/防火墙（网闸）.png')
  },
  {
    label: '服务器',
    image: require('@/assets/topo/virtual/服务器.png')
  },
  {
    label: '负载均衡',
    image: require('@/assets/topo/virtual/负载均衡.png')
  },
  {
    label: '高可持续威胁攻击检测系统',
    image: require('@/assets/topo/virtual/高可持续威胁攻击检测系统.png')
  },
  {
    label: '工控审计、日志审计',
    image: require('@/assets/topo/virtual/工控审计、日志审计.png')
  },
  {
    label: '工控IDS',
    image: require('@/assets/topo/virtual/工控IDS.png')
  },
  {
    label: '继电保护',
    image: require('@/assets/topo/virtual/继电保护.png')
  },
  {
    label: '交换机',
    image: require('@/assets/topo/virtual/交换机.png')
  },
  {
    label: '交换机组',
    image: require('@/assets/topo/virtual/交换机组.png')
  },
  {
    label: '开关设备',
    image: require('@/assets/topo/virtual/开关设备.png')
  },
  {
    label: '客户端',
    image: require('@/assets/topo/virtual/客户端.png')
  },
  {
    label: '漏洞扫描系统',
    image: require('@/assets/topo/virtual/漏洞扫描系统.png')
  },
  {
    label: '路由器',
    image: require('@/assets/topo/virtual/路由器.png')
  },
  {
    label: '上网行为管理',
    image: require('@/assets/topo/virtual/上网行为管理.png')
  },
  {
    label: '数据库服务器',
    image: require('@/assets/topo/virtual/数据库服务器.png')
  },
  {
    label: '无线路由器',
    image: require('@/assets/topo/virtual/无线路由器.png')
  },
  {
    label: '以太网交换机',
    image: require('@/assets/topo/virtual/以太网交换机.png')
  },
  {
    label: '应用防火墙',
    image: require('@/assets/topo/virtual/应用防火墙.png')
  },
  {
    label: 'DCS,PLC、FCS、WINCC、SCADA',
    image: require('@/assets/topo/virtual/DCS,PLC、FCS、WINCC、SCADA.png')
  },
  {
    label: 'IDS入侵检测系统',
    image: require('@/assets/topo/virtual/IDS入侵检测系统.png')
  },
  {
    label: 'IPS入侵防御系统',
    image: require('@/assets/topo/virtual/IPS入侵防御系统.png')
  },
  {
    label: 'VPN网关',
    image: require('@/assets/topo/virtual/VPN网关.png')
  }
]
