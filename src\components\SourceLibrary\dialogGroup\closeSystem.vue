<template>
  <div class="dialog">
    <dialogCom :dialog-visible="dialogVisible" :submit="_submit" :cancel="_closeDialog" :dis-flag="disFlag" title="关机">
      <template>
        <div class="dialog_item">
          <p class="text_color_6">确定关闭下列共 {{ hostingNum }} 个 云主机?</p>
          <p v-if="ignoreNum" class="text_color_3">
            <svg-icon icon-class="warningmsg" class-name="card-panel-icon" />
            {{ ignoreNum }}个 云主机 无法操作，已忽略!
          </p>
          <div v-if="virtualList.length" class="disVirtualList">
            <div
              v-for="(item, index) in virtualList"
              :key="index"
              :class="item.status == 1 ? 'disVirtual' : ''"
              class="disVirtual_content"
            >
              <svg-icon icon-class="computer" class-name="card-panel-icon" />
              {{ item.deviceName }}
              <svg-icon
                v-if="item.status == 0"
                icon-class="true"
                class-name="card-panel-icon status_class"
              />
            </div>
          </div>
          <div class="info_message">
            <p>平台将尝试正常关机，失败后进行强制关机操作！</p>
          </div>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import dialogCom from '@/components/SourceLibrary/dialogGroup/index'
export default {
  components: {
    dialogCom
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      disFlag: false,
      dialogFlag: false,
      disVirtualList: [], // 无法操作主机列表
      virtualList: [] // 带状态主机列表
    }
  },
  computed: {
    // 忽略主机数
    ignoreNum() {
      return this.virtualList.filter((ele) => ele.status == 1).length
    },
    // 可操作主机数
    hostingNum() {
      return this.virtualList.filter((ele) => ele.status == 0).length
    }
  },
  created() {
    this.getList()
    this.virtualList = this.list
  },
  methods: {
    // 获取数据列表
    getList() {},
    _submit() {},
    _closeDialog() {
      this.$parent.closeDialog = false
    }
  }
}
</script>
