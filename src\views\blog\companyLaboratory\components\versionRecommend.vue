<template>
  <div class="content-manage-child">
    <div class="top">
      <div class="left">
        <span>时间筛选：</span>
        <el-date-picker
          size="mini"
          v-model="searchParams.date"
          type="date"
          placeholder="选择日期"
        >
        </el-date-picker>
      </div>
      <div class="right">
        <el-button size="mini" v-if="ids.length" type="danger" :disabled="ids.length == 0">删除</el-button>
        <el-button size="mini">操作日志</el-button>
        <el-button size="mini" @click="addDialog = true" type="primary">
          添加案例
        </el-button>
      </div>
    </div>

    <el-table
      :data="dataList"
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="versionName" label="版本名称"> </el-table-column>
      <el-table-column prop="introduction" label="版本简介">
        <template slot-scope="scope">
          <div style="border:1px solid black">{{ scope.row.introduction }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="functionShow" label="版本功能展示">
        <template slot-scope="scope">
          <div style="border:1px solid black">{{ scope.row.functionShow }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="版本定价"> </el-table-column>
      <el-table-column prop="createTime" label="添加时间"> </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        :width="this.$store.getters.operationButtonWidth + 'px'"
      >
        <template slot-scope="scope">
          <div ref="operation">
            <el-button type="primary" size="mini" @click="openEditCase(scope)">
              编辑
            </el-button>
            <!-- <el-button
              type="danger"
              size="mini"
              @click="deleteCase(scope.row.id)"
            >
              删除
            </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部分页栏 -->
    <el-pagination
      style="float: right; margin-top:10px"
      class="bottom"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="searchParams.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>

    <el-dialog title="添加版本推荐" :visible.sync="addDialog" width="700px">
      <el-form
        ref="addForm"
        :rules="addFormRules"
        :model="addForm"
        label-width="120px"
      >
        <el-form-item label="版本名称" prop="versionName">
          <el-input
            v-model="addForm.versionName"
            class="company-name"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="版本简介" prop="introduction">
          <el-input
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            v-model="addForm.introduction"
          ></el-input>
        </el-form-item>
        <el-form-item label="版本功能展示" prop="functionShow">
          <el-input v-model="addForm.functionShow"></el-input>
        </el-form-item>
        <el-form-item label="版本定价" prop="price">
          <el-input v-model="addForm.price"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="addCase">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="编辑版本推荐信息"
      :visible.sync="editDialog"
      width="700px"
    >
      <el-form
        ref="editForm"
        :rules="addFormRules"
        :model="editForm"
        label-width="120px"
      >
        <el-form-item label="版本名称" prop="versionName">
          <el-input
            v-model="editForm.versionName"
            maxlength="10"
            class="company-name"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="企业简介" prop="introduction">
          <el-input
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            v-model="editForm.introduction"
          ></el-input>
        </el-form-item>
        <el-form-item label="跳转链接" prop="functionShow">
          <el-input v-model="editForm.functionShow"></el-input>
        </el-form-item>
        <el-form-item label="版本定价" prop="price">
          <el-input v-model="editForm.price"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="editCase">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchParams: {
        status: "1",
        limit: 10,
        page: 1
      },
      total: 10,
      loading: false,
      dataList: [],
      ids: [],
      addDialog: false,
      addForm: {
        versionName: "",
        introduction: "",
        price: "",
        functionShow: ""
      },
      editDialog: false,
      editForm: {},
      addFormRules: {
        versionName: [
          { required: true, message: "请输入文章详情", trigger: "blur" },
          { max: 15, message: "标题最多十个15 个字符", trigger: "blur" }
        ],
        introduction: [
          { required: true, message: "请输入文章详情", trigger: "blur" }
        ],
        price: [{ required: true, message: "请输入价格", trigger: "blur" }],
        functionShow: [
          { required: true, message: "请输入跳转链接", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getDataList();
  },
  mounted() {
    // 设置操作栏长度
    this.$nextTick(() => {
      this.$store.dispatch("setWidth", this.$refs.operation.children.length);
    });
  },
  methods: {
    // 获取Case列表
    async getDataList() {
      this.loading = true;
      this.dataList = [
        {
          id: 1,
          price: "9999/年",
          versionName: "企业实验室1",
          introduction:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          functionShow:
            "发表文章、技术分享、组队参数、靶场试验、企业人员管理、企业云盘",
          status: 1,
          createTime: "2022-05-30 12:00"
        },
        {
          id: 2,
          price: "9999/年",
          versionName: "企业实验室2",
          introduction:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          functionShow:
            "发表文章、技术分享、组队参数、靶场试验、企业人员管理、企业云盘",
          status: 1,
          createTime: "2022-05-30 12:00"
        },
        {
          id: 3,
          price: "9999/年",
          versionName: "企业实验室3",
          introduction:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          functionShow:
            "发表文章、技术分享、组队参数、靶场试验、企业人员管理、企业云盘",
          status: 1,
          createTime: "2022-05-30 12:00"
        }
      ];
      this.loading = false;
    },

    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.searchParams.limit = newSize;
      this.getDataList();
    },

    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.searchParams.page = newSize;
      this.getDataList();
    },

    // 编辑
    async openEditCase(scope) {
      this.editDialog = true;
      this.editForm = Object.assign({}, scope.row);
    },
    async editCase() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return;
      });
    },
    // 删除
    async deleteCase(ids) {
      var configResult = await this.$confirm(
        "此操作将永久删除该Case图片, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).catch(err => {
        return err;
      });
      if (configResult !== "confirm") {
        return this.$message.info({ message: "已经取消删除", duration: 1000 });
      }
      this.$message.success({ message: "删除成功", duration: 1000 });
      console.log(ids);
      this.getDataList();
    },

    // 表格前勾选框
    handleSelectionChange(val) {
      this.ids = val.map(item => {
        return item.id;
      });
    },

    async addCase() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.content-manage-child {
  padding: 20px;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /deep/ .el-dialog {
    .el-dialog__header {
    }
    .el-dialog__body {
      .el-form {
        .el-form-item {
          .el-form-item__content {
            .company-name {
              width: 200px;
              input {
              }
            }
            .el-textarea {
              //   width: 500px;
            }
          }
        }
      }
    }
  }
}
.addImg {
  .avatar-uploader-icon {
    border: 1px solid balck;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
}
</style>
