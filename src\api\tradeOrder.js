import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}

export function checkTransfer(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/checkTransfer",
    method: "post",
    data:params,
  });
}

export function refundConfirm(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/refund/confirm",
    method: "post",
    data:params,
  });
}

export function getOrderInfo(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/getOrderInfo",
    method: "get",
    params:params,
  });
}

export function refundDenied(params) {
  return request({
    url: process.env.ADMIN_API + "/tradeOrder/refund/denied",
    method: "post",
    data:params,
  });
}
