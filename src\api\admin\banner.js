import request from '@/utils/request'

/**
 * 企业首页
 * @param {*} data
 */
export function getBannerList() {
  return request({
    url: 'portal/bannerList',
    method: 'post'
  })
}

/**
 * 企业首页
 * @param {*} data
 */
export function addBanner(data) {
  return request({
    url: 'adminConfig/setAdminConfig',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 企业首页
 * @param {*} data
 */
export function updateBanner(data) {
  return request({
    url: 'adminConfig/setAdminConfig',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 企业首页
 * @param {*} data
 */
export function delBanner(data) {
  return request({
    url: 'adminConfig/setAdminConfig',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

