<template>
    <div :style="containerStyle" class="showMore-container">
        <div class="inner" :style="{
            height: flagType == 1 ? thresholdHeight - 4 + 'px' : 'auto',
        }">
            <div ref="express-box" class="express-box" :style="{
                width: boxWidth,
            }">
                <slot></slot>
            </div>
            <span :style="[
                {
                    fontSize: '12px',
                    marginTop: '20px',
                    position: 'absolute',
                    right: right,
                    background: '#fff',
                    top: '-14px',
                    color: '#0086fd'
                },
                textStyle
            ]" @click="spread" v-show="flagType != 0">
                {{ flagType == 1 ? '展开' : '收起' }}
                <i style="margin-left: 4px;" :class="`${flagType == 1 ? 'el-icon-caret-bottom' : 'el-icon-caret-top'}`"></i>
            </span>
        </div>
    </div>
</template>
<script>
import { throttle } from "@/utils/commonUtil";
export default {
    props: {
        thresholdHeight: {
            type: Number,
            default: 39
        },
        containerStyle: {
            type: Object,
            default: () => ({})
        },
        textStyle: {
            type: Object,
            default: () => ({})
        },
        right: {
            type: String,
            default: '30px',
        },
        boxWidth: {
            type: String,
            default: '93%',
        }
    },
    data() {
        return {
            timer: null,
            flagType: 0
        }
    },
    beforeDestroy() {
        clearTimeout(this.timer);
        window.removeEventListener('resize', this.init);
    },
    async mounted() {
        await this.$nextTick();
        this.timer = setTimeout(() => {
            this.init();
            this.initReset();
        }, 1200)
    },
    methods: {
        spread() {
            this.flagType = this.flagType == 1 ? 2 : 1;
        },
        initReset() {
            window.addEventListener('resize', this.init);
        },
        async init() {
            throttle(async () => {
                await this.$nextTick();
                let expressBox = this.$refs['express-box'];
                let boxH = expressBox.offsetHeight;
                console.log("boxH", boxH, 'this.thresholdHeight', this.thresholdHeight)
                this.timer = setTimeout(() => {
                    if (boxH > this.thresholdHeight) {
                        // 展示并且为收缩状态
                        this.flagType = 1;
                    } else if (boxH <= this.thresholdHeight) {
                        this.flagType = 0;
                    }
                }, 1200);
            })();
        }
    },
}
</script>
<style lang='scss' scoped>
.showMore-container {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    cursor: pointer;

    .inner {
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .express-box {}

    span {}
}
</style>