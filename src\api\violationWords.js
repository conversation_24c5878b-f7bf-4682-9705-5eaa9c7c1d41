
import request from '@/utils/request'

// 获取违规词条
export function getKeywordList(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/keyword/getPageList',
    method: 'post',
    data: params
  })
}

// 审核违规词条
export function checkKeyword(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/keyword/check',
    method: 'post',
    data: params
  })
}

// 导出白名单txt
export function exportWords(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/keyword/exportWords',
    method: 'post',
    data: params
  })
}

