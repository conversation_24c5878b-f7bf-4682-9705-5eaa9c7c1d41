<template>
  <div class="content-contentManage-child">
    <div class="header">
      <div v-if="isShowSearch" class="top">
        <el-form ref="searchForm" :inline="true" :model="searchForm" label-position="right">
          <el-form-item>
            <el-select
              v-model="searchForm.isOriginal"
              size="small"
              placeholder="请选择文章类型"
              clearable
              @change="getDataList()"
              @clear="getDataList()">
              <el-option
                v-for="item in blogTypeDictList"
                :key="item.uid"
                :label="item.dictLabel"
                :value="item.dictValue" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
              v-model="searchForm.blogSortUid"
              size="small"
              placeholder="请选择文章分类"
              clearable
              @clear="getDataList()">
              <el-option v-for="item in sortOptions" :key="item.uid" :label="item.sortName" :value="item.uid" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="searchForm.author" placeholder="请输入用户昵称" size="small" />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchForm.keywords"
              placeholder="请输入文章标题关键词"
              size="small"
              suffix-icon="el-icon-search"
              clearable
              @change="getDataList()"
              @clear="getDataList()"/>
          </el-form-item>
          <el-form-item>
            <div style="margin-right:10px">
              <el-select
                size="small"
                clearable
                placeholder="是否置顶"
                style="width:140px  ;margin-right:10px"
                v-model="searchForm.level"
                @change="getDataList()"
                @clear="getDataList()"
              >
                <el-option label="是" value=2></el-option>
                <el-option label="否" value=0></el-option>
              </el-select>
            </div>
          </el-form-item>

          <el-form-item style="float:right;" class="flex" label="">
            <el-button type="primary" size="small" icon="el-icon-search" @click="handelSearch">
              搜索
            </el-button>

            <el-button
              v-if="ids.length"
              :disabled="ids.length == 0"
              type="danger"
              icon="el-icon-delete"
              size="small"
              @click="deleteMsg(ids)">
              删除
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="center">
      <el-table
        v-loading="loading"
        id="out-table"
        :data="dataList"
        style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" />
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="imgPath" align="center" label="标题图">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.photoUrl"
              :src="scope.row.photoUrl"
              :preview-src-list="[scope.row.photoUrl]"
              style="width: 80px; height: 80px" />
          </template>
        </el-table-column>
        <el-table-column prop="title" align="center" label="文章标题" />
        <el-table-column prop="author" align="center" width="120px" label="作者" />
        <el-table-column label="文章类型" align="center" width="120px" prop="isOriginal">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isOriginal == 1" type="success">
              原创
            </el-tag>
            <el-tag v-if="scope.row.isOriginal == 0" type="info">转载</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="文章分类" width="120px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.blogSort && scope.row.blogSort.sortName }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column label="操作" align="center" fixed="right">
          <template slot-scope="scope">
            <div ref="operation" style="display:flex;flex-direction:column;">
              <el-button
                v-if="scope.row.status===1"
                :icon="`el-icon-${scope.row.level===2?'bottom':'top'}`"
                :type="scope.row.level===2?'warning':'success'"
                size="small"
                @click="setTop(scope.row.level,scope.row.uid)"
                style="width: 50%;margin: auto;">
                {{ scope.row.level===2?'取消置顶':'置顶' }}
              </el-button>

              <el-button style="width: 50%;margin: 5px auto;" icon="el-icon-view" type="primary" size="small" @click="openEditMsg(scope)">
                查看
              </el-button>
              <!-- <el-button
                v-permission="'/blog/delete'"
                type="danger"
                icon="el-icon-delete"
                size="small"
                @click="deleteMsg(scope.row.uid)"
                style="width: 50%;margin: auto;">
                删除
              </el-button> -->
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 底部分页栏 -->
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        style="float: right; margin-top:10px"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <el-dialog :visible.sync="editDialog" center>
      <span class="content-title">
        <span class="content-title-left">
          {{ editForm.title }}
        </span>
        <el-tag v-if="editForm.isOriginal == 1" effect="dark" type="success">
          原创
        </el-tag>
        <el-tag v-if="editForm.isOriginal == 0" effect="dark" type="info">
          转载
        </el-tag>
      </span>
      <div style="margin-bottom:20px">
        {{
          editForm.releaseTime
            ? `于${editForm.releaseTime}发布`
            : `于${editForm.setReleaseTime}发布`
        }}
      </div>
      <div v-html="editForm.content" />
      <!-- <el-form
        ref="editForm"
        :model="editForm"
        label-position="right"
        label-width="40px"
      >
        <el-form-item label="作者">
          {{ editForm.author }}
        </el-form-item>
        <el-form-item label="内容">
          <div v-html="editForm.blogSort.content"></div>
        </el-form-item>
        <el-form-item label=" ">
          <el-button size="mini" @click="editDialog = false">取 消</el-button>
          <el-button size="mini" @click="editMsg" type="primary">
            确 定
          </el-button>
        </el-form-item>
      </el-form> -->
    </el-dialog>
  </div>
</template>

<script>
import FileSaver from 'file-saver'
import XLSX from 'xlsx'
import {getListByDictTypeList} from '@/api/sysDictData'
import {getBlogSortList} from '@/api/blogSort'
import {adminDelete, adminDeleteBatch, blogSetTop, blogUnTop, getBlogList} from '@/api/blog'

export default {
  data() {
    return {
      isShowSearch: true,
      searchForm: {
        blogSortUid: '',
        isOriginal: '',
        author: '',
        level: "",
        keywords: null
      },
      page: 1,
      limit: 10,
      total: 100,
      sortOptions: [], // 文章分类
      blogTypeDictList: [], // 文章类型
      loading: false,
      dataList: [],
      ids: [],
      editDialog: false,
      editForm: {
        blogSort: {}
      },
      isAddDialog: false,
      editFormRules: []
    }
  },
  watch: {
    // ids(newValue, oldValue) {}
  },
  created() {
    this.getDataList()
    this.blogSortList()
    // 获取字典
    this.getDictList()
  },
  mounted() { },
  methods: {
    // 文章列表
    getDataList() {
      this.loading = true

      var params = {}
      params.keyword = this.searchForm.keywords
      // params.levelKeyword = this.queryParams.levelKeyword;
      // params.isPublish = this.queryParams.publishKeyword;
      // params.isOriginal = this.queryParams.originalKeyword;
      params.level = this.searchForm.level
      params.author = this.searchForm.author
      params.blogSortUid = this.searchForm.blogSortUid
      params.isOriginal = this.searchForm.isOriginal
      params.currentPage = this.page
      params.pageSize = this.limit
      params.reqType = 'ADMIN_CONTENT_REQ'
      getBlogList(params).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.dataList = response.data.records
          // 设置操作栏长度
          this.$nextTick(() => {
            this.$store.dispatch(
              'setWidth',
              this.$refs.operation.children.length
            )
          })
          this.page = response.data.current
          this.limit = response.data.size
          this.total = response.data.total
        }
        this.loading = false
        // console.log(this.dataList);
      })
    },

    // 获取文章分类
    blogSortList() {
      var blogSortParams = {}
      blogSortParams.pageSize = 500
      blogSortParams.currentPage = 1
      getBlogSortList(blogSortParams).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          // this.blogSortData = response.data.records;
          this.sortOptions = response.data.records
        }
      })
    },

    /**
     * 字典查询
     */
    getDictList() {
      var dictTypeList = [
        'sys_recommend_level',
        'sys_original_status',
        'sys_publish_status',
        'sys_normal_disable',
        'sys_blog_type'
      ]

      getListByDictTypeList(dictTypeList).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          var dictMap = response.data
          // this.blogOriginalDictList = dictMap.sys_original_status.list;
          // this.blogPublishDictList = dictMap.sys_publish_status.list;
          // this.blogLevelDictList = dictMap.sys_recommend_level.list;
          // this.openDictList = dictMap.sys_normal_disable.list;
          this.blogTypeDictList = dictMap.sys_blog_type.list

          // if (dictMap.sys_original_status.defaultValue) {
          //   this.blogOriginalDefault = dictMap.sys_original_status.defaultValue;
          // }
          // if (dictMap.sys_publish_status.defaultValue) {
          //   this.blogPublishDefault = dictMap.sys_publish_status.defaultValue;
          // }
          // if (dictMap.sys_recommend_level.defaultValue) {
          //   this.blogLevelDefault = dictMap.sys_recommend_level.defaultValue;
          // }
          // if (dictMap.sys_normal_disable.defaultValue) {
          //   this.openDefault = dictMap.sys_normal_disable.defaultValue;
          // }
          // if (dictMap.sys_blog_type.defaultValue) {
          //   this.blogTypeDefault = dictMap.sys_blog_type.defaultValue;
          // }
        }
      })
    },

    // 搜索
    handelSearch() {
      this.page = 1
      this.getDataList()
    },

    // 表格前勾选框
    handleSelectionChange(val) {
      if (val.length === 1) this.editForm = val[0]
      this.ids = val
    },

    // 编辑
    openEditMsg(scope) {
      this.editForm = Object.assign({}, scope.row)
      this.isAddDialog = false
      this.editDialog = true
    },
    async editMsg() {
      this.editDialog = false
      // this.$refs.editForm.validate(valid => {
      //   if (!valid) return;
      // });
    },

    // 点击新增
    handleOpenAdd() {
      // this.$refs.editForm.resetFields();
      // this.resetForm("editForm");
      this.clearValue(this.editForm)
      this.isAddDialog = true
      this.editDialog = true
    },

    // 删除
    async deleteMsg(ids) {
      var configResult = await this.$confirm(
        '此操作将永久删除该条数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(err => {
        return err
      })
      if (configResult !== 'confirm') {
        return this.$message.info({ message: '已经取消删除', duration: 1000 })
      }
      if (typeof ids === 'string') {
        var params = {}
        params.uid = ids
        adminDelete(params).then(response => {
          this.$commonUtil.message.success(response.message)
          this.getDataList()
        })
      } else {
        adminDeleteBatch(ids).then(response => {
          this.$commonUtil.message.success(response.message)
          this.getDataList()
        })
      }
    },

    // 重置
    resetForm(formName) {
      // console.log(formName);
      this.$refs[formName].resetFields()
    },

    // 清空对象值
    clearValue(obj) {
      Object.keys(obj).forEach(key => {
        // if (typeof obj[key] == "object") {
        //   this.clearValue(obj[key]);
        // } else {
        obj[key] = ''
        // }
      })
    },

    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.limit = newSize
      this.getDataList()
    },

    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.page = newSize
      this.getDataList()
    },

    // 导出Excel表格
    exportExcel() {
      var xlsxParam = { raw: true } // 导出的内容只做解析，不进行格式转换
      // let fix = document.querySelector(".el-table__fixed"); //如果是都给了fixed样式
      const fix = document.querySelector('.el-table__fixed-right') // 如果是只有右边有fixed样式
      let wb
      if (fix) {
        // 判断要导出的节点中是否有fixed的表格，如果有，转换excel时先将该dom移除，然后append回去
        wb = XLSX.utils.table_to_book(
          document.querySelector('#out-table').removeChild(fix),
          xlsxParam
        )
        document.querySelector('#out-table').appendChild(fix)
      } else {
        wb = XLSX.utils.table_to_book(
          document.querySelector('#out-table'),
          xlsxParam
        )
      }
      var wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        FileSaver.saveAs(
          new Blob([wbout], { type: 'application/octet-stream' }),
          'table.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') {
        }
      }
      return wbout
    },
    // 置顶操作
    setTop(level, uid) {
      // v为当前文章的level 根据level判断请求的接口
      const params = { uid: uid };
      (level === 2 ? blogUnTop(params) : blogSetTop(params)).then(res => {
        this.$message.success(level === 2 ? '取消置顶成功' : '已置顶')
        this.getDataList()
      }).catch(() => {
        this.$message.warning('操作失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-contentManage-child {
  padding: 20px;

  /deep/ .header {
    .top {
      .el-form {
        .el-form-item {
          margin-bottom: 0px;

          .el-input {
            width: 160px;
          }
        }
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
    }
  }
}

/deep/ .el-dialog {
  .el-dialog__header {
    .el-dialog__headerbtn {
      top: 10px;
      font-size: 24px;
    }
  }

  .el-dialog__body {
    padding-top: 0;

    .content-title {
      .content-title-left {
        font-size: 24px;
        font-weight: bolder;
      }

      .el-tag {
        margin: 10px 0;
      }
    }
  }
}
</style>
