import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/deleteBatch",
    method: "post",
    data: params
  });
}

export function allSkill() {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/allSkill",
    method: "get"
  });
}

export function addSort(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/addSort",
    method: "post",
    data: params
  });
}

export function reduceSort(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/reduceSort",
    method: "post",
    data: params
  });
}

export function getSkillList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/getList",
    method: "post",
    data: params
  });
}

export function getSkillDetail(params) {
  return request({
    url: process.env.ADMIN_API + "/trainSkill/getInfo",
    method: "get",
    params
  });
}
