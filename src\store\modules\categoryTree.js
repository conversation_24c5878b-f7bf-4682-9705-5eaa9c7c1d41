
export default {
  state: {
    // 题目分类
    categorySubject: [],
    // 试卷分类
    categoryTextPaper: []
  },
  mutations: {
    SET_SUBJECT: (state, data) => {
      state.categorySubject = data
    },
    SET_TEXT_PAPER: (state, data) => {
      state.categoryTextPaper = data
    }
  },
  actions: {
    setSubject({ commit }, data) {
      commit('SET_SUBJECT', data)
    },
    setTextPaper({ commit }, data) {
      commit('SET_TEXT_PAPER', data)
    }
  }
}
