<template>
  <div class="content-manage">
    <customerCase></customerCase>
    <!-- <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="首页Banner管理" name="1">
        <banner></banner>
      </el-tab-pane>
      <el-tab-pane label="服务标签页管理" name="2">
        <serve></serve>
      </el-tab-pane>
      <el-tab-pane label="客户案例管理" name="3">
        <customerCase></customerCase>
      </el-tab-pane>
      <el-tab-pane label="版本推荐管理" name="4">
        <versionRecommend></versionRecommend>
      </el-tab-pane>
      <div class="content-manage-main">
         <banner></banner><serve></serve><customerCase></customerCase><versionRecommend></versionRecommend>
      </div>
    </el-tabs> -->
  </div>
</template>

<script>
import banner from "./components/banner.vue";
import customerCase from "./components/customerCase.vue";
import serve from "./components/serve.vue";
import versionRecommend from "./components/versionRecommend.vue";

export default {
  components: {
    banner,
    customerCase,
    serve,
    versionRecommend,
  },
  data() {
    return {
      activeName: "2",
    };
  },
  methods: {
    handleClick(tab, event) {
      //   console.log(tab, event);
    },
  },
};
</script>
<style lang="scss" scoped>
.content-manage {
  width: 100%;
  padding: 20px;
  /deep/ .el-tabs {
    width: 100%;
  }
}
</style>
