import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/disWithdrawParam/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/disWithdrawParam/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/disWithdrawParam/edit",
    method: "post",
    data: params
  });
}

export function deleteData(params) {
  return request({
    url: process.env.ADMIN_API + "/disWithdrawParam/delete",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/disWithdrawParam/deleteBatch",
    method: "post",
    data: params
  });
}

export function downloadExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/disWithdrawParam/downloadExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
