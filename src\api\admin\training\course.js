import request from "@/utils/request";
//获取全部课程列表
export function allCouseList(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/school/curriculum/getCurriculumList",
    method: "post",
    data: params
  });
}
//批量审核课程
export function batchAuditCourse(params) {
  return request({
    url:
      process.env.ADMIN_API + "/admin/school/curriculum/manualCheckCurriculum",
    method: "post",
    data: params
  });
}
//批量上下架删除
export function batchStatusChange(params) {
  return request({
    url:
      process.env.ADMIN_API +
      "/admin/school/curriculum/setCurriculumStatusBatch",
    method: "post",
    data: params
  });
}
// 课程详情-根据uid查询课程
export function getVideoCourseDetailByUid(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/school/curriculum/getCurriculumByUid",
    method: "get",
    params: params
  });
}
// 课程详情-根据课程uid查询章节
export function getVideoCourseDetailBySection(params) {
  return request({
    url:
      process.env.ADMIN_API +
      "/admin/school/curriculum/chapter/getChapterByCurriculumId",
    method: "get",
    params: params
  });
}
// 课程详情-根据章节uid查询视频
export function getVideoCourseDetailByCourse(params) {
  return request({
    url:
      process.env.ADMIN_API +
      "/admin/school/curriculum/video/getVideoByChapterId",
    method: "get",
    params: params
  });
}
// 新建或修改课程我创建的单品课
export function addMyCurriculum(params = {}) {
  return request({
    url: `${process.env.ADMIN_API}/admin/school/curriculum/addCurriculum`,
    method: "post",
    data: params
  });
}
// 删除我创建的单品课
export function delMyCreateVideoCourse(params = {}) {
  return request({
    url: `${process.env.ADMIN_API}/admin/school/curriculum/deleteCurriculum`,
    method: "post",
    data: params
  });
}
//添加课程下的章节
export function myCurriculumAddChapter(params = {}) {
  return request({
    url: `${process.env.ADMIN_API}/admin/school/curriculum/chapter/addChapter`,
    method: "post",
    data: params
  });
}
//修改课程下的章节
export function updateVideoCourseChapterByUid(params = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/curriculum/chapter/updateChapter`,
    method: "post",
    data: params
  });
}

//删除课程下的章节
export function delVideoCourseChapterByUid(params = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/curriculum/chapter/deleteChapterBatch`,
    method: "post",
    data: params
  });
}

//章节拖拽排序
export function setVideoCourseChapterSort(data = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/curriculum/chapter/exchangeChapterSort`,
    method: "post",
    data
  });
}
//添加章节下的视频
export function myCurriculumAddVideo(params = {}) {
  return request({
    url: `${process.env.ADMIN_API}/admin/school/curriculum/video/addVideo`,
    method: "post",
    data: params
  });
}
//修改章节下的视频(设为免费或者不免费)
export function myCurriculumUpdateVideo(params = {}) {
  return request({
    url: `${process.env.ADMIN_API}/admin/school/curriculum/video/setVideoStatusBatch`,
    method: "post",
    data: params
  });
}
//删除视频
export function myCurriculumDeleteVideo(params = {}) {
  return request({
    url: `${process.env.ADMIN_API}/admin/school/curriculum/video/deleteVideo`,
    method: "post",
    data: params
  });
}
//视频排序
export function myCurriculumSortVideo(data = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/curriculum/video/exchangeVideoSort`,
    method: "post",
    data
  });
}

// 获取课程的资料数据
export function myCurriculumInformation(params = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/profile/pageList`,
    method: "post",
    data: params
  });
}

// 课程的添加资料
export function myCurriculumAddInformation(params = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/profile/add`,
    method: "post",
    data: params
  });
}

// 课程的删除资料
export function myCurriculumDelInformation(params = {}) {
  return request({
    url: `${process.env.ADMIN_API
      }/admin/school/profile/deleteBatch`,
    method: "post",
    data: params
  });
}

/**
 * 保存拓扑
 */
export function addTopoDataAPI(data) {
  return request({
    url: "/topology/cepoSysTopology/addModelCourseTopo",
    method: "POST",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}

/**
 * 启动拓扑
 */
export function startTopoAPI(params) {
  return request({
    url: `topology/cepoSysTopology/startModelCourseTopo/${params}`
  });
}

/**
 * 释放拓扑
 */
export function releaseTopoAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/releaseModelCourseTopoV3/${params}`
  });
}

/**
 * 获取拓扑
 * @param {*} params
 */
export function getTopoDataAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/getModelCourseTopo/${params}`,
    method: "get"
  });
}
