import request from '@/utils/request'

// 查找所有题库分类
export function getCategorySelectAll(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/category/getTree',
    method: 'get',
    params
  })
}

// 添加题库分类
export function addCategory(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/category/add',
    method: 'post',
    data: params
  })
}

// 修改题库分类信息
export function editCategory(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/category/edit',
    method: 'post',
    data: params
  })
}

// 删除题库分类
export function deleteCategory(params) {
  return request({
    url: process.env.ADMIN_API + `/train/title/category/delete/${params.id}`,
    method: 'get'
  })
}
