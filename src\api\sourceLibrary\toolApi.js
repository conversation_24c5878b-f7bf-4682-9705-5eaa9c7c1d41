import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listBase(query) {
  return request({
    url: process.env.ADMIN_API + '/toolBase/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getBase(id) {
  return request({
    url: process.env.ADMIN_API + '/toolBase/getInfo',
    params: {id},
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addBase(data) {
  return request({
    url: process.env.ADMIN_API + '/toolBase/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改【请填写功能名称】
export function updateBase(data) {
  return request({
    url: process.env.ADMIN_API + '/toolBase/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除【请填写功能名称】
export function delBase(ids) {
  return request({
    url: process.env.ADMIN_API + '/toolBase/delete',
    params: {ids:ids.join(',')},
    method: 'get'
  })
}


export function uploadBase() {
  return process.env.ADMIN_API + '/toolBase/uploadFile'
}


export function increaseDownloadCount(data) {
  return request({
    url: process.env.ADMIN_API + '/toolBase/increaseDownloadCount/' + data,
    method: 'get'
  })
}
