import request from "@/utils/request";

// 选择来自体系
export function getSelectColumnSystem(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectColumnSystem",
    method: "get",
    params: params
  });
}
//选择发布栏目
export function getSelectColumn(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectColumn",
    method: "get",
    params: params
  });
}
//选择知识话题
export function getSelectBlogTopic(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectBlogTopic",
    method: "get",
    params: params
  });
}
//后台发布文章
export function addBlog(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/publish",
    method: "post",
    data: params
  });
}
//草稿箱列表
export function getDraftList(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectDraftBlog",
    method: "post",
    data: params
  });
}
//删除文章
export function deleteArticle(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/deleteBlogById",
    method: "get",
    params: params
  });
}
//编辑文章
export function editArticle(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/editDraftBlog",
    method: "post",
    data: params
  });
}
//根据id获取文章详情
export function getBuyIdArticle(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectBlogById",
    method: "get",
    params: params
  });
}
//获取原创文章列表
export function getOriginalList(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectOriginalBlog",
    method: "post",
    data: params
  });
}

//文章待审核编辑
export function editBlogByIdAndType(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/editBlogByIdAndType",
    method: "post",
    data: params
  });
}

// 根据体系id获取系列talk
export function selectColumnSystemTalk(params) {
  return request({
    url: process.env.ADMIN_API + "/admin/blog/selectColumnSystemTalk",
    method: "get",
    params
  });
}