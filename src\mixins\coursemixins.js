// 知识学堂 全部课程，全部套餐mixins
export default {
    data() {
        return {

        }
    },
    computed: {
        hasShowBtn() {
            return this.$store.state.course.currentSubmitType == 'add'
        }
    },
    methods: {
        // 返回
        GotoBack() {
            this.$store.commit('course/setHasIndex', true)
            this.$store.commit('course/setMyCreateCoursePackageCurrentUid', '')
            this.$store.commit('course/setMyCreateNormalCourseCurrentUid', '')
            this.$store.commit('course/setCurrentPackageType', 'add')
        },
        // 找到对象发送变化的属性
        findChangedProperties(oldObject, newObject) {
            const changedProperties = {};
            const keys = Object.keys(newObject);

            for (const key of keys) {
                if (oldObject[key] !== newObject[key]) {
                    changedProperties[key] = newObject[key];
                }
            }
            return changedProperties;
        },
    }
}