import request from '@/utils/request'

// 发布资讯
export function addInformation(params){
    return request({
        url:process.env.ADMIN_API + '/information/addInfo',
        method:"post",
        data:{
            information:params
        },
    })
}

// 获取资讯
export function getInformation(params){
    return request({
        url:process.env.ADMIN_API + '/information/searchInfo',
        method:"post",
        data:params,
    })
}

//置顶资讯
export function updateIsTopInfo(params){
    return request({
        url:process.env.ADMIN_API + '/information/updateIsTopInfo',
        method:"post",
        data:params,
    })
}

//删除资讯
export function deleteInformation(params){
    return request({
        url:process.env.ADMIN_API + '/information/batchDeleteInfo',
        method:"post",
        data:params,
    })
}

//编辑资讯
export function changeInformation(params){
    return request({
        url:process.env.ADMIN_API + '/information/changeInfo',
        method:"post",
        data:{
            information:params
        },
    })
}