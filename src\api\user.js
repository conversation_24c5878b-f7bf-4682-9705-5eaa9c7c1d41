import request from "@/utils/request";

export function getUserList(params) {
  return request({
    url: process.env.ADMIN_API + "/user/getList",
    method: "post",
    data: params
  });
}

export function addUser(params) {
  return request({
    url: process.env.ADMIN_API + "/user/add",
    method: "post",
    data: params
  });
}

export function sendCode(params) {
  return request({
    url: process.env.ADMIN_API + "/auth/sendCode",
    method: "get",
    params
  });
}

export function editUser(params) {
  return request({
    url: process.env.ADMIN_API + "/user/edit",
    method: "post",
    data: params
  });
}

export function deleteUser(params) {
  return request({
    url: process.env.ADMIN_API + "/user/delete",
    method: "post",
    data: params
  });
}

export function unpackUser(params) {
  return request({
    url: process.env.ADMIN_API + "/user/unpackUser",
    method: "post",
    data: params
  });
}

export function resetUserPassword(params) {
  return request({
    url: process.env.ADMIN_API + "/user/resetUserPassword",
    method: "post",
    data: params
  });
}

export function getRegistrationList(params) {
  return request({
    url: process.env.ADMIN_API + "/registration/getList",
    method: "post",
    data: params
  });
}

///user/downloadUserExcel

export function downloadUserExcel(params) {
  return request({
    url: process.env.ADMIN_API + "/user/downloadUserExcel",
    method: "get",
    params,
    responseType: "blob"
  });
}
