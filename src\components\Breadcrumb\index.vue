<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="index">
        <span class="no-redirect cursor" @click="toPage(item)">{{ item.meta.title }}</span>

      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  data () {
    return {
      levelList: null
    }
  },
  computed: {
    ...mapGetters(['categoryList', 'allMenu'])
  },
  watch: {
    $route () {
      this.getBreadcrumb()
    }
  },
  created () {
    this.getBreadcrumb()
  },
  methods: {
    getBreadcrumb () {
      let matched = this.$route.matched.filter(item => item.name)

      // 去除重复路由
      if (matched && matched.length == 2) {
        if (matched[1].meta && matched[1].parent.meta && matched[1].meta.uid == matched[1].parent.meta.uid) {
          matched = [matched[1]]
        }
      }

      // 加载一级菜单
      const title = this.categoryList.find(item => item.dictValue == matched[matched.length - 1].meta.dictValue)
      if (!title) {
        setTimeout(() => { this.getBreadcrumb() }, 300)
        return
      }
      matched = [
        {
          meta: {
            menuType: 0,
            title: title.dictLabel,
            isFirst: true,
            dictValue: matched[matched.length - 1].meta.dictValue
          }
        },
        ...matched]

      // console.log("面包屑对象", matched, this.categoryList, this.allMenu);
      this.levelList = matched
    },
    toPage (item) {
      // console.log('itemtopage', item)
      if (!item.meta || item.meta.menuType != 0) return // 目录直接返回

      if (!item.path && item.meta.isFirst) { // 一级面包屑则跳转当前一级菜单首个子菜单
        const menus = this.allMenu.get(item.meta.dictValue + '')
        const url = this.findFirstMenu(menus)
        if (url) this.$router.push(url)
        return
      } else if (!item.path) {
        return
      }
      
      this.$router.push(item.path)
    },

    // 找出第一个菜单
    findFirstMenu (menus, index = 0) {
      let url
      if (menus[index].isShow == 1) {
        if (menus[index].menuType == 0 && menus[index].children.length == 0) {
          url = menus[index].fullConcatUrlObj.fullConcatUrl
        } else {
          url = this.findFirstMenu(menus[index].children)
        }
      } else if (menus[index + 1]) {
        url = this.findFirstMenu(menus, index + 1)
      }
      return url
    },
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 10px;

  .no-redirect {
    color: #97a8be;
    // cursor: text;
  }
}
</style>
