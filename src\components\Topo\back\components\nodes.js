import { Graph } from '@antv/x6'
import ports from './ports'

import {
  getPhysicalListNew,
  getVirtualList
} from '@/api/topo'

Graph.registerNode(
  'node-image',
  {
    inherit: 'rect',
    width: 54,
    height: 54,
    markup: [
      {
        tagName: 'image'
      },
      {
        tagName: 'text',
        selector: 'label'
      }
    ],
    attrs: {
      image: {
        width: 54,
        height: 54
      }
    },
    ports
  },
  true
)

export const createPhysicalNodes = async(graph) => {
  const ret = await getPhysicalListNew()
  if (ret.code === 0) {
    const nodes = ret.data.map(item =>
      graph.createNode({
        shape: 'node-image',
        label: '',
        attrs: {
          body: {
            stroke: '#fff',
            'fill-opacity': 0
          },
          image: {
            'xlink:href': item.facilityIcon
          },
          label: {
            fontSize: 10,
            refX: '50%',
            refY: '50%',
            textWrap: {
              width: 100
            }
          },
          type: {
            name: '物理',
            type: 1
          },
          title: {
            fill: '#000000',
            fontFamily: 'Arial, helvetica, sans-serif',
            fontSize: 12,
            text: item.facilityName,
            refX: '50%',
            refY: '120%',
            textAnchor: 'middle',
            textVerticalAnchor: 'middle'
          }
        },
        markup: [
          {
            tagName: 'image',
            selector: 'image'
          },
          {
            tagName: 'text',
            selector: 'title'
          },
          {
            tagName: 'text',
            selector: 'label'
          }
        ],
        data: {
          templateId: item.id,
          facilityPhysicsDetailVoList: item.facilityPhysicsDetailVoList,
          num: item.portSize,
          facilityCategory: item.facilityCategory
        }
      })
    )
    return nodes
  }
  this.$message.warning('未查询到虚拟设备数据.')
  return []
}

export const createVirtualNodes = async(graph) => {
  const ret = await getVirtualList()
  if (ret.code === 200) {
    const nodes = ret.rows.map(item =>
      graph.createNode({
        shape: 'node-image',
        label: '',
        attrs: {
          body: {
            stroke: '#fff',
            'fill-opacity': 0
          },
          image: {
            'xlink:href': item.facilityIcon
          },
          label: {
            fontSize: 10,
            refX: '50%',
            refY: '50%',
            textWrap: {
              width: 100
            }
          },
          type: {
            name: '虚拟',
            type: 0
          },
          title: {
            fill: '#000000',
            fontFamily: 'Arial, helvetica, sans-serif',
            fontSize: 12,
            text: item.facilityName,
            refX: '50%',
            refY: '120%',
            textAnchor: 'middle',
            textVerticalAnchor: 'middle'
          }
        },
        markup: [
          {
            tagName: 'image',
            selector: 'image'
          },
          {
            tagName: 'text',
            selector: 'title'
          },
          {
            tagName: 'text',
            selector: 'label'
          }
        ],
        data: {
          templateId: item.id,
          facilityCategory: '',
          sourceId: item.sourceId,
          facilityCategoryId: item.facilityCategoryId
        }
      })
    )
    return nodes
  }
  this.$message.warning('未查询到虚拟设备数据.')
  return []
}
