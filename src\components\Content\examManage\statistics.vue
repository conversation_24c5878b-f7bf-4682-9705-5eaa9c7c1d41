<template>
    <div class="statistics">
        <el-page-header style="margin-bottom: 12px;" content="返回上一层级" @back="goBack"/>
        <span class="title">考试概况</span>
        <el-popover width="300" placement="bottom" title="" trigger="hover" content="">
            <i slot="reference" class="el-icon-warning"></i>
            <p>参与人数：参与答题的人数,同个学员多次参与计为一次</p>
            <p>提交人数：参与答题并提交考试的人数，同个学员多次提交计为一次</p>
            <p>批阅试卷份数：已提交并需老师批阅的试卷数，学员多次提交计为多份</p>
        </el-popover>
        <!-- 考试概况信息 -->
        <div class="exam-info">
            <div class="left">
                <el-descriptions class="descriptions" :column="2" title="">
                    <el-descriptions-item label="考试名称">{{ examOverviewData.examName }}</el-descriptions-item>
                    <el-descriptions-item label="课程学员">--</el-descriptions-item>
                    <el-descriptions-item label="总分">{{ examOverviewData.totalScore ? examOverviewData.totalScore : '--'
                    }}分</el-descriptions-item>
                    <el-descriptions-item label="平均分">{{ examOverviewData.average && examOverviewData.average.toFixed(2) }}分</el-descriptions-item>
                    <el-descriptions-item label="时长">{{ examOverviewData.duration }}分钟</el-descriptions-item>
                    <el-descriptions-item label="最高分">{{ examOverviewData.highestScore }}分</el-descriptions-item>
                    <el-descriptions-item label="次数">{{ (examOverviewData.frequency == -1 ? '不限' :
                            examOverviewData.frequency)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="最低分">{{ examOverviewData.minimumScore }}分</el-descriptions-item>
                    <el-descriptions-item label="通过分数">{{ examOverviewData.passScore ? examOverviewData.passScore+'分' : '不设置'  }}</el-descriptions-item>
                    <el-descriptions-item label="关联课程">--</el-descriptions-item>
                </el-descriptions>
            </div>
            <div class="right">
                <div class="item">
                    <div class="submit-info">
                        <span class="num">{{ examOverviewData.notSubmitted ? examOverviewData.notSubmitted : 0
                        }}</span>
                        <span>人</span>
                    </div>
                    <div class="desc">
                        <span>未提交</span>
                    </div>
                </div>
                <div class="item">
                    <div class="submit-info">
                        <span class="num">{{ examOverviewData.submitted ? examOverviewData.submitted : 0 }}</span>
                        <span>人</span>
                    </div>
                    <div class="desc">
                        <span>已提交</span>
                    </div>
                </div>
                <div class="item">
                    <div class="submit-info">
                        <span class="num">{{ examOverviewData.uncorrected ? examOverviewData.uncorrected : 0 }}</span>
                        <span>份</span>
                    </div>
                    <div class="desc">
                        <span>未批改</span>
                    </div>
                </div>
                <div class="item">
                    <div class="submit-info">
                        <span class="num">{{ examOverviewData.corrected ? examOverviewData.corrected : 0 }}</span>
                        <span>份</span>
                    </div>
                    <div class="desc">
                        <span>已批改</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- tab导航栏 -->
        <div class="tab">
            <el-tabs v-model="tabActive" @tab-click="statisticsTabClick">
                <el-tab-pane label="分数段统计" name="1">
                    <!-- 图表展示 -->
                    <div id="echar-contain"></div>
                </el-tab-pane>
                <el-tab-pane label="客观题分析" name="2">
                    <el-collapse accordion>
                        <el-collapse-item v-for="(item, index) in objectiveQuestionAnalysisData" :key="item.uid">
                            <template slot="title">
                                <div class="collapse-title">
                                    <div class="left">
                                        <h4>第{{ (index + 1) }}题</h4>
                                    </div>
                                    <div class="right">
                                        <span>正确率</span>
                                        <el-progress :width="42" type="circle"
                                            :percentage="item.accuracy ? item.accuracy : 0"></el-progress>
                                    </div>
                                </div>
                            </template>
                            <div class="tab-select">
                                <div class="title-head">
                                    <span style="vertical-align: top;">（{{ item.type | topicType }}）</span><div style="display: inline-block;white-space: pre-wrap;" v-html="item.title"></div>（）
                                </div>
                                <!-- 选择题 -->
                                <template v-if="item.type == 0 || item.type == 1 || item.type ==2">
                                    <div class="exam-options" v-for="(answerItem, index) in item.answerList" :key="answerItem.uid">
                                        <p>{{ index | optionPrefix }}.</p>
                                        <div>
                                            <span>{{ answerItem.answer }}</span>
                                        </div>
                                        <span class="process">{{ answerItem.selectionRate ? answerItem.selectionRate:0 }}%{{ (answerItem.hasAnswer == 1 ?
                                        '（答案）' : '') }}</span>
                                    </div>
                                </template>
                                <!-- 判断题 -->
                                <template v-if="item.type==3">
                                    <el-descriptions title="" :column="1">
                                        <el-descriptions-item label="答案">{{ item.hasAnswer==0?'错误':'正确'}}</el-descriptions-item>
                                    </el-descriptions>
                                </template>
                                <div>
                                    <span class="parse">解析：</span>
                                    <p class="parse-analysis" v-html="item.analysis"></p>
                                </div>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </el-tab-pane>
                <el-tab-pane label="成绩排名" name="3">
                    <div class="search">
                        <el-button type="primary" class="export-score" @click="exportScore">导出成绩单</el-button>
                        <el-input placeholder="请输入内容" v-model="scoreRankSearchForm.keyword" class="input-with-select">
                            <el-select v-model="scoreRankSearchForm.type" slot="prepend" placeholder="请选择">
                                <el-option label="昵称" :value="1"></el-option>
                                <el-option label="真实姓名" :value="2"></el-option>
                                <el-option label="手机号" :value="3"></el-option>
                            </el-select>
                            <el-button @click="scoreRankSearch" slot="append" icon="el-icon-search"></el-button>
                        </el-input>
                    </div>
                    <!-- 表格 -->
                    <el-table class="rankingTable" :data="rankingTableData" :cell-style="{ textAlign: 'center' }"
                        :header-cell-style="{ textAlign: 'center' }" style="width: 100%">
                        <el-table-column prop="date" label="排名" width="180">
                            <template slot-scope="scope">
                                <span>{{(pageSize*(page-1)+scope.$index+1)}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="userName" label="真实姓名" width="180">
                        </el-table-column>
                        <el-table-column prop="nickName" label="昵称" width="180">
                        </el-table-column>
                        <el-table-column prop="finalScore" label="得分">
                        </el-table-column>
                        <el-table-column prop="isPass" label="考试结果">
                        </el-table-column>
                        <el-table-column prop="spendTime" label="用时">
                            <template slot-scope="scope">
                                <span>{{ $commonUtil.formatSeconds(scope.row.spendTime) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="startTime" label="开始考试时间">
                        </el-table-column>
                        <el-table-column prop="commitTime" label="交卷时间">
                        </el-table-column>
                        <el-table-column prop="objectiveScore" label="客观题分">
                        </el-table-column>
                        <el-table-column prop="subjectiveScore" label="主观题">
                        </el-table-column>
                        <el-table-column prop="address" label="操作">
                            <template slot-scope="scope">
                                <ul class="operation">
                                    <li @click="exportExam(scope.row)">导出试卷</li>
                                </ul>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页 -->
                    <div class="pagination">
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page="page" :page-sizes="[10, 20, 30, 40]" :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="total">
                        </el-pagination>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script>
import { examOverview, objectiveQuestionAnalysis, scoreRanking, outputPaper, exportTranscript } from '@/api/content/exam'
import { throttle, debounce } from '@/utils/commonUtil'
import { mapGetters } from 'vuex'
import echarts from 'echarts'
export default {
    inject: ['examParentThis'],
    computed: {
        sidebar() {
            return this.$store.state.app.sidebar;
        },
        ...mapGetters(['examInfo'])
    },
    filters: {
        topicType(val) {
            switch (val) {
                case 0:
                    return '单选题';
                case 1:
                    return '多选题';
                case 2:
                    return '不定项选择题';
                case 3:
                    return '判断题';
                case 4:
                    return '填空题';
                case 5:
                    return '单问答题';
                case 6:
                    return '材料题';
            }
        },
        optionPrefix(val) {
            let optionsLabelMaps = new Map();
            for (var i = 0; i < 26; i++) {
                optionsLabelMaps.set(i, String.fromCharCode(65 + i));
            }
            return optionsLabelMaps.get(val);
        }
    },
    destroyed() {
        window.removeEventListener('resize', this.echartsSelfAdaption);
    },
    mounted() {

    },
    data() {
        return {
            scoreRankSearchForm: {
                type: 1,
                keyword:''
            },
            echartsOption: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,

                },
                xAxis: [
                    {
                        type: 'category',
                        name: '分数',
                        data: ['0-20分', '20-40分', '40-60分', '60-80分', '80-100分'],
                        axisTick: {
                            alignWithLabel: true
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '人数'
                    }
                ],
                series: [
                    {
                        color: '#1890ff',
                        name: '人数',
                        type: 'bar',
                        barWidth: '60%',
                        data: []
                    }
                ]
            },
            examOverviewData: {},
            objectiveQuestionAnalysisData: [],
            tabActive: '1',
            rankingTableData: [],
            page: 1,
            pageSize: 10,
            total: 0,
            chartInstance:null
        }
    },
    async mounted() {
        this.initScoreRanking();
        this.initObjectiveQuestionAnalysis();
        await this.initExamOverview();
        // 加载图标
        this.initEchart();
    },
    methods: {
        goBack() {
          this.examParentThis.flag = 2;
        },
        // a标签下载
        linkDownload(blob, fileName = "文件名", suffix='.xls') {
            let url = window.URL.createObjectURL(
                new Blob([blob], {
                    type: "application/vnd.ms-excel"
                })
            );
            //创建隐藏的a标签
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            //设置导出文件的名字
            link.setAttribute("download", `${fileName+suffix}`);
            document.body.appendChild(link);
            //模拟点击事件
            link.click();
            //导出成功后删除这个标签并释放blob对象
            link.remove();
            window.URL.revokeObjectURL(url); //释放掉blob对象
        },
        // 导出成绩单
        exportScore() {
            let params = {
                uid: this.examInfo.uid,
            }
            // 防抖
            debounce(async () => {
                let result = await exportTranscript(params);
                console.warn(result)
                this.linkDownload(result.data, `${this.examInfo.examName}成绩单`);
            }, 1000)();
        },
        // 导出试卷
        exportExam(data) {
            let params = {
                examRecordUid: data.uid,
            }
            // 防抖
            debounce(async () => {
                let result = await outputPaper(params);
                this.linkDownload(result.data, "试卷",'.docx');
            }, 1000)();
        },
        scoreRankSearch() {
            this.page = 1;
            this.initScoreRanking();
        },
        // 加载成绩排名
        async initScoreRanking() {
            let params = {
                trainExamUid: this.examInfo.uid,
                currentPage: this.page,
                pageSize: this.pageSize,
                ...this.scoreRankSearchForm
            }
            let result = await scoreRanking(params);
            if (result.code == this.$ECode.SUCCESS) {
                this.rankingTableData = result.data.records;
                this.total = result.data.total;
            }
        },
        // 客观分析题
        async initObjectiveQuestionAnalysis() {
            let params = {
                examUid: this.examInfo.uid
            }
            let result = await objectiveQuestionAnalysis(params);
            if (result.code == this.$ECode.SUCCESS) {
                this.objectiveQuestionAnalysisData = result.data;
            }
        },
        // 前端筛选分数段
        echartsDataFilter(arr) {
            let map = new Map([["0-20", 0], ["20-40", 0], ["40-60", 0], ["60-80", 0], ["80-100", 0]]);
            let scoreArr = Array.from(map.keys());
            arr.forEach(Dataitem => {
                scoreArr.forEach(item => {
                    let [start, end] = item.split('-');
                    if (Dataitem > start && Dataitem <= end) {
                        map.set(item, map.get(item) + 1);
                    }
                })
            });
            return scoreArr.map(item => {
                return map.get(item);
            });
        },
        // 加载考试概况
        async initExamOverview() {
            let params = {
                examUid: this.examInfo.uid
            }
            let result = await examOverview(params);
            if (result.code == this.$ECode.SUCCESS) {
                this.examOverviewData = result.data;
                let seriesData = this.echartsDataFilter(result.data.scoreList);
                this.echartsOption.series[0].data = seriesData;
            }
        },
        // echarts图表自适应
        echartsSelfAdaption() {
            // 节流
            throttle(()=> {
                this.chartInstance.resize();
            }, 50)();
        },
        // 初始化图表
        initEchart() {
            var chartDom = document.getElementById('echar-contain');
            this.chartInstance = echarts.init(chartDom);
            this.chartInstance.setOption(this.echartsOption);
            // echarts自适应
            window.addEventListener('resize', this.echartsSelfAdaption);
        },
        // tab切换触发
        statisticsTabClick(val) {

        },
        handleCurrentChange(val) {
            this.page = val;
            this.initScoreRanking();
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.initScoreRanking();
        },
    }
}
</script>
<style lang="scss" scoped>
.statistics {
    padding: 0px 0px 80px 0px;

    .title {
        font-size: 17px;
    }

    .tab {
        .rankingTable{
            .operation {
                padding: 0;
                margin: 0 auto;
                list-style: none;
                display: flex;
                width: max-content;
                .el-popover__reference-wrapper .el-popover__reference::after{
                    content: "";
                    height: 14px;
                    border-right: 1px solid #ebe8e8;
                    right: 0;
                    position: absolute;
                }
                li {
                    color: #2a75ed;
                    cursor: pointer;
                    float: left;
                    padding: 0 15px;
                    display: flex;
                    align-items: center;
                    position: relative;
                    justify-content: center;

                    &::after{
                        content: "";
                        height: 14px;
                        border-right: 1px solid #ebe8e8;
                        right: 0;
                        position: absolute;
                    }
                    &:last-child::after {
                        border: none;
                    }
                }
                .el-dropdown {
                    li::after {
                        border: none;
                    }
                }
            }
        }
        .search{
            width: 100%;
            display: flex;
            justify-content: space-between;
            .export-score{
                height: 40px;
            }
            .input-with-select {
                margin: 0 0 20px 0;
                width: 380px;
                .el-select {
                    width: 102px;
                }
            }
        }
        #echar-contain {
            width: 100%;
            min-height: 320px;
        }

        .tab-select {
            font-size: 14px;
            /deep/ p{
                display: inline-block;
                margin: 0;
            }
            .title-head{
                /deep/ img{
                    width: 300px;
                }
            }
            .parse-analysis{
                white-space: pre-wrap;
                /deep/ img{
                    width: 300px;
                }
            }
            .parse {
                color: #B2B2B2;
            }

            .exam-options {
                display: flex;

                p {
                    padding: 0;
                    margin: 0;
                }

                .process {
                    color: #B2B2B2;
                    margin-left: 20px;
                }

                div {
                    max-width: 380px;
                }
            }
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .collapse-title {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .right {
                display: flex;
                align-items: center;
                width: 120px;

                span {
                    margin-right: 10px;
                    color: #2fce63;
                }
            }
        }
    }

    .exam-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        margin-top: 20px;
        min-height: 200px;
        background: #f5f7fa;

        .left {
            width: 460px;
        }

        .right {
            width: 500px;
            justify-content: space-around;
            display: flex;
            align-items: center;

            .item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .desc {
                    font-size: 13px;
                }

                .submit-info {
                    span {
                        font-size: 13px;
                    }

                    .num {
                        font-size: 50px;
                    }
                }

                &:nth-child(1) {
                    .submit-info {
                        color: #fb6161;
                    }
                }

                &:nth-child(2) {
                    .submit-info {
                        color: #2fce63;
                    }
                }

                &:nth-child(3) {
                    .submit-info {
                        color: #495060;
                    }
                }

                &:nth-child(4) {
                    .submit-info {
                        color: #495060;
                    }
                }
            }
        }

        .descriptions {
            /deep/ .el-descriptions__body {
                background: transparent;
            }
        }
    }

    .el-icon-warning {
        color: #b2b2b2;
    }

    .bottom-back {
        transition: all .5s;
        z-index: 99;
        width: calc(100% - 180px);
        right: 0;
        position: fixed;
        bottom: 0;
        background: white;
        box-shadow: 0 0 10px rgb(209, 209, 209);
        align-items: center;
        display: flex;
        justify-content: start;
        padding-left: 20px;
        height: 60px;
    }
    /deep/.el-page-header__content {
        font-size: 14px;
    }
}
</style>