export default {
    methods: {
        maskPhone(data, start, end, mask, filed = 'phone') {
            let splitFields = filed.split('|');
            let reg = /^(\d{3})\d{4}(\d{4})$/;
            // 脱敏值
            if (mask) {
                for (let i = 0; i < splitFields.length; i++) { 
                    if (data[splitFields[i]]) {
                        return data[splitFields[i]].replace(reg, `$1${'*'.repeat(end - start)}$2`);
                    }
                }
            }
            // 原始值
            for (let i = 0; i < splitFields.length; i++) {
                if (data[splitFields[i]]) {
                    return data[splitFields[i]] || '--';
                }
            }
            return '--';
        }
    }
}