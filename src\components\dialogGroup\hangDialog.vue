<template>
  <div class="dialog-confi">
    <el-dialog
      append-to-body
      v-if="dialogFlag"
      :title="title"
      :visible.sync="dialogFlag"
      :before-close="_beforeClose"
      center
      width="40%"
    >
      <!-- 内容区域 -->
      <div class="dialog_content">
        <p>确定挂起下列共{{ virtualList.length }}个云主机</p>
        <div v-if="virtualList.length" class="disVirtualList">
          <div v-for="(item, index) in virtualList" :key="index" class="disVirtual_content">
            <svg-icon icon-class="computer" class-name="card-panel-icon" />
            {{ item.facilityName }}
            <svg-icon icon-class="true" class-name="card-panel-icon status_class" />
          </div>
        </div>
        <div class="dialog-footer" style="text-align:center;margin-top:40px">
          <el-button @click="cancal">取 消</el-button>
          <el-button :disabled="disFlag" type="primary" style="margin-left:120px" @click="sure">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { suspend } from '@/api/sourceLibrary/virtualApi'
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      dialogFlag: true,
      disFlag: false
    }
  },
  created() {
    this.virtualList = this.list
  },
  methods: {
    cancal() {
      this.dialogFlag = false
      this.$emit('cancelDialog', true)
    },
    sure() {
      this.disFlag = true
      const list = []
      this.virtualList.forEach(item => {
        const flag = suspend(item.id)
          .then((res) => {
            res.name = '挂起' + item.facilityName + '， '
            // console.log('item', item);
            this.$parent.msgList = res
            this.$emit('msgShow')
            return res
            // if (res.code == 200) {

            //   this.$message.success(res.msg)
            // } else {
            //   this.$message.error(res.msg)
            // }
          })
          .catch((err) => {
            console.log(err)
          })
        list.push(flag)
      })
      Promise.all(list).then(res => {
        // console.log('result', result);
        // this.$message.success(res[0].msg)
        this.disFlag = false
        this.$emit('updateList')
        this.cancal()
      })
      // for (let item of this.virtualList) {
      //   suspend(item.id)
      //     .then((res) => {
      //       if (res.code == 200) {
      //         this.$message.success(res.msg)
      //       } else {
      //         this.$message.error(res.msg)
      //       }
      //     })
      //     .catch((err) => {
      //       console.log(err)
      //     })
      // }
      // setTimeout(() => {
      //   this.$emit('updateList')
      //   this.cancal()
      // }, 3000)
    },
    _beforeClose() {
      this.$emit('cancelDialog', true)
    }
  }
}
</script>
<style lang="scss"  scoped>
.dialog-confi ::v-deep {
  .el-dialog__body {
    padding: 0 0 50px 30px;
    height: 80%;
  }
}
.disVirtual_content {
  width: 32.3%;
  height: 40px;
  line-height: 40px;
  padding: 0 5px;
  margin: 0 1% 5px 0;
  box-sizing: border-box;
  background: #f5f5f5;
  position: relative;
}
</style>
