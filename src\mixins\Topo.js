export default {
  data() {
    return {
      topoOptions: {
        data: null, // 拓扑数据
        status: 0, // 0未启动 2启动中 1启动完成
        readonly: false, // 只读
        editable: true, // 是否可编辑,
        scene: false // 是否可启动 关闭
      },
      topoAttrs: null,
      topoStatusTimer: null
    }
  },
  created() {
    this.getTopoData()
  },
  methods: {
    getTopoData() {
      // 获取拓扑
      if (!this.businessId) return
      this.getTopoDataAPI(this.businessId).then((ret) => {
        if (ret.code !== 0) {
          this.$message.error('获取拓扑数据失败.')
          return
        }
        if (ret.data.jsonData) {
          this.topoOptions.data = ret.data.jsonData.map(el => JSON.parse(el))
          this.topoAttrs = ret.data.topology
          const status = Number(this.topoAttrs.openEnv)
          this.topoOptions.status = status
          // 释放中
          if (status === 3) {
            this.topoStopStatus()
          }
          // 启动中
          if (status === 2) {
            this.topoStartStatus()
          }
        }
      })
    },
    // 启动拓扑
    startTopo() {
      this.topoOptions.status = 2
      this.startTopoAPI(this.businessId).then(async (ret) => {
        if (ret.code === 0) {
          this.topoStatusTimer = setInterval(() => {
            this.topoStartStatus()
          }, 5000)
          return
        }
        this.topoOptions.status = 0
        this.$message.error('启动失败.')
      }).catch(() => {
        this.topoOptions.status = 0
        this.$message.error('启动失败.')
      })
    },
    // 释放拓扑
    releaseTopo() {
      this.topoOptions.status = 3
      this.releaseTopoAPI(this.businessId).then((ret) => {
        if (ret.code === 0) {
          this.topoStatusTimer = setInterval(() => {
            this.topoStopStatus()
          }, 5000)
          return
        }
        this.$message.error('释放失败.')
        this.topoOptions.status = 1
      }).catch(() => {
        this.topoOptions.status = 1
      })
    },
    // 保存
    saveTopo(cells, topoImage) {
      let businessId = ''
      let groupCode = ''
      if (typeof this.businessId == 'object') {
        businessId = this.businessId.examCode
        groupCode = this.businessId.groupCode
      } else {
        businessId = this.businessId
      }
      this.addTopoDataAPI({
        jsonData: cells.map(el => JSON.stringify(el)),
        businessId: businessId,
        topoImage: topoImage,
        groupCode: groupCode
      }).then((ret) => {
        if (ret.code === 0) {
          this.$message.success('保存成功.')
          this.getTopoData()
          return
        }
        this.$message.error('保存失败.')
      })
    },
    // vnc登录
    vncLogin(templateId) {
      this.vncLoginAPI(templateId, this.topoAttrs.id).then((ret) => {
        if (ret.code !== 0) {
          return
        }
        window.open(`https://192.168.52.40${ret.data.data.url}`)
      })
    },
    // 拓扑启动状态
    topoStartStatus() {
      this.getTopoStartStatusAPI(this.topoAttrs.id).then((ret) => {
        if (ret.code != 0) {
          return
        }
        if (ret.data == 1) {
          this.topoOptions.status = 1
          clearInterval(this.topoStatusTimer)
          return
        }
        if (ret.data == 2) {
          this.$message.error('启动失败.')
          this.topoOptions.status = 0
          clearInterval(this.topoStatusTimer)
          return
        }
      }).catch(() => {
        this.$message.error('启动失败.')
        this.topoOptions.status = 0
      }).finally(() => {
        this.loading = false
      })
    },
    // 拓扑释放状态
    topoStopStatus() {
      this.getTopoStopStatusAPI(this.topoAttrs.id).then((ret) => {
        if (ret.data == 1) {
          this.topoOptions.status = 0
          clearInterval(this.topoStatusTimer)
          return
        }
        if (ret.code != 0) {
          this.$message.error('获取拓扑状态失败.')
        }
      }).catch(() => {
        this.$message.error('释放失败.')
        this.topoOptions.status = 1
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
