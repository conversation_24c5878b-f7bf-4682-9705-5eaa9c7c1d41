import request from '@/utils/request'

// 查询公告列表
export function listNotice(query) {
  return request({
    url: '/admin/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告列表
export function pageNotice(query) {
  return request({
    url: '/admin/notice/pageList',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: '/admin/notice/getInfo/' + noticeId,
    method: 'get'
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: '/admin/notice',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: '/admin/notice',
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除公告
export function delNotice(noticeId) {
  return request({
    url: '/admin/notice/' + noticeId,
    method: 'delete'
  })
}
