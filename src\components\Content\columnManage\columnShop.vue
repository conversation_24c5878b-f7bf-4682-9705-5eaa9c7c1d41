<template>
  <div class="column-shop-component">
    <div class="column-shuo-content">
      <el-page-header class="page-header" content="返回课程列表" @back="goBack"/>
      <div class="column-info">
        <div class="left">
          <div class="cover">
            <img :src="columnInfo.coverUrl" alt="">
          </div>
          <div class="left-desc">
            <div class="title">
              <span>{{ columnInfo.title }}</span>
            </div>
            <div class="bottom-info">
              <span class="type">指定学员</span>
              <span class="time">截至2023-11-30 00:00:00</span>
              <div class="putaway-status">
                <div :class="[columnInfo.status == 1?'dot-up':'dot-down']" class="dot"/>
                <span>{{ columnInfo.status == 1 ? '已发布' : '未上架' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="right" @click="share">
          <i class="el-icon-share"/>
          <span>分享</span>
        </div>
      </div>
      <el-tabs v-model="columnShopTabActive" @tab-click="columnShopClick">
        <el-tab-pane label="目录管理" name="dir-management">
          <!-- 训练营搜索头部 -->
          <div class="head">
            <div class="search-btn">
              <el-button type="primary" @click="addShop">添加课程</el-button>
              <el-button v-if="selectDatas.length" type="danger" @click="batchDel">删除</el-button>
            </div>
            <div class="search">
              <el-select v-model="columnShopSearchForm.shopType" placeholder="请选择" @change="ininHomeShopList">
                <el-option :value="null" label="商品类型"/>
                <el-option v-for="item,index in shopTypeArr" :key="index" :label="item" :value="index"/>
              </el-select>
              <el-select v-model="columnShopSearchForm.putawayStatus" style="margin:0 7px;" placeholder="请选择" @change="ininHomeShopList">
                <el-option :value="null" label="全部状态"/>
                <el-option v-for="item,index in statusArr" :key="index" :label="item" :value="index"/>
              </el-select>
              <el-input v-model="columnShopSearchForm.shopName" placeholder="请输入商品名称">
                <template slot="prepend">
                  <i class="el-icon-search"/>
                </template>
              </el-input>
              <el-button class="search-btn" @click="ininHomeShopList">搜索</el-button>
            </div>
          </div>
          <p>课程内单品{{ total }}条</p>
          <!-- 训练营表格 -->
          <div class="column-camp-table-box">
            <el-table
              ref="videoClassTable"
              :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
              :cell-style="{ textAlign: 'center' }"
              :data="columnShopHomeList"
              class="column-shop-table"
              style="width: 100%"
              @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55"/>
              <el-table-column prop="sort" align="center" label="排序" width="60" />
              <el-table-column prop="title" label="商品封面">
                <template slot-scope="scope">
                  <el-image :preview-src-list="[scope.row.coverUrl]" :src="scope.row.coverUrl" style="width: 100px; height: 100px" fit="fill"/>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="单品名称"/>
              <el-table-column prop="viewCount" label="播放次数"/>
              <!-- <el-table-column prop="campPeriodNumber" label="类型">
                            </el-table-column> -->
              <el-table-column prop="upStatus" label="上架状态">
                <template slot-scope="scope">
                  <p>{{ scope.row.upStatus==0?'已发布':'已下架' }}</p>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <p>{{ scope.row.status==1?'启用':'已删除' }}</p>
                </template>
              </el-table-column>
              <el-table-column prop="upTime" label="上架时间"/>
              <el-table-column prop="address" label="操作">
                <template slot-scope="scope">
                  <ul class="operation">
                    <li @click="share">分享</li>
                  </ul>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 添加课程弹窗 -->
    <el-dialog
      :visible.sync="columnShopDialogVisible"
      title="添加课程"
      width="40%"
      center
      @closed="columnShopDialogClose">
      <el-form :inline="true" :model="columnShopDialogSearchForm" size="mini" class="demo-form-inline">
        <el-form-item label="" label-width="0">
          <el-input v-model="columnShopDialogSearchForm.keyword" placeholder="请输入商品名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchShop">查询</el-button>
        </el-form-item>
      </el-form>
      <el-tabs v-model="columnShopDialogTabActive" @tab-click="columnShopDialogTabClick">
        <el-tab-pane label="视频" name="2">
          <el-table
            v-loading="columnShopDialogSearchForm.loading"
            v-loadMore="scrollLoad"
            :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
            :cell-style="{ textAlign: 'center' }"
            :data="columnShopDialogList"
            tooltip-effect="dark"
            class="column-shop-table"
            height="300"
            style="width: 100%"
            @selection-change="videoSelectionChange">
            <el-table-column type="selection" width="55"/>
            <el-table-column align="center" prop="videoUid" label="视频ID" show-overflow-tooltip width="180"/>
            <el-table-column align="center" prop="title" label="标题"/>
            <!--                      <el-table-column prop="title" label="视频封面">-->
            <!--                        <template slot-scope="scope">-->
            <!--                          <el-image :preview-src-list="[scope.row.coverUrl]" style="width: 100px; height: 100px" :src="scope.row.coverUrl" fit="fill">-->
            <!--                          </el-image>-->
            <!--                        </template>-->
            <!--                      </el-table-column>-->
            <el-table-column align="center" prop="fileUrl" label="视频封面" width="180">
              <template slot-scope="scope">
                <el-image
                  :preview-src-list="[scope.row.fileUrl]"
                  :src="scope.row.fileUrl"
                  style="width: 100px; height: 100px"
                  fit="fill"/>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="columnShopDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="realAddShop">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 分享弹窗 -->
    <template>
      <el-dialog :visible.sync="shareDialogVisible" class="el-qr-dialog" title="二维码" width="22%" center>
        <div class="qr-code"/>
      </el-dialog>
    </template>
  </div>
</template>
<script>
import Sortable from 'sortablejs'
import { mapGetters } from 'vuex'
import { addGoods, getGoodsPageList, cancelRelation, updateGoodsSortBatch } from '@/api/content/column'
import { videoList } from '@/api/content/video'

export default {
  inject: ['columnManagementThis'],
  data() {
    return {
      selectDatas: [],
      shareDialogVisible: false,
      shopTypeArr: ['图文', '音频', '视频', '直播', '电子书', 'AI互动课'],
      statusArr: ['已下架', '已发布', '待上架'],
      total: 0,
      page: 1,
      pageSize: 1000,
      columnShopDialogTabActive: '2',
      columnShopTabActive: 'dir-management',
      columnShopDialogVisible: false,
      columnShopSearchForm: {
        putawayStatus: null,
        shopType: null,
        shopName: ''
      },
      columnShopHomeList: [],
      columnShopDialogList: [],
      columnShopDialogSearchForm: {
        loading: false,
        total: 0,
        page: 1,
        pageSize: 5,
        keyword: ''
      },
      columnAddShopForm: {
        contentList: []
      }
    }
  },
  computed: {
    ...mapGetters(['columnInfo'])
  },
  mounted() {
    this.ininHomeShopList()
    this.$nextTick(() => {
      this.initSortTable()
    })
  },
  methods: {
    initSortTable() {
      // 表格拖拽排序方法
      // 获取 el-table
      const tableTag = this.$refs['videoClassTable'].$el
      // 获取 tbody 节点
      const elTbody = tableTag.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      new Sortable(elTbody, {
        animation: 150, // 拖拽时的动画速度，单位为毫秒
        handle: '.el-table__row', // 拖拽的手柄元素，这里使用表格的行作为手柄
        onEnd: (evt) => {
          this.dragSort(evt)// 拖拽结束时的回调函数
        }
      })
    },
    async dragSort(evt) {
      // 前端排序数据处理
      const movedItem = this.columnShopHomeList.splice(evt.oldIndex, 1)[0]
      this.columnShopHomeList.splice(evt.newIndex, 0, movedItem)
      this.$forceUpdate() // 强制更新组件
      const sortedShopHomeList = []
      this.columnShopHomeList.forEach((item, index) => {
        const obj = {}
        obj.sort = index + 1
        obj.uid = item.uid
        sortedShopHomeList.push(obj)
      })
      const res = await updateGoodsSortBatch(sortedShopHomeList)
      if (res.code === 200) {
        this.$message.success('排序成功')
      }
      this.ininHomeShopList()
      // 调用后端接口
    },

    handleSelectionChange(datas) {
      this.selectDatas = datas
    },
    delShopss(data) {
      this.delShop([data.uid])
    },
    batchDel() {
      if (!this.selectDatas.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      const ids = this.selectDatas.map(item => {
        return item.uid
      })
      this.delShop(ids)
    },
    // 删除商品
    async delShop(ids) {
      const params = ids
      const result = await cancelRelation(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.$message.success('删除成功')
        this.ininHomeShopList()
      }
    },
    // 滚动到底部加载
    scrollLoad() {
      if (this.columnShopDialogSearchForm.total <= this.columnShopDialogList.length || this.columnShopDialogSearchForm.loading) return
      this.columnShopDialogSearchForm.page++
      switch (this.columnShopDialogTabActive) {
        case '2':
          // 加载视频列表
          this.initVideList()
          break
      }
    },
    // 确认添加课程
    async realAddShop() {
      this.columnAddShopForm.moduleUid = this.columnInfo.uid
      const params = {
        ...this.columnAddShopForm
      }
      const result = await addGoods(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.$message.success('添加课程成功')
      }
      this.columnShopDialogVisible = false
      // 刷新
      this.ininHomeShopList()
    },
    videoSelectionChange(data) {
      this.columnAddShopForm.contentList = data.map(item => {
        return {
          contentType: Number(this.columnShopDialogTabActive),
          contentUid: item.uid,
          name: item.title
        }
      })
    },
    searchShop() {
      this.columnShopDialogSearchForm.page = 1
      this.columnShopDialogList = []
      this.initVideList()
    },
    async initVideList() {
      this.columnShopDialogSearchForm.loading = true
      const params = {
        currentPage: this.columnShopDialogSearchForm.page,
        pageSize: this.columnShopDialogSearchForm.pageSize,
        title: this.columnShopDialogSearchForm.keyword,
        status:0
      }
      const result = await videoList(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.columnShopDialogSearchForm.total = result.data.total
        this.columnShopDialogList = [...this.columnShopDialogList, ...result.data.records]
        console.log(this.columnShopDialogList)
      }
      this.columnShopDialogSearchForm.loading = false
    },
    async ininHomeShopList() {
      this.columnShopHomeList = []
      const params = {
        moduleUid: this.columnInfo.uid,
        currentPage: this.page,
        pageSize: this.pageSize,
        keyword: this.columnShopSearchForm.shopName,
        contentType: this.columnShopSearchForm.shopType
      }
      const result = await getGoodsPageList(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.total = result.data.total
        this.columnShopHomeList = result.data.records
      }
    },
    columnShopDialogTabClick({ name }, event) {
      // if (this.columnShopDialogSearchForm.page == val) return;
      // 重置当前页
      this.columnShopDialogSearchForm.page = 1
      console.log(name)
      switch (name) {
        case '2':
          // 加载视频列表
          this.initVideList()
          break
      }
    },
    columnShopDialogClose() {

    },
    share() {
      this.shareDialogVisible = true
    },
    addShop() {
      this.columnShopDialogSearchForm.page = 1
      this.columnShopDialogTabActive = '2'
      this.initVideList()
      this.columnShopDialogVisible = true
    },
    columnShopClick() {

    },
    goBack() {
      this.columnManagementThis.flag = 2
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.ininHomeShopList()
    },
    handleCurrentChange(val) {
      this.page = val
      this.ininHomeShopList()
    }
  }
}
</script>
<style lang="scss" scoped>
.column-shop-component {
    padding-bottom: 20px;
    .el-qr-dialog {
        /deep/ .el-dialog__body {
            display: flex;
            justify-content: center;
            .qr-code {
                width: 150px;
                height: 150px;
                background: rebeccapurple;
            }
        }
    }
    .el-pagination {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }

    .column-shuo-content {
        padding: 0 20px;
        .column-info{
            padding: 20px;
            width: 100%;
            min-height: 70px;
            background: rgb(245, 247, 250);
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left{
                display: flex;
                align-items: center;
                .cover{
                    margin-right: 20px;
                    width: 130px;
                    height: 70px;
                    background: #2a75ed;
                    overflow: hidden;
                    img{
                        width: 100%;
                        height: 100%;
                        object-fit: fill;
                    }
                }
                .left-desc{
                    display: flex;
                    flex-direction: column;
                    height: 70px;
                    justify-content: space-between;
                    .title{

                    }
                    .bottom-info{
                        .type{
                            font-size: 14px;
                            color: red;
                        }
                        .time{
                            font-size: 15px;
                            color: #999;
                        }
                        .putaway-status{
                            position: relative;
                            margin-left: 10px;
                            display: inline-block;
                            font-size: 14px;
                            color: rgb(51, 51, 51);
                            .dot{
                              position: absolute;
                              width: 5px;
                              height: 5px;
                              border-radius: 50%;
                              left: -7px;
                              bottom: 3px;
                            }
                            .dot-up{
                                background: rgb(7, 193, 96);
                            }
                            .dot-down{
                                background: rgb(215, 216, 215);
                            }
                        }
                    }
                }
            }
            .right{
                cursor: pointer;
            }
        }
        .page-header{
            padding: 20px 0;
        }
        .head {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .search {
                display: flex;
                align-items: center;

                .search-btn {
                    margin-left: 10px;
                }
            }
        }

        .column-camp-table-box {
            .column-shop-table {
                .operation {
                    padding: 0;
                    margin: 0 auto;
                    list-style: none;
                    display: flex;
                    width: max-content;

                    li {
                        color: #2a75ed;
                        cursor: pointer;
                        float: left;
                        padding: 0 15px;
                        display: flex;
                        align-items: center;
                        position: relative;
                        justify-content: center;

                        &::after {
                            content: "";
                            height: 14px;
                            border: 1px solid #ebe8e8;
                            right: 0;
                            position: absolute;
                        }

                        &:last-child::after {
                            border: none;
                        }
                    }
                }
            }
        }
    }
}
</style>
