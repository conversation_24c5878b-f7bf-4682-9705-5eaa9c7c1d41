import request from "@/utils/request";

export function getList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainKnowledge/getList",
    method: "post",
    data: params
  });
}

export function add(params) {
  return request({
    url: process.env.ADMIN_API + "/trainKnowledge/add",
    method: "post",
    data: params
  });
}

export function edit(params) {
  return request({
    url: process.env.ADMIN_API + "/trainKnowledge/edit",
    method: "post",
    data: params
  });
}

export function deleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/trainKnowledge/deleteBatch",
    method: "post",
    data: params
  });
}

export function getAuthKnowledge(params) {
  return request({
    url: process.env.ADMIN_API + "/trainKnowledge/authKnowledge",
    method: "post",
    data: params
  });
}
