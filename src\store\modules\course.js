const state = {
  courseInfo: {},
  myCreateNormolCourseStep: "addCourseOne", // 个人中心我的课程创建单品课当前步骤
  myCreateNormalCourseCurrentUid: "", // 个人中心我的课程创建单品课执行当前步骤的课程uid
  myCreateCoursePackageStep: "createPackageOne", // 个人中心我的课程创建课程套餐当前步骤
  myCreateCoursePackageCurrentUid: "", // 个人中心我的课程创建课程套餐执行当前步骤的课程uid
  hasIndex: true, // 判断（全部套餐/全部课程）是否是列表页
  currentSubmitType: 'add', // 当前的提交的类型 如 添加，修改，查看
};

const mutations = {
  // 保存课程数据
  setCourseInfo(state, data) {
    state.courseInfo = data;
  },
  // 改变创建单品课程步骤
  setMyCreateNormolCourseStep(state, data) {
    console.log("改变单品课步骤", data);
    state.myCreateNormolCourseStep = data;
  },
  // 改变创建单品课当前的课程uid
  setMyCreateNormalCourseCurrentUid(state, data) {
    console.log("改变创建课程id", data);
    state.myCreateNormalCourseCurrentUid = data;
  },
  // 改变创建套餐课程步骤
  setMyCreateCoursePackageStep(state, data) {
    state.myCreateCoursePackageStep = data;
  },
  // 改变创建套餐当前的套餐uid
  setMyCreateCoursePackageCurrentUid(state, data) {
    state.myCreateCoursePackageCurrentUid = data;
  },
  // 改变（全部套餐/全部课程）是否是列表页
  setHasIndex(state, data) {
    state.hasIndex = data;
  },
  // 改变当前的提交的类型
  setCurrentPackageType(state, data) {
    state.currentSubmitType = data
  }
};

export default {
  namespaced: true,
  state,
  mutations
};
