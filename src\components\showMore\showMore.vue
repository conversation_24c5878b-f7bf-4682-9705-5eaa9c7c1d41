<template>
    <div :style="containerStyle" class="showMore-container">
        <div class="inner" :style="{
            height: flagType == 1 ? thresholdHeight-4 + 'px' : 'auto',
        }">
            <div ref="express-box" class="express-box">
                <slot></slot>
            </div>
        </div>
        <span :style="[
            {
                marginTop: '20px',
                position: 'absolute',
                right: 0,
                background: '#fff',
                top:'-14px',
                color:'#0086fd'
            },
            textStyle
        ]" @click="spread" v-show="flagType != 0">
            {{ flagType == 1 ? '点击展开' : '点击收起' }}
        </span>
    </div>
</template>
<script>
import { throttle } from "@/utils/commonUtil";
export default {
    props: {
        thresholdHeight: {
            type: Number,
            default: 39
        },
        containerStyle: {
            type: Object,
            default: () => ({ })
        },
        textStyle: {
            type: Object,
            default: () => ({ })
        }
    },
    data(){
        return {
           timer:null,
           flagType:0
        }
    },
    beforeDestroy() {
        clearTimeout(this.timer);
        window.removeEventListener('resize', this.init);
    },
    async mounted() {
        await this.$nextTick();
        this.timer=setTimeout(() => { 
            this.init();
            this.initReset();
        },300)
    },
    methods: {
        spread() { 
            this.flagType = this.flagType == 1 ? 2 : 1;
        },
        initReset() { 
            window.addEventListener('resize', this.init);
        },
        async init() { 
            throttle(async () => {
                await this.$nextTick();
                let expressBox = this.$refs['express-box'];
                let boxH = expressBox.offsetHeight;
                console.log("boxH",boxH);
                this.timer = setTimeout(() => {
                    if (expressBox.offsetHeight > this.thresholdHeight) {
                        // 展示并且为收缩状态
                        this.flagType = 1;
                    } else if (expressBox.offsetHeight <=this.thresholdHeight) {
                        this.flagType = 0;
                    }
                }, 300);
            })();
        }
    },
}
</script>
<style lang='scss' scoped>
.showMore-container {
    display: flex;
    flex-direction: column;
    position: relative;
    width:calc(100% - 64px);
    cursor: pointer;
    .inner{
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    .express-box {
        width:97%
    }
    span {
    }
}
</style>