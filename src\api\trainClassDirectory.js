import request from "@/utils/request";

export function getDirectoryList(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClassDirectory/getList",
    method: "post",
    data: params
  });
}

export function addDirectory(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClassDirectory/add",
    method: "post",
    data: params
  });
}

export function editDirectory(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClassDirectory/edit",
    method: "post",
    data: params
  });
}

export function deleteDirectoryData(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClassDirectory/delete",
    method: "post",
    data: params
  });
}

export function deleteDirectoryBatch(params) {
  return request({
    url: process.env.ADMIN_API + "/trainClassDirectory/deleteBatch",
    method: "post",
    data: params
  });
}

export function allEnableDirectoryList(classUid) {
  return request({
    url:
      process.env.ADMIN_API +
      "/trainClassDirectory/allEnableList?classUid=" +
      classUid,
    method: "get"
  });
}
