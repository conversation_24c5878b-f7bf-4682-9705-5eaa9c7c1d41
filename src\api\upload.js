import request from '@/utils/request'

/**
 * 上传图片文件
 * @param params
 */
export function uploadFile(params) {
  return request({
    url: process.env.PICTURE_API + '/file/cropperPicture',
    method: 'post',
    data: params
  })
}

/**
 * 活动文件上传
 * @param params
 */
 export function activityUploadFile(params) {
    return request({
      url: process.env.PICTURE_API + '/file/activityUploadFile',
      method: 'post',
      data: params
    })
}
  
// 分片上传接口
export function bigFileUpload(params, callback) {
  return request({
    url: process.env.PICTURE_API + '/file/bigFileUpload',
    method: 'post',
    data: params,
    onUploadProgress:callback
  })
}

//分片合成
// 分片上传接口
export function chunkMerge(params) {
  return request({
    url: process.env.PICTURE_API + '/file/merge',
    method: 'get',
    params
  })
}