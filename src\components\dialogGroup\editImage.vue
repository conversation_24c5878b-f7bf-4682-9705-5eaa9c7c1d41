<template>
  <div class="dialog">
    <dialog-com
      :title="title"
      :is-image="true"
      :dialog-visible="dialogVisible"
      :submit="_submit"
      :cancel="_closeDialog"
      :dis-flag="false"
    >
      <template>
        <div class="dialog_Image">
          <el-form ref="imageForm" :model="formData" :rules="rules" label-width="120px">
            <el-form-item label="镜像名称:" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入镜像名称"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <div v-if="!editData">
              <el-form-item label="镜像源:" prop="source_type">
                <el-radio-group v-model="formData.source_type" @change="sourceClick">
                  <el-radio-button
                    v-for="item in sourceOptions"
                    :key="item.value"
                    :label="item.value"
                  >{{ item.label }}</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="activeSource == '0'" label="镜像地址:" prop="url">
                <el-input
                  v-model="formData.url"
                  placeholder="www.baidu.com"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item v-if="activeSource == '1'" label="镜像文件:" prop="route">
                <el-upload
                  ref="upload"
                  :auto-upload="false"
                  :action="imageUrl"
                  :data="uploadData"
                  :on-success="_onSuccess"
                  :on-change="handleChange"
                  :on-error="_onError"
                  :limit="1"
                  :file-list="fileList"
                  :headers="myHeaders"
                  :multiple="false"
                  class="upload-demo"
                >
                  <div style="width:100%;height:36px;border:1px dashed #ccc;background:#F7F7F7;">+上传</div>
                  <div style="display:none">
                    <input :value="formData.route" type="text" >
                  </div>
                </el-upload>
              </el-form-item>
            </div>
            <el-form-item v-if="!editData" label="镜像类型:">
              <el-radio-group v-model="formData.type" @change="typeClick">
                <el-radio-button
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.value"
                >{{ item.label }}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <div v-if="activeType =='0' || (editData && editData.container_format=='bare')">
              <el-form-item label="格式:" prop="disk_format">
                <el-select v-model="formData.disk_format" placeholder="raw">
                  <el-option
                    v-for="item in formatList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <el-form-item prop="isProtected">
              <el-checkbox v-model="formData.isProtected">受保护</el-checkbox>
            </el-form-item>
            <div v-if="advanced">
              <el-form-item label="最小磁盘:" prop="min_disk">
                <el-input-number v-model="formData.min_disk" :min="0" :max="999" type="number"/>
              </el-form-item>
              <el-form-item label="最低内存:" prop="min_ram">
                <el-input-number v-model="formData.min_ram" :min="0" :max="999" type="number"/>
              </el-form-item>
              <el-form-item prop="hw_boot_menu">
                <el-checkbox v-model="formData.hw_boot_menu">是否显示启动菜单</el-checkbox>
              </el-form-item>
              <el-form-item prop="img_config_drive">
                <el-checkbox v-model="formData.img_config_drive">镜像是否配置驱动</el-checkbox>
              </el-form-item>
              <el-form-item prop="hw_qemu_guest_agent">
                <el-checkbox v-model="formData.hw_qemu_guest_agent">是否支持qemu_guest_agent</el-checkbox>
              </el-form-item>
              <p style="color:#aaa;margin-left:120px;">当镜像存在初始用户名和密码，请填写一下信息</p>
              <el-form-item label="用户名:" prop="user_name">
                <el-input v-model="formData.user_name"/>
              </el-form-item>
              <el-form-item label="密码:" prop="pass_word">
                <el-input v-model="formData.pass_word" type="password"/>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </template>
    </dialog-com>
  </div>
</template>
<script>
import dialogCom from '@/components/dialogGroup/index'
import {
  uploadImages,
  getImage,
  // addImages,
  // addISOs,
  uploadImagesUrl,
  updateImage
} from '@/api/sourceLibrary/imageApi'
export default {
  name: 'EditImage',
  components: {
    dialogCom
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    // 主界面单选框选中传来的数据
    editData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      uploadData: {},
      uuid: '', // 上传返回的id
      advanced: false, // 高级选项默认关闭
      disFlag: false,
      imageUrl: '', // 镜像文件
      activeSource: '1', // 镜像源默认选中URL
      activeType: '0', // 镜像类型默认选中磁盘镜像
      fileList: [], // 上传
      formData: {
        // 表单数据
        name: '', // 名称
        route: '', // 镜像文件
        url: '', // 镜像地址
        source_type: '1', // 镜像源
        type: '0', // 镜像类型
        os_type: 'Linux', // 操作系统类型
        isProtected: false,
        disk_format: 'raw', // 格式
        hw_vif_model: 'virtio', // 网卡模式
        ipv6_support: false,
        // properties: {
        // ipv6_support:false,
        // },
        min_disk: '', // 最小磁盘
        min_ram: '', // 最低内存
        hw_boot_menu: false, // 是否显示启动菜单
        img_config_drive: false, // 镜像是否配置驱动
        hw_qemu_guest_agent: false, // 是否支持qemu_guest_agent
        user_name: '', // 用户名
        pass_word: '', // 密码
        uuid: '' // 上传镜像文件返回的字段id
      },
      // 镜像源
      sourceOptions: [
        // { label: 'URL', value: '0' },
        { label: '本地文件', value: '1' }
      ],
      // 镜像类型
      typeOptions: [
        { label: '磁盘镜像', value: '0' },
        { label: '光盘镜像', value: '1' }
      ],
      // 操作系统类型
      typeList: [
        { label: 'Linux', value: 'Linux' },
        { label: 'Windows', value: 'Windows' }
      ],
      // 网卡模式
      networkList: [
        { label: 'virtio', value: 'virtio' },
        { label: 'e1000', value: 'e1000' }
      ],
      // 格式
      formatList: [
        { label: 'raw', value: 'raw' },
        { label: 'qcow2', value: 'qcow2' }
      ],
      rules: {
        route: [{ required: true, message: '请上传镜像文件' }],
        source_type: [{ required: true }],
        url: [{ required: true, message: '镜像地址为必填项' }],
        name: [
          { required: true, message: '请输入镜像名称', trigger: 'blur' },
          {
            min: 5,
            max: 20,
            message: '长度在 5 到 20 个字符',
            trigger: 'blur'
          }
        ],
        os_type: [{ required: true, message: '请选择', trigger: 'blur' }],
        hw_vif_model: [{ required: true, message: '请选择', trigger: 'blur' }],
        min_disk: [
          { required: true, message: '请输入最小磁盘数', trigger: 'blur' }
        ],
        min_ram: [
          { required: true, message: '请输入最小内存大小', trigger: 'blur' }
        ],
        disk_format: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  // 授权登录
  computed: {
    myHeaders() {
      const authCode = localStorage.getItem('Admin-Token')
      return { 'Admin-Token': authCode }
    }
  },
  created() {
    this.imageUrl = '/api/' + uploadImages()
    if (this.editData) {
      this.formData = this.editData
    }
    // console.log(this.editData);
    this.editData && this.getDetail()
  },
  methods: {
    async getDetail() {
      await getImage(this.editData.id).then((res) => {
        if (res.code == 200) {
          this.formData = res.data
          this.formData.hw_boot_menu == 'True'
            ? (this.formData.hw_boot_menu = true)
            : (this.formData.hw_boot_menu = false) // 动态菜单
          this.formData.hw_qemu_guest_agent == 'no'
            ? (this.formData.hw_qemu_guest_agent = false)
            : (this.formData.hw_qemu_guest_agent = true)
          this.formData.img_config_drive == 'optional'
            ? (this.formData.img_config_drive = false)
            : (this.formData.img_config_drive = true)
        }
      })
    },
    // 高级选项
    advance() {
      this.advanced = !this.advanced
    },
    // 镜像源切换
    sourceClick(val) {
      if (val * 1 === 0) {
        this.disFlag = false
      } else {
        this.disFlag = true
      }
      this.activeSource = val
    },
    // 镜像类型切换
    typeClick(val) {
      this.activeType = val
    },
    _onSuccess() {
      this.fileList = []
      this.$emit('fromChild')
      this.msgSuccess('新增光盘成功')
      this.$parent.editImageFlag = false
    },
    handleChange() {
      this.formData.route = true
    },
    _onError(err) {
      console.log(err)
      this.msgError(`文件上传出错，请重新上传!`)
      this.fileList = []
    },
    // 编辑/新建弹框确认
    _submit() {
      this.$refs['imageForm'].validate((valid) => {
        if (valid) {
          const result = JSON.parse(JSON.stringify(this.formData))
          result.source_type = result.source_type == '1' ? 'file' : 'http'
          result.uuid = this.uuid
          this.uploadData = result
          if (result.id) {
            // this.formData.isProtected = this.formData.isProtected == "是" ? true: false // 受保护
            // this.formData.img_config_drive = this.formData.img_config_drive == "mandatory" ? true: false // 镜像是否配置驱动
            // this.formData.hw_qemu_guest_agent = this.formData.hw_qemu_guest_agent == "yes" ? true: false // 是否支持qemu_guest_agent
            // this.formData.ipv6_support =this.formData.properties.ipv6_support == "是" ? true: false // 支持ipv6
            updateImage(result).then((response) => {
              if (response.code === 200) {
                this.disFlag = true
                this.fileList = []
                this.$emit('fromChild')
              }
              this.msgSuccess('修改成功')
              this.$parent.editImageFlag = false
            })
          } else if (this.activeSource == '0') {
            console.log('000000000', result)
            uploadImagesUrl(result).then((response) => {
              if (response.code === 200) {
                this.fileList = []
                this.$emit('fromChild')
              }
              this.msgSuccess('新增磁盘成功')
              this.$parent.editImageFlag = false
            })
          } else {
            console.log('111111111', this.uploadData, result)
            this.$nextTick(() => {
              this.$refs.upload.submit()
            })
          }
        }
      })
    },
    // 编辑弹框关闭
    _closeDialog() {
      this.$parent.editImageFlag = false
      this.$parent.$refs.multipleTable.clearSelection()
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog_Image {
  .el-form::v-deep {
    .el-form-item {
      .el-form-item__content {
        .el-upload {
          width: 100%;
        }
      }
    }
    .el-select,
    .el-cascader--medium {
      width: 100%;
    }
    .el-input--medium {
      width: 98%;
    }
  }
}
</style>
