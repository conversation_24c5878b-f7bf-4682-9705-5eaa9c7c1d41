<template>
  <div class="dialog-confi">
    <el-dialog
      v-if="dialogFlag"
      :title="title"
      :visible.sync="dialogFlag"
      :before-close="_beforeClose"
      center
      width="50%"
    >
      <!-- 内容区域 -->
      <div v-if="virtualList.length" class="dialog_content">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="云主机：">
            <svg-icon icon-class="computer" class-name="card-panel-icon" />
            {{ virtualList[0].facilityName }}
          </el-form-item>
          <el-form-item label="新密码：" prop="password">
            <el-input v-model="form.password" :type="passw">
              <i slot="suffix" :class="icon" @click="showPass"/>
            </el-input>
          </el-form-item>
          <el-form-item>
            <p style="margin-top:-20px;color:rgba(0,0,0,0.5)">8 - 30个字符，必须同时包含其中三项（大写字母、小写字母、数字）</p>
          </el-form-item>
        </el-form>
        <div class="dialog-footer" style="text-align:center">
          <el-button @click="cancal">取 消</el-button>
          <el-button :disabled="disFlag" type="primary" style="margin-left:120px" @click="sure">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { cloudHostResetPassword } from '@/api/sourceLibrary/virtualApi'
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      dialogFlag: true,
      form: {},
      rules: {
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },
      // 用于更换Input中的图标
      icon: 'el-input__icon el-icon-view',
      passw: 'password',
      disFlag: false
    }
  },
  created() {
    this.virtualList = this.list
  },
  methods: {
    _beforeClose() {
      this.$emit('cancelDialog', true)
    },
    cancal() {
      this.$emit('cancelDialog', true)
      this.dialogFlag = false
    },
    sure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.disFlag = true
          const paramas = {
            cloudHostId: this.list[0].id,
            password: this.form.password
          }
          cloudHostResetPassword(paramas)
            .then((res) => {
              if (res.code == 200) {
                // this.form.networksQuotasList = res.data
                // this.$message.success(res.msg)
                res.name = '重置' + this.list[0].facilityName + '密码， '
                this.$parent.msgList = res
                this.$emit('msgShow')
                this.$nextTick(() => {
                  this.cancal()
                })
              } else {
                res.name = this.list[0].facilityName
                this.$parent.msgList = res
                this.$emit('msgShow')
                // this.$message.error(res.msg)
              }
              this.disFlag = false
            })
            .catch((err) => {
              this.disFlag = false
              console.log(err)
            })
        } else {
          return false
        }
      })
    },
    showPass() {
      // 点击图标是密码隐藏或显示
      if (this.passw == 'text') {
        this.passw = 'password'
        // 更换图标
        this.icon = 'el-input__icon el-icon-view'
      } else {
        this.passw = 'text'
        this.icon = 'el-input__icon el-icon-loading'
      }
    }
  }
}
</script>
<style lang="scss"  scoped>
.dialog-confi ::v-deep {
  .dialog_content {
    .el-dialog__body {
      height: 500px;
    }
    ._progress {
      .el-progress-bar__outer {
        border-radius: 0px;
        .el-progress-bar__inner {
          border-radius: 0px;
        }
      }
      .p {
        margin: 20px 0 5px;
      }
    }
    .el-progress__text {
      display: none;
    }
  }
}
</style>
