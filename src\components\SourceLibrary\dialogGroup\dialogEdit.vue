<template>
  <div class="dialog">
    <dialogCom
      v-if="dialogFlag"
      :title="title"
      :dialog-visible.sync="dialogFlag"
      :cancel="_closeDialog"
      :dis-flag="disFlag"
      :submit="_submit"
    >
      <template>
        <div class="dialog_item">
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="名称" prop="facilityName11">
              <el-input v-model="form.facilityName11"/>
            </el-form-item>
            <el-form-item label="设备描述" prop="description">
              <el-input
                v-model="description"
                type="textarea"
                placeholder="请输入设备描述"
                maxlength="255"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="端口号">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="可输入多个端口号，以逗号隔开区分，如2020,3030,1780"
                v-model="form.ports">
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="镜像名称" prop="jname">
                <el-input type="text" v-model="form.jname" placeholder="请输入镜像名称"></el-input>
            </el-form-item>-->
          </el-form>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import { editVirtual } from '@/api/sourceLibrary/virtualApi'
import dialogCom from '@/components/SourceLibrary/dialogGroup/index'
export default {
  components: {
    dialogCom
  },
  props: {
    title: {
      type: String,
      default: null
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    facilityDescription: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: ''
    }
  },
  data() {
    return {
      disFlag: false,
      dialogFlag: false,
      // form: this.list[0],
      form: {
        id: this.list[0].id,
        facilityName11: this.list[0].facilityName,
        description: this.list[0].facilityDescription,
        ports: this.list[0].ports
      },
      description: this.facilityDescription,
      rules: {
        facilityName11: {
          required: true,
          message: '请输入名称',
          trigger: 'change'
        }
      }
    }
  },
  watch: {
    dialogVisible: {
      deep: true,
      immediate: true,
      handler(val) {
        this.dialogFlag = val
      }
    }
  },
  mounted() {
    console.log('this.list[0]---------------------', this.list[0])
    // this.form = this.list[0]
    // this.form.ports=this
    console.log("form",this.form)
    this.form.description = this.facilityDescription
    this.dialogFlag = this.dialogVisible
    // this.getList()
  },
  methods: {
    // 获取数据列表
    getList() {},
    _closeDialog() {
      this.dialogFlag = false
      this.$parent.dialogEditFlag = false
    },
    _submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.disFlag = true
          console.log(this.form)
          const params = {
            id: this.form.id,
            facilityDescription: this.description || '',
            facilityName: this.form.facilityName11,
            ports:this.form.ports
          }
          console.log(params)
          editVirtual(params)
            .then((res) => {
              if (res.code == 200 || res.code == 0) {
                // this.dialogFlag = false
                res.name = '编辑' + this.list[0].facilityName + '， '
                this.$parent.msgList = res
                this.$emit('msgShow')
                // this.$message.success(res.msg)
                this.$emit('updateList')
                this._closeDialog()
              } else {
                this.$message.error(res.msg)
              }
              this.disFlag = false
            })
            .catch((err) => {
              this.disFlag = false
              console.log(err)
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>
