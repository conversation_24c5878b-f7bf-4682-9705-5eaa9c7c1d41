import request from "@/utils/request";

//获取地区树形
export function getAreaTree(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/getAreaTree",
    method: "get"
  });
}
//添加地区树形
export function addArea(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/addArea",
    method: "post",
    data: params
  });
}

//删除地区树形
export function deleteArea(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/deleteArea/"+params.areaId,
    method: "delete",
    data: params
  });
}

//更新地区树形
export function updateArea(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/editArea",
    method: "post",
    data: params
  });
}

