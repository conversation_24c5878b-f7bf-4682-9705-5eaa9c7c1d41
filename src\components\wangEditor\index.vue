<template>
    <div :style="{ border: showBoder ?'1px solid #ccc':''}">
        <Toolbar v-if="!onlyEditor" style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
        <Editor v-loading="loading" :key="flag" :style="{ height: height,overflowY: 'hidden'}" v-model="wangEditorContent" :defaultConfig="propsEditorConfig" :mode="mode"
            @onCreated="onCreated" @onChange="onChange" />
            <hyMaterial :show.sync="materialShow" @select="selectMaterial" title="选择素材" :type="selectType" :limit="1" />
    </div>
</template>
<script>
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { uploadFile } from '@/api/file'
import { DomEditor, Boot, IModuleConf, IButtonMenu, IDomEditor } from '@wangeditor/editor'
import hyMaterial from '@/components/HyMaterial'
import Videomenu from "@/utils/Videomenu";
import Material from "@/utils/Material";
export default {
    components: { Editor, Toolbar, hyMaterial,  },
    props: {
        showBoder: {
            type: Boolean,
            default:true
        },
        height: {
            type: String,
            default: '400px'
        },
        // 是否仅展示编辑区域
        onlyEditor: {
            type: Boolean,
            default: false,
        },
        // 编辑器内容
        content: {
            type: String,
            default: "",
        },
        // 编辑器配置
        editorConfig: {
            type: Object,
            default: () => { },
        }
    },
    data() {
        return {
            flag:0,
            loading:false,
            editor: null,
            toolbarConfig: {
                excludeKeys: ["group-image", "group-video"]
            },
            materialShow: false,
            selectType: 1,//1图片 2视频
            propsEditorConfig: null,
            defaultEditorConfig: {
                placeholder: "请输入内容...",
                readOnly: false,
                autoFocus: true,
                scroll: true,
                MENU_CONF: {}
            },
            mode: 'default', // or 'simple'
        }
    },
    computed: {
        wangEditorContent: {
            get() {
                return this.content;
            },
            set(val) {
                return this.content;
            }
        }
    },
    methods: {
        //素材库选择
        selectMaterial(val) {
            // console.log('素材库选择', val)
            //封面上传
            if (this.upType == 1) {
                this.form.fileUid = val[0].fileUid
                this.form.coverURL = val[0].pictureUrl
            }
            //文章内容上传
            if (this.upType == 2) {
                this.setSelectionAddress()
                //选择图片
                if (this.selectType == 1) {
                    const node = { type: 'image', src: val[0].pictureUrl, children: [{ text: '' }] }
                    this.editor.insertNode(node);
                }
                //选择视频
                if (this.selectType == 2) {
                    const node = { type: 'video', src: val[0].fileUrl, children: [{ text: '' }] }
                    this.editor.insertNode(node);
                }
            }
        },
        //设置编辑器光标位置
        setSelectionAddress() {
            this.editor.select(this.selection)
            console.log('设置光标位置', this.selection)
        },
        // 内容改变触发
        onChange(editor) {
            let html = editor.getHtml();
            console.log(html);
            this.$emit("update:content", html);
        },
        onCreated(editor) {
            let that = this;
            this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
            this.editor.on('exec', () => {
                // 选择素材
                this.materialShow = true;
            });
            this.$nextTick(() => {
                let allKeys = editor.getAllMenuKeys();
                const toolbar = DomEditor.getToolbar(editor)
                const curToolbarConfig = toolbar.getConfig()
                curToolbarConfig.insertKeys = {
                    index: Date.now() + '',
                    keys: ['material']
                }
                this.initMediaMenuEvent()
            });
            if (this.$route.query.type) {
                this.editor.disable()
            }
            const MenusList = [{
                key: 'material',
                class: Material,
                index: 32
            }, {
                key: 'videomenu',
                class: Videomenu,
                index: 33
            }]
            this.$nextTick(() => {
                const toolbar = DomEditor.getToolbar(editor)
                const curToolbarConfig = toolbar.getConfig() //工具栏配置
                const allRegisterMenu = editor.getAllMenuKeys() //所有注册的菜单
                // console.log('工具栏配置', editor.getAllMenuKeys())
                let keys = []
                for (let item of MenusList) {
                    if (allRegisterMenu.indexOf(item.key) < 0) { // 如果未注册，则注册
                        const menuObj = {
                            key: item.key,
                            factory() {
                                return new item.class()
                            }
                        }
                        Boot.registerMenu(menuObj);
                    }
                    keys.push(item.key)
                }
                //插入Toolbar
                curToolbarConfig.insertKeys = {
                    index: MenusList[0].index,
                    keys: keys
                }
                //初始化工具栏
                this.initMediaMenuEvent()
            });
        },
        initMediaMenuEvent() {
            const editor = this.editor
            let that = this;
            editor.on('materialShow', () => {
                // console.log('点击了选择素材库')
                this.getSelectionAddress()
                this.upType = 2
                this.selectType = 1
                that.materialShow = true;
            })
            editor.on('VideomenuShow', () => {
                this.getSelectionAddress()
                this.selectType = 2
                this.upType = 2
                that.materialShow = true;
            })
        },
        //获取并且保存编辑器光标位置
        getSelectionAddress() {
            this.selection = this.editor.selection
            console.log('光标位置', this.selection)
        },
        // 统一设置loading
        loadingFun(fun) {
            return async (file, insertFn) => {
                // 开启菊花图
                this.loading = true;
                this.editor.disable();
                await fun(file, insertFn);
                // 关闭菊花图
                this.loading = false;
                this.editor.enable();
            }
        },
        async customImageUpload(file, insertFn) {
            // 自定义上传
            let limit = file.size / 1024 / 1024;
            var reg = /(.*)\.(jpg|jpeg|png)$/gi;
            if (!reg.test(file.name)) {
                this.$message({
                    offset: 100,
                    type: "error",
                    message: "仅支持JPG/JPEG/PNG",
                });
                return;
            }
            //大于10M
            if (limit > 10) {
                this.$message({
                    offset: 100,
                    type: "warning",
                    message: "您上传的文件超过了10M了",
                });
                return;
            }
            let formData = new FormData();
            formData.append("file", file);
            formData.append("userUid", "uid00000000000000000000000000000000");
            formData.append("userUid", "uid00000000000000000000000000000000");
            formData.append("source", "picture");
            formData.append("sortName", "admin");
            formData.append("projectName", "blog");
            let result = await uploadFile(formData);
            console.log(result)
            //上传成功，图片合规
            if (result.code == this.$ECode.SUCCESS) {
                let imgUrl = result.data[0].url;
                // 插入图片
                insertFn(imgUrl);
            } else if (result.code == 601) {
                // 上传图片不合规
                this.$message({
                    offset: 100,
                    type: "warning",
                    message: "您上传的图片违规,请重新上传!",
                });
            }
            // insertFn(url, alt, href);
        },
        async customVideoUpload(file, insertFn) {
            let limit = file.size / 1024 / 1024;
            //大于500M
            if (limit > 500) {
                this.$message({
                    offset: 100,
                    type: "warning",
                    message: "您上传的文件超过了500M了",
                });
                return;
            }
            console.warn(this.editor)
            let formData = new FormData();
            formData.append("file", file);
            formData.append("userUid", "uid00000000000000000000000000000000");
            formData.append("userUid", "uid00000000000000000000000000000000");
            formData.append("source", "picture");
            formData.append("sortName", "admin");
            formData.append("projectName", "blog");
            let result = await uploadFile(formData)
            //上传成功
            if (result.code === this.$ECode.SUCCESS) {
                let videoUrl = result.data[0].url;
                insertFn(videoUrl);
            }
            console.log(file);
            // insertFn(url, alt, href);
        }
    },
    async mounted() {
        this.flag = 0;
        //编辑区配置（默认配置优先,用户配置覆盖）
        this.propsEditorConfig = {
            ...this.defaultEditorConfig,
            ...this.editorConfig
        }
        // 自定义图片上传
        this.propsEditorConfig.MENU_CONF["uploadImage"] = {
            customUpload: this.loadingFun(this.customImageUpload),
        };
        // 自定义视频上传
        this.propsEditorConfig.MENU_CONF["uploadVideo"] = {
            // 自定义上传
            customUpload: this.loadingFun(this.customVideoUpload),
        };
        this.flag = 1;
    },
    beforeDestroy() {
        const editor = this.editor
        if (editor == null) return
        editor.destroy() // 组件销毁时，及时销毁编辑器
    }
}
</script>
<style lang="scss" scoped>
</style>