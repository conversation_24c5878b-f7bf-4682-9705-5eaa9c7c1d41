<template>
  <div class="upload">
    <el-upload
      drag
      :action="action"
      :name="name"
      multiple
      :headers="{
        Authorization:$GetToken()
      }"
      :data="otherData"
      :show-file-list="false"
      :on-success="handleImageSuccess"
      :before-upload="beforeImageUpload"
    >
      <div class="upload-text" v-if="!imgUrl">
        <i :class="icon"></i>
        <div class="el-upload__text"><slot></slot></div>
      </div>
      <img v-else :src="imgUrl" alt="" class="img-url" />
    </el-upload>
  </div>
</template>

<script>
export default {
  name: "Upload",
  props: {
    // 上传地址
    action: {
      type: String,
    },
    // 图标
    icon: {
      type: String,
    },
    // 其它参数
    otherData: {
      type: Object,
    },
    // 获取系统配置
    systemConfig: {
      type: Object,
    },
    // 自定义file的名称
    name: {
      type: String,
      default: "file",
    },
    // 上传类型
    // 0：引流 1：购买前引流 2：购买后引流
    uploadType: {
      type: String,
      default: 0,
    },
  },
  data() {
    return {
      imgUrl: "",
    };
  },
  methods: {
    // 上传成功后的回调
    handleImageSuccess(res, file, fileList) {
      if (res.code == this.$ECode.SUCCESS) {
        let file = res.data;
        for (let index = 0; index < file.length; index++) {
          this.imgUrl =
            this.systemConfig.localPictureBaseUrl + file[index].picUrl;
        }
        this.$emit("setPicUrl", this.imgUrl, parseInt(this.uploadType));
      } else {
        this.$message.error(res.message);
      }
    },
    // 上传图片控制
    beforeImageUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isLt5M = file.size / (1024 * 5) / (1024 * 5) < 5;
      if (!isPNG && !isJPG) {
        this.$message.error("上传图片只能是JPG/PNG格式!");
      }
      if (!isLt5M) {
        this.$message.error("上传图片大小不能超过5MB!");
      }
      return isJPG || (isPNG && isLt5M);
    },
  },
};
</script>

<style scoped lang="scss">
.upload {
  margin-top: 20px;

  .upload-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 26px;
  }

  .img-url {
    width: 360px;
    height: 180px;
  }
}
</style>
