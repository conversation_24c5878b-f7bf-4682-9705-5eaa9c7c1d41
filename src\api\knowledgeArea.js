import request from "@/utils/request";

export function addArea(params) {
    return request({
        url: process.env.ADMIN_API + "/user/laboratoryTag/add",
        method: "post",
        data: params
    });
}

export function editArea(params) {
    return request({
        url: process.env.ADMIN_API + "/user/laboratoryTag/edit",
        method: "post",
        data: params
    });
}

export function deleteArea (params) {
    return request({
        url: process.env.ADMIN_API + "/user/laboratoryTag/delete",
        method: "post",
        data: params
    });
}

export function pageListArea(params) {
    return request({
        url: process.env.ADMIN_API + "/user/laboratoryTag/pageList",
        method: "post",
        data: params
    });
}