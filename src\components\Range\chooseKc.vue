<template>
  <div>
    <!-- 添加课程弹窗 -->
    <el-dialog
      :visible.sync="columnShopDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="beforeClose"
      title="添加课程"
      width="40%"
      center>
      <el-form :inline="true" :model="columnShopDialogSearchForm" size="mini" class="demo-form-inline">
        <el-form-item label="" label-width="0">
          <el-input v-model="columnShopDialogSearchForm.keyword" style="width: 100%;" placeholder="请输入名称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="initSearch">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="columnShopDialogSearchForm.loading"
        v-loadMore="scrollLoad"
        ref="kcTable"
        :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
        :cell-style="{ textAlign: 'center' }"
        :data="columnShopDialogList"
        row-key="uid"
        tooltip-effect="dark"
        class="column-shop-table"
        height="300px"
        style="width: 100%"
        @selection-change="selectionChange">
        <el-table-column :reserve-selection="true" type="selection" width="55"/>
        <el-table-column align="center" prop="title" show-overflow-tooltip label="标题"/>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel()">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { videoList } from '@/api/content/video'
export default {
  name: 'ChooseKc',
  props: {
    // 选中的数据
    dataList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      columnShopDialogVisible: true,
      columnShopDialogList: [],
      columnShopDialogSearchForm: {
        loading: false,
        total: 0,
        page: 1,
        pageSize: 10,
        keyword: ''
      },
      ids: [],
      flag: false // 选中锁,由于回显的时候会出发@selection-change事件导致其他默认选中的数据丢失,需要加一把锁
    }
  },
  created() {
    this.ids = this.dataList
    this.initSearch()
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    confirm() {
      this.$emit('confirm', this.ids)
    },
    // 手动点击选中
    selectionChange(data) {
      if (this.flag) return
      this.ids = data.map(item => {
        return {
          id: item.uid,
          name: item.title
        }
      })
    },
    // 滚动到底部加载
    scrollLoad() {
      if (this.columnShopDialogSearchForm.total <= this.columnShopDialogList.length || this.columnShopDialogSearchForm.loading) return
      this.columnShopDialogSearchForm.page++
      this.initList()
    },
    initSearch() {
      this.columnShopDialogSearchForm.page = 1
      this.columnShopDialogList = []
      this.initList()
    },
    async initList() {
      this.columnShopDialogSearchForm.loading = true
      const params = {
        currentPage: this.columnShopDialogSearchForm.page,
        pageSize: this.columnShopDialogSearchForm.pageSize,
        title: this.columnShopDialogSearchForm.keyword
      }
      const result = await videoList(params)
      if (result.code === this.$ECode.SUCCESS) {
        this.columnShopDialogSearchForm.total = result.data.total
        this.columnShopDialogList = this.columnShopDialogList.concat(result.data.records)
        // 设置选中
        this.setSelect()
      }
      this.columnShopDialogSearchForm.loading = false
    },
    // 设置选中
    setSelect() {
      // 加锁,防止出发selectionChange事件
      if (this.ids.length > 0) {
        this.flag = true
      }
      this.ids.forEach((item, index) => {
        this.$nextTick(() => {
          const itemObject = this.columnShopDialogList.find(item2 => {
            return item2.uid === item.id
          })
          if (itemObject) {
            this.$refs.kcTable.toggleRowSelection(itemObject, true)
          }
          // 循环结束,解锁
          if (index >= this.ids.length - 1) {
            this.flag = false
          }
        })
      })
    },
    beforeClose(done) {
      this.$emit('cancel')
      done()
    }
  }
}
</script>

<style>
</style>
