import request from '@/utils/request'

// 查询图标列表
export function listEquipmentIcon(query) {
  return request({
    url: '/accumulate/icon/list',
    method: 'get',
    params: query
  })
}

// 查询图标详细
export function getEquipmentIcon(iconId) {
  return request({
    url: '/accumulate/icon/' + iconId,
    method: 'get'
  })
}

// 新增图标
export function addEquipmentIcon(data) {
  return request({
    url: '/accumulate/icon/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改图标
export function updateEquipmentIcon(data) {
  return request({
    url: '/accumulate/icon/update',
    method: 'put',
    data: data
  })
}


// 删除图标
export function delEquipmentIcon(data) {
  return request({
    url: '/accumulate/icon/delete/' + data,
    method: 'delete'
  })
}
