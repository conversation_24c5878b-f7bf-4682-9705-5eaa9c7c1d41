<template>
  <div class="content-manage-child">
    <div class="top">
      <div class="left">
        <span>筛选状态：</span>
        <el-select
          size="mini"
          v-model="searchParams.status"
          placeholder="请选择"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <span style="margin-left: 30px;">时间筛选：</span>
        <el-date-picker
          size="mini"
          v-model="searchParams.date"
          type="date"
          placeholder="选择日期"
        >
        </el-date-picker>
      </div>
      <div class="right">
        <el-button size="mini" v-if="ids.length" type="danger" :disabled="ids.length == 0">删除</el-button>
        <el-button size="mini">操作日志</el-button>
        <el-button size="mini" @click="addDialog = true" type="primary"
          >添加Banner</el-button
        >
      </div>
    </div>

    <el-table
      :data="bannersList"
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="imgPath" label="Banner图">
        <template slot-scope="scope">
          <el-image
            style="width: 100px; "
            :src="scope.row.imgPath"
            fit="contain"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="Banner标题"> </el-table-column>
      <el-table-column prop="describe" label="Banner描述">
        <template slot-scope="scope">
          <div style="border:1px solid black">{{ scope.row.describe }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="createBy" label="上传者"> </el-table-column>
      <el-table-column prop="createTime" label="上传时间"> </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        :width="this.$store.getters.operationButtonWidth + 'px'"
      >
        <template slot-scope="scope">
          <div ref="operation">
            <el-button type="primary" size="mini" @click="offBanner(scope)">
              {{ scope.row.status == 1 ? "下架" : "上架" }}
            </el-button>
            <el-button
              type="danger"
              size="mini"
              @click="deleteBanner(scope.row.id)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部分页栏 -->
    <el-pagination
      style="float: right; margin-top:10px"
      class="bottom"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="searchParams.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>

    <el-dialog title="添加Banner" :visible.sync="addDialog" width="700px">
      <el-form
        ref="addForm"
        :rules="addFormRules"
        :model="addForm"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="addForm.title"
            maxlength="10"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="describe">
          <el-input
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            v-model="addForm.describe"
          ></el-input>
        </el-form-item>
        <el-form-item label="图片" prop="imgPath">
          <el-upload
            class="avatar-uploader"
            action="https://jsonplaceholder.typicode.com/posts/"
            :show-file-list="false"
            :on-error="_uploadImgError"
            :before-upload="_beforeUpload"
            :headers="{
              Authorization:$GetToken()
            }"
            :on-success="_uploadImgSuccess"
            :on-remove="_uploadImgRemove"
            :auto-upload="true"
          >
            <div
              class="addImg"
              :style="{ border: addForm.imgPath ? '' : '#8c939d 1px solid' }"
            >
              <img v-if="addForm.imgPath" :src="addForm.imgPath" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="addBanner">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    var validateImgPath = (rule, value, callback) => {
      if (value === "") {
        if (this.isEdit) callback();
        callback(new Error("请上传Banner图片"));
      } else {
        callback();
      }
    };
    return {
      statusOptions: [
        { value: "0", label: "待上架" },
        { value: "1", label: "已发布" }
      ],
      searchParams: {
        status: "1",
        date: null,
        limit: 10,
        page: 1
      },
      total: 10,
      loading: false,
      bannersList: [],
      ids: [],
      addDialog: false,
      addForm: { title: "", descibe: "", imgPath: "" },
      addFormRules: {
        title: [
          { required: true, message: "请输入文章详情", trigger: "blur" },
          { max: 10, message: "标题最多十个10 个字符", trigger: "blur" }
        ],
        describe: [
          { required: true, message: "请输入文章详情", trigger: "blur" },
          { max: 100, message: "描述最多十个100 个字符", trigger: "blur" }
        ],
        imgPath: [
          { required: true, validator: validateImgPath, trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getBannersList();
  },
  mounted() {
    // 设置操作栏长度，当从
    this.$nextTick(() => {
      this.$store.dispatch("setWidth", this.$refs.operation.children.length);
    });
  },
  methods: {
    // 获取banner列表
    async getBannersList() {
      this.loading = true;
      this.bannersList = [
        {
          id: 1,
          imgPath:
            "https://images.pexels.com/photos/12175838/pexels-photo-12175838.jpeg?auto=compress&cs=tinysrgb&w=1600&lazy=load",
          title: "企业实验室1",
          describe:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          createBy: "admin",
          status: 1,
          createTime: "2022-05-30"
        },
        {
          id: 2,
          imgPath:
            "https://images.pexels.com/photos/3178798/pexels-photo-3178798.jpeg?auto=compress&cs=tinysrgb&w=1600",
          title: "企业实验室2",
          describe:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          createBy: "admin",
          status: 1,
          createTime: "2022-05-30"
        },
        {
          id: 3,
          imgPath:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          title: "企业实验室3",
          describe:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          createBy: "admin",
          status: 1,
          createTime: "2022-05-30"
        }
      ];
      this.loading = false;
    },

    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.searchParams.limit = newSize;
      this.getBannersList();
    },

    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.searchParams.page = newSize;
      this.getBannersList();
    },

    // 下架
    async offBanner(scope) {
      console.log(scope);
      this.bannersList[scope.$index].status =
        this.bannersList[scope.$index].status == 1 ? 0 : 1;
      this.getBannersList();
    },

    // 删除
    async deleteBanner(ids) {
      var configResult = await this.$confirm(
        "此操作将永久删除该Banner图片, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).catch(err => {
        return err;
      });
      if (configResult !== "confirm") {
        return this.$message.info({ message: "已经取消删除", duration: 1000 });
      }
      this.$message.success({ message: "删除成功", duration: 1000 });
      console.log(ids);
      this.getBannersList();
    },

    // 表格前勾选框
    handleSelectionChange(val) {
      this.ids = val.map(item => {
        return item.id;
      });
    },

    async addBanner() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return;
      });
    },

    // 上传封面图片
    _uploadImgError(err, file, fileList) {
      this.$message.error("文件上传失败，请重试！");
    },
    _uploadImgSuccess(res, file) {
      let status = res.data && Object.keys(res.data)[0];
      if (status == 200) {
        // 图片审核通过
        // if (status == 400) this.form.status = "4"; // 把待审核状态传给后端
        // this.form.coverImage = res.data[status][0];
        this.addForm.imgPath = URL.createObjectURL(file.raw);
        console.log(this.addForm.imgPath);
        this.$refs.addForm.validateField("imgPath");
      } else if (status == 400) {
        this.$alert("图片疑似违规，请重新上传！", "提示", {
          confirmButtonText: "确定"
        });
      } else if (status == 500) {
      }
    },
    _beforeUpload(file) {
      let types = ["image/jpg", "image/png", "image/jpeg"];
      // console.log(file);
      const isJPG = types.includes(file.type);
      const isLt5M = file.size / 1024 / 1024 < 2;
      const _URL = window.URL || window.webkitURL;
      const img = new Image();
      img.src = _URL.createObjectURL(file);
      img.onload = () => {
        console.log(isJPG);
        console.log(isLt5M);
        if (img.height !== 380 || img.width !== 1920) {
          reject(this.$message.warning(`请上传高1920*380尺寸的图片`));
        }
        if (!isJPG) {
          reject(this.$message.error("上传图片只能是 jpg或png 格式!"));
        }
        if (!isLt5M) {
          reject(this.$message.error("上传图片大小不能超过 2MB!"));
        }
        resolve(true);
      };
      // if (!isJPG) {
      //   this.$message.error("上传图片只能是 jpg或png 格式!");
      // }
      // if (!isLt5M) {
      //   this.$message.error("上传图片大小不能超过 2MB!");
      // }
      // return isJPG && isLt5M;
    },
    _uploadImgRemove(file, fileList) {
      this.addForm.imgPath = "";
      this.$refs.addForm.validateField("imgPath");
    }
  }
};
</script>

<style lang="scss" scoped>
.content-manage-child {
  padding: 20px;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /deep/ .el-dialog {
    .el-dialog__header {
    }
    .el-dialog__body {
      .el-form {
        .el-form-item {
          .el-form-item__content {
            .el-input {
              width: 200px;
              input {
              }
            }
            .el-textarea {
              width: 500px;
            }
          }
        }
      }
    }
  }
}
.addImg {
  .avatar-uploader-icon {
    border: 1px solid balck;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
}
</style>
