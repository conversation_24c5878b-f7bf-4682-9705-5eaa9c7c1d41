<template>
  <div class="dialog">
    <dialogCom
      v-if="dialogOpenFlag"
      :title="title"
      :dialog-visible="dialogOpenFlag"
      :submit="_submit"
      :cancel="_closeDialog"
      :dis-flag="disFlag"
    >
      <template>
        <!-- <span>这是一段信息</span> -->
        <div v-loading="!virtualList.length" class="dialog_item">
          <p class="text_color_6">
            确定
            <span>{{ dialogFlag == 'open' ? '开启': dialogFlag == 'close' ? '关闭' : dialogFlag == 'update' ? '重启' : '恢复' }}</span>
            下列共 {{ hostingNum }} 个 云主机?
          </p>
          <p v-if="virtualList.length" class="text_color_3">
            <svg-icon icon-class="warningmsg" class-name="card-panel-icon" />
            {{ virtualList.length - hostingNum }}个 云主机 无法操作，已忽略!
          </p>
          <div v-if="virtualList.length" class="disVirtualList">
            <div
              v-for="(item, index) in virtualList"
              :key="index"
              :class="(dialogFlag == 'open' && item.status == 'ACTIVE') || (dialogFlag == 'close' && item.status == 'ACTIVE') || (dialogFlag == 'update' && item.status == 'ACTIVE') || dialogFlag == 'recover' ? 'disVirtual' : ''"
              class="disVirtual_content"
            >
              <svg-icon icon-class="computer" class-name="card-panel-icon" />
              {{ item.facilityName }}
              <svg-icon
                v-if="(dialogFlag == 'open' && item.status !== 'ACTIVE') || (dialogFlag == 'close' && item.status == 'ACTIVE') || (dialogFlag == 'update' && item.status == 'ACTIVE') || (dialogFlag == 'recover' && item.status == 'SUSPENDED')"
                icon-class="true"
                class-name="card-panel-icon status_class"
              />
            </div>
          </div>
          <div v-if="dialogFlag == 'close'" class="info_message">
            <p>平台将尝试正常关机，失败后进行强制关机操作！</p>
          </div>
          <div v-if="dialogFlag == 'update'" class="info_message">
            <p>强制重启，直接切断电源。没有及时写入硬盘中的内存数据将会丢失。</p>
          </div>
        </div>
      </template>
    </dialogCom>
  </div>
</template>
<script>
import {
  cloudHostRun,
  cloudHostStop,
  cloudHostReboot,
  resume
} from '@/api/sourceLibrary/virtualApi'
import dialogCom from '@/components/SourceLibrary/dialogGroup/index'
export default {
  components: {
    dialogCom
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    },
    title: {
      type: String,
      default: ''
    },
    dialogFlag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      canVirtualList: [], // 可操作主机列表
      virtualList: [], // 带状态主机列表
      closeFlag: 0,
      dialogOpenFlag: false,
      disFlag: false
    }
  },
  computed: {
    // 忽略主机数
    ignoreNum() {
      return this.virtualList.filter((ele) => ele.status == 'ACTIVE').length
    },
    // 可操作主机数
    hostingNum() {
      if (this.dialogFlag == 'open') {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.canVirtualList = this.virtualList.filter(
          (ele) => ele.status !== 'ACTIVE'
        )
      } else if (this.dialogFlag == 'close') {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.canVirtualList = this.virtualList.filter(
          (ele) => ele.status == 'ACTIVE'
        )
      } else if (this.dialogFlag == 'update') {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.canVirtualList = this.virtualList.filter(
          (ele) => ele.status == 'ACTIVE'
        )
      } else if (this.dialogFlag == 'recover') {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.canVirtualList = this.virtualList.filter(
          (ele) => ele.status == 'SUSPENDED'
        )
      }
      return this.canVirtualList.length
    }
  },
  watch: {
    closeFlag: {
      handler(val) {
        if (val == this.canVirtualList.length) {
          this._closeDialog()
        }
      }
    }
  },
  created() {
    this.getList()
    this.dialogOpenFlag = this.dialogVisible
  },
  methods: {
    // 获取数据列表
    async getList() {
      // eslint-disable-next-line no-empty
      if (this.dialogFlag == 'open') {
        // eslint-disable-next-line no-empty
      } else if (this.dialogFlag == 'close') {
        // eslint-disable-next-line no-empty
      } else if (this.dialogFlag == 'update') {
        // eslint-disable-next-line no-empty
      } else if (this.dialogFlag == 'recover') {
      }
      this.virtualList = this.list // 接口调试时需去掉本行代码
    },
    _submit() {
      if (this.dialogFlag == 'open') {
        this._startVolume()
      } else if (this.dialogFlag == 'close') {
        this._closeVolume()
      } else if (this.dialogFlag == 'update') {
        this._updateVolume()
      } else if (this.dialogFlag == 'recover') {
        this._recoverVolume()
      }
      // this.$nextTick(() => {
      //   this._closeDialog()
      // })
    },
    // 开机
    async _startVolume() {
      this.disFlag = true
      const list = []
      this.canVirtualList.forEach(item => {
        const flag = cloudHostRun(item.id)
          .then((res) => {
            res.name = '开启' + item.facilityName + '， '
            this.$parent.msgList = res
            this.$emit('msgShow')
            return res
          })
          .catch((err) => {
            console.log(err)
          })
        list.push(flag)
      })
      Promise.all(list).then(res => {
        if (this.canVirtualList && this.canVirtualList.length) {
          this.$emit('updateList')
        }
        this._closeDialog()
      })
    },
    // 关机
    async _closeVolume() {
      this.disFlag = true
      const list = []
      this.canVirtualList.forEach(item => {
        const flag = cloudHostStop(item.id)
          .then((res) => {
            res.name = '关闭' + item.facilityName + '， '
            this.$parent.msgList = res
            this.$emit('msgShow')
            return res
          })
          .catch((err) => {
            console.log(err)
          })
        list.push(flag)
      })
      Promise.all(list).then(res => {
        this.disFlag = false
        if (this.canVirtualList && this.canVirtualList.length) {
          this.$emit('updateList')
        }
        this._closeDialog()
      })
    },
    // 重启
    async _updateVolume() {
      this.disFlag = true
      const list = []
      this.canVirtualList.forEach(item => {
        const flag = cloudHostReboot(item.id)
          .then((res) => {
            res.name = '重启' + item.facilityName + '， '
            this.$parent.msgList = res
            this.$emit('msgShow')
            return res
          })
          .catch((err) => {
            console.log(err)
          })
        list.push(flag)
      })
      Promise.all(list).then(res => {
        this.disFlag = false
        if (this.canVirtualList && this.canVirtualList.length) {
          this.$emit('updateList')
        }
        this._closeDialog()
      })
    },
    // 恢复
    async _recoverVolume() {
      this.disFlag = true
      const list = []
      this.canVirtualList.forEach(item => {
        const flag = resume(item.id)
          .then((res) => {
            res.name = '恢复' + item.facilityName + '， '
            this.$parent.msgList = res
            this.$emit('msgShow')
            return res
          })
          .catch((err) => {
            console.log(err)
          })
        list.push(flag)
      })
      Promise.all(list).then(res => {
        this.disFlag = false
        if (this.canVirtualList && this.canVirtualList.length) {
          this.$emit('updateList')
        }
        this._closeDialog()
      })
    },
    _closeDialog() {
      this.dialogOpenFlag = false
      this.$parent.openDialog = false
    }
  }
}
</script>
