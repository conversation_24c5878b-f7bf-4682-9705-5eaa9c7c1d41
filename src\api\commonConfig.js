import request from "@/utils/request";

export function getCommonConfigList(params) {
  return request({
    url: process.env.ADMIN_API + "/common/config/getAllList",
    method: "post",
    data: params
  });
}

export function addCommonConfig(params) {
  return request({
    url: process.env.ADMIN_API + "/common/config/add",
    method: "post",
    data: params
  });
}

export function editCommonConfig(params) {
  return request({
    url: process.env.ADMIN_API + "/common/config/edit",
    method: "post",
    data: params
  });
}

export function deleteBatchCommonConfig(params, uid) {
  return request({
    url: process.env.ADMIN_API + "/common/config/delete/${uid}",
    method: "delete",
  });
}

export function stickCommonConfig(params) {
  return request({
    url: process.env.ADMIN_API + "/common/config/stick",
    method: "post",
    data: params
  });
}
