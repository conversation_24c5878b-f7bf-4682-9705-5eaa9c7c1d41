import request from "@/utils/request";

export function deleteMenu(param) {
    return request({
        url: process.env.ADMIN_API + "/communityMenu/delete",
        method: "post",
        data: param
    });
}

export function addMenu(param) {
    return request({
        url: process.env.ADMIN_API + "/communityMenu/add",
        method: "post",
        data: param
    });
}

export function editMenu(param) {
    return request({
        url: process.env.ADMIN_API + "/communityMenu/edit",
        method: "post",
        data: param
    });
}

export function stickeMenu(param) {
    return request({
        url: process.env.ADMIN_API + "/communityMenu/stick",
        method: "post",
        data: param
    });
}

export function getAllMenu(param) {
    return request({
        url: process.env.ADMIN_API + "/communityMenu/getAll",
        method: "get",
        params: param
    });
}

export function getListNotMe(param) {
    return request({
        url: process.env.ADMIN_API + "/communityMenu/getListNotMe",
        method: "get",
        params:param
    });
}

