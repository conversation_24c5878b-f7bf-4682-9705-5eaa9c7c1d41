<template>
  <div class="singel-course-set">
    <div class="mt-30">
      <el-form :model="courseForm" :rules="rules" ref="courseForm" label-width="80px" class="demo-courseForm"
        v-loading="editLoading">
        <el-form-item prop="videoUrl">
          <template slot="label">
            <span class="color-666">上传视频</span>
          </template>
          <template>
            <div class="flex-col">
             <!-- <div style="width:320px;height:240px;" class="upload_border" v-if="!courseForm.firstFrameUrl"
                @click="videoRoute('firstFrameUrl')">
                <i class="el-icon-plus"></i>
              </div>
              <template v-if="courseForm.firstFrameUrl">
                <video controls width="300" :src="courseForm.firstFrameUrl"></video>
                <el-button size="small" type="primary"
                  @click="videoRoute('firstFrameUrl')">更换视频</el-button>
              </template> -->
              <el-upload class="upload-demo" action="" :disabled="true">
                <div class="video-open cursor flex-center" @click="videoRoute('videoUrl')">
                  <div v-if="courseForm.videoUrl" class="w-100 h-100">
                    <video controls width="300" :src="courseForm.videoUrl"></video>
                   <!-- <el-image style="width: 100%;height: 100%;" :src="getBaseUrl(courseForm.firstFrameUrl)" fit="cover">
                    </el-image> -->
                  </div>
                  <div v-else class="flex-col">
                    <div class="flex-center">
                      <i class="font-24 color-main el-icon-plus"></i>
                    </div>
                    <span style="color: rgba(0,0,0,0.4);font-size: 12px;">点击选择视频</span>
                  </div>
                </div>
              </el-upload>
            </div>
          </template>
        </el-form-item>
        <el-form-item>
          <template slot="label">
            <span class="color-666">视频贴片</span>
          </template>
          <template>
            <div class="flex-col">
              <el-upload class="upload-demo"  action="" :disabled="true">
                <div class="img-open cursor flex-center" @click="settingRoute('picturePasterUid','picturePasterUrl')">
                  <div v-if="courseForm.picturePasterUrl" class="w-100 h-100">
                    <el-image style="width: 100%;height: 100%;" :src="courseForm.picturePasterUrl"
                      fit="cover">
                    </el-image>
                  </div>
                  <div class="flex-col" v-else>
                    <div class="flex-center">
                      <i class="font-24 color-main el-icon-plus"></i>
                    </div>
                    <span style="color: rgba(0,0,0,0.4);font-size: 12px;">点击选择图片</span>
                  </div>
                </div>
              </el-upload>
              <div class="color-999 font-12 mt-5" style="line-height: 22px;">
                如未设置视频贴片，将默认使用视频首帧作为贴片
              </div>
            </div>
          </template>
        </el-form-item>

        <el-form-item prop="curriculumName">
          <template slot="label">
            <span class="color-666">名称</span>
          </template>
          <template>
            <div class="line-input">
              <el-input v-model.trim="courseForm.curriculumName" placeholder="请输入名称" maxlength="40" show-word-limit
                style="width: 626px;"></el-input>
            </div>
          </template>
        </el-form-item>

        <el-form-item>
          <template slot="label">
            <span class="color-666">封面</span>
          </template>
          <template>
            <div class="flex-col">
              <el-upload class="upload-demo"  action="" :disabled="true">
                <div class="img-open cursor flex-center" @click="settingRoute('pictureUid','pictureUrl')">
                  <div v-if="courseForm.picturePasterUrl" class="w-100 h-100">
                    <el-image style="width: 100%;height: 100%;" :src="courseForm.pictureUrl"
                      fit="cover">
                    </el-image>
                  </div>
                  <div class="flex-col" v-else>
                    <div class="flex-center">
                      <i class="font-24 color-main el-icon-plus"></i>
                    </div>
                    <span style="color: rgba(0,0,0,0.4);font-size: 12px;">点击选择图片</span>
                  </div>
                </div>
              </el-upload>
              <div class="color-999 font-12 mt-5" style="line-height: 22px;">
                如未设置封面，将默认使用视频首帧作为封面
              </div>
            </div>
          </template>
        </el-form-item>

        <el-form-item prop="detail">
          <template slot="label">
            <span class="color-666">详情</span>
          </template>
          <template>
            <el-input type="textarea" placeholder="请输入内容" v-model="courseForm.detail"
              :autosize="{ maxRows: 10, minRows: 10 }" resize="none">
            </el-input>
          </template>
        </el-form-item>

        <el-form-item prop="sellWay">
          <template slot="label">
            <span class="color-666">售卖方式</span>
          </template>
          <template>
            <div class="color-666 font-14 flex-col" style="margin-top: 13px;">
              <el-radio-group v-model="courseForm.sellWay">
                <el-radio :label="0">免费</el-radio>
                <el-radio :label="1">付费</el-radio>
                <el-radio :label="2">加密</el-radio>
                <!-- <el-radio :label="3">指定学员</el-radio> -->
              </el-radio-group>
              <template v-if="courseForm.sellWay == 0">
                <div class="mt-20"><span class="color-666 font-14 mr-20">有效期</span><el-radio v-model="dateradio"
                    :label="9">长期有效</el-radio>
                </div>
              </template>

              <template v-if="courseForm.sellWay == 1">
                <div class="mt-20">
                  <el-form-item prop="sellingPrice">
                    <template slot="label">
                      <span class="color-666">商品价格</span>
                    </template>
                    <template>
                      <el-input placeholder="请输入" v-model.trim="courseForm.sellingPrice" style="width: 200px;">
                      </el-input><span class="ml-5">元</span>
                    </template>
                  </el-form-item>
                </div>
              </template>

              <template v-if="courseForm.sellWay == 2">
                <div class="mt-20">
                  <el-form-item prop="password">
                    <template slot="label">
                      <span class="color-666">密码设置</span>
                    </template>
                    <template>
                      <el-input placeholder="请设置3-12位数字，字母或汉字" v-model.trim="courseForm.password" style="width: 270px;">
                      </el-input>
                    </template>
                  </el-form-item>
                </div>
              </template>

              <template v-if="courseForm.sellWay == 3">
                <div class="mt-20">
                  <span class="color-999 font-14">仅被指定学员可免费学习课程，添加指定学员请前往【学员列表】手动操作</span>
                </div>
              </template>
            </div>
          </template>
        </el-form-item>

        <el-form-item prop="categoryUid">
          <template slot="label">
            <span class="color-666">知识体系</span>
          </template>
          <template>
            <div class="color-666 font-14 flex-col">
              <el-select v-model="courseForm.categoryUid" placeholder="请选择" style="width: 446px;">
                <el-option v-for="item in typeList" :key="item.uid" :label="item.name" :value="item.uid">
                </el-option>
              </el-select>
            </div>
          </template>
        </el-form-item>


        <el-form-item prop="name">
          <template slot="label">
            <span class="color-666">上架设置</span>
          </template>
          <template>
            <div class="color-666 font-14 flex-col" style="margin-top: 13px;">
              <el-radio-group v-model="shelvesradio">
                <el-radio :label="1">立即上架</el-radio>
                <el-radio :label="2">定时上架</el-radio>
                <el-radio :label="3">暂不上架</el-radio>
              </el-radio-group>
              <template>
                <div class="flex-col mt-15" v-if="shelvesradio != 3">
                  <span style="color: #C9CDD4;">更多设置</span>
                  <div class="flex">
                    <el-checkbox v-model="checked">定时下架</el-checkbox>
                    <div class="ml-10"> <el-date-picker v-model="date1" :key="500" type="datetime" placeholder="选择日期时间"
                        value-format="yyyy-MM-dd HH:mm:ss" :disabled="!checked">
                      </el-date-picker></div>
                  </div>
                </div>
              </template>
              <template v-if="shelvesradio == 2">
                <div class="flex-col mt-15">
                  <div>
                    <el-form-item prop="upTime">
                      <template slot="label">
                        <span class="color-666">上架时间</span>
                      </template>
                      <template>
                        <el-date-picker v-model="date2" type="datetime" :key="510" placeholder="选择日期时间"
                          value-format="yyyy-MM-dd HH:mm:ss">
                        </el-date-picker>
                      </template>
                    </el-form-item>
                  </div>
                  <span class="mt-15" style="color: #C9CDD4;">更多设置</span>
                  <div class="flex">
                    <el-checkbox v-model="checked1">定时下架</el-checkbox>
                    <div class="ml-10"> <el-date-picker v-model="date3" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择日期时间" :key="520" :disabled="!checked1">
                      </el-date-picker></div>
                  </div>
                </div>
              </template>
              <template v-if="shelvesradio == 3">
              </template>
            </div>
          </template>
        </el-form-item>


        <el-form-item prop="name">
          <template slot="label">
            <span class="color-666">隐藏设置</span>
          </template>
          <template>
            <div class="color-666 font-14 flex-col">
              <div class="flex ai-center"><el-checkbox v-model="courseForm.viewStatus">隐藏</el-checkbox> <span
                  class="color-999 font-14 ml-10">不可通过搜索或列表进行访问，仅通过链接式访问</span></div>
            </div>
          </template>
        </el-form-item>
        <el-form-item style="display: flex;justify-content: center;">
          <el-button class="el-button-width" @click="singleCourseCancel">取 消</el-button>
          <el-button class="el-button-width" type="primary" @click="submitForm('courseForm')">确 定</el-button>
        </el-form-item>
      </el-form>
      <HyMaterial :show.sync="dialogTableVisible" :type="2" @select="selectVideo" :limit="1" />
      <HyMaterial :show.sync="dialogTableVisible2" @select="selectPicture" :limit="1" />
    </div>
  </div>
</template>

<script>
import omit from 'lodash/omit'
import cloneDeep from 'lodash/cloneDeep'
import { uploadFile } from '@/api/file'
import { getCurriculumCategoryList } from '@/api/targetRange/list'
import HyMaterial from '@/components/HyMaterial/index.vue'
export default {
  name: 'HuanyuCollegeNuxtWebCourseSet',
  components: {
    HyMaterial
  },
  props: {
    singleCourseCancel: {
      type: Function,
      default: () => { }
    },
    submitSingleCourseForm: {
      type: Function,
      default: () => { }
    },
    courseDetail: {
      type: Object,
      default: () => ({})
    },
    editLoading: { // 表单数据回显加载中
      type: Boolean,
      default: false
    },
    increase: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const confirmUpdate = (relus, value, callback) => {
      if (this.shelvesradio == 2) {
        if (this.date2) {
          callback()
        } else {
          callback(new Error('请选择上架时间'))
        }
      } else {
        callback()
      }
    }
    const checkPrice = (rules, value, callback) => {
      if (value) {
        if (value == 0 || value == 0.0 || value == 0.00) {
          callback(new Error('价格不能为0'))
          return
        }
        let regNum = /^\d+(.\d{1,2})?$/;
        let regCh = /[\u4e00-\u9fa5]/g;
        if (regCh.test(value)) {
          callback(new Error('请勿输入中文'))
          return
        }
        if (!regNum.test(value)) {
          callback(new Error('请输入正确的格式，最多保留两位小数'))
          return
        }
        callback()
      } else {
        callback(new Error('请输入价格'))
        return
      }
    }
    return {
      // 上传文件所需参数
      imgUploadParams: {
        source: "picture",
        projectName: "blog",
        sortName: "web",
        token: "",
        userUid: ""
      },
      imgUploadHeaders: {
        Authorization: ""
      },
      courseForm: { // 新建视频课程表单
        curriculumName: "",
        categoryUid: "",
        summary: "",
        detail: "",
        pictureUid: "",
        pictureUrl: "",
        sellingPrice: "",
        curriculumType: "2",
        picturePasterUrl: "",
        videoUrl: "",
        videoUid: "",
        videoName: "",
        sellWay: 0,
        upRelease: "",
        downRelease: "",
        upTime: "",
        downTime: "",
        viewStatus: false,
        password: '',
        firstFrameUrl: '',
      },
      dialogTableVisible: false,
      dialogTableVisible2: false,
      videoUrl: '',
      videoName:'',
      rules: {
        // pictureUrl: [
        //   { required: true, message: '请上传视频封面', trigger: ['change', 'blur'] }
        // ],
        videoUrl: [
          { required: true, message: '请上传视频', trigger: ['change', 'blur'] }
        ],
        // picturePasterUrl: [
        //   { required: true, message: '请上传视频贴片', trigger: ['change', 'blur'] }
        // ],
        curriculumName: [
          { required: true, message: '请填写名称', trigger: ['change', 'blur'] }
        ],
        sellWay: [
          { required: true, message: '请填写售卖', trigger: ['change', 'blur'] }
        ],
        categoryUid: [
          { required: true, message: '请选择知识体系', trigger: ['change', 'blur'] }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: ['change', 'blur'] }
        ],
        detail: [
          { required: true, message: '请输入详情', trigger: ['change', 'blur'] }
        ],
        sellingPrice: [
          // { required: true, message: '请输入价格', trigger: ['change', 'blur'] },
          { required: true, validator: checkPrice, trigger: ['change', 'blur'] }
        ],
        upTime: [
          // { required: true, message: '请选择上架时间', trigger: ['change', 'blur'] },
          {
            required: true,
            validator: confirmUpdate,
            trigger: ["blur", "change"],
          },
        ],
      },
      shelvesradio: 1, // 上下架状态
      checked: false, // 是否定时下架
      checked1: false, // 是否定时下架
      date1: '', // 立即上架时的定时下架时间
      date2: '', // 定时上架的定时上架时间
      date3: '', // 定时上架的定时下架时间
      userInfo: {}, // 个人信息
      videoFirstimgsrc: '', // 截取后的视频封面
      uploadLoading: false,
      typeList: [], //知识体系数据
      dateradio: 9, //长期有效
      choiceDialog: false, // 选择视频或图片弹窗
      treeName: '', // 分组搜索值
      radio: '', // 列表中选中的值
      folderList: [{
        categoryName: '全部图片',
        uid: 'allMaterials',
        allNums: 0,
        child: [],
        defaultNums: 0,
      }],
      defaultProps: {
        children: 'child',
        label: 'categoryName'
      },
      treeForm: {
        name: '',
        type: 1
      },
      choiceList: [], // 选择列表数据
      hasNextPage: false, //选择列表数据是否具有下一页
      selectForm: {
        current: 1,// 选择列表数据页码
        size: 6, //选择列表数据每页条数
        name: '',
      },
      choiceParams: 'allMaterials', //选择列表数据的参数
      listLoading: false, // 列表加载中状态
      choiceType: '', // 当前选择的类型
      imgBasePath: '', //图片前缀
      firstFrame: '', // 视频第一帧图片
      uploadType: '', // 支持上传的类型
      localLoading: false, //本地上传loading
      videoTime: {}, // 视频时长，或者图片分辨率
      urlName: '',
      urlId: ''
    };
  },

  mounted() {
    this.getTypeList();
    this.imgBasePath = localStorage.getItem("localPictureBaseUrl") || ''
    // this.userInfo = this.$store.state.userInfo || null
    // this.imgUploadParams.userUid = this.userInfo?.uid || null
    // this.imgUploadParams.token = localStorage.getItem("token") || null
    // this.imgUploadHeaders.Authorization = localStorage.getItem("token") || null
    console.log(this.increase);
    if (Object.keys(this.courseDetail).length && this.increase) {
      console.log("xxxx", this.courseDetail)
      const data = JSON.parse(JSON.stringify(this.courseDetail));
      this.courseForm = JSON.parse(JSON.stringify({ ...this.courseDetail, viewStatus: data.viewStatus == 0 ? true : false }));
      this.shelvesradio = data.shelvesradio
      this.firstFrame = data.firstFrame
      switch (data.status) {
        case 0:
        case 1:
          this.shelvesradio = 1;
          break;
        case 2:
          this.shelvesradio = 2;
          break;
      }
      this.date1 = data.downTime
      this.date2 = data.upTime
      this.date3 = data.downTime
      this.date3 && (this.checked1 = true)
    }
    // this.getTypeList()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  watch: {
    'courseForm.sellWay': {
      handler: function (val) {
        let formName = 'courseForm'
        if (val == 0 || val == 3) {
          this.courseForm.sellingPrice = ''
          this.courseForm.password = ''
          this.$refs[formName].clearValidate('sellingPrice')
          this.$refs[formName].clearValidate('password')
        } else if (val == 1) {
          this.courseForm.password = ''
          this.$refs[formName].clearValidate('password')
        } else if (val == 2) {
          this.courseForm.sellingPrice = ''
          this.$refs[formName].clearValidate('sellingPrice')
        }
      },
      deep: true
    },
    'shelvesradio': {
      handler: function (val) {
        // if (val == 1 || val == 3) {
        //   this.date2 = ''
        //   this.date3 = ''
        //   this.checked1 = false
        // } else if (val == 2) {
        //   this.checked = false
        // }
      },
      deep: true
    },
    'courseDetail': {
      handler: function (val) {
        // console.log("改变了", val);
        // if (Object.keys(val).length) {
        //   const data = val;
        //   this.courseForm = data
        //   this.shelvesradio = data.shelvesradio
        //   this.firstFrame = data.firstFrame
        //   this.checked = Boolean(data.date1)
        //   this.checked1 = Boolean(data.date3)
        //   this.date1 = data.date1
        //   this.date2 = data.date2
        //   this.date3 = data.date3
        // }
      },
      deep: true
    },
    'checked': {
      handler: function (val) {
        if (!val) {
          this.date1 = ''
        }
      },
      deep: true
    },
    'checked1': {
      handler: function (val) {
        if (!val) {
          this.date3 = ''
        }
      },
      deep: true
    }
  },
  methods: {
    videoRoute(url) {
      this.videoUrl = url
      this.dialogTableVisible = true
    },
    selectVideo(row) {
      console.log(row);
      if(!this.courseForm.pictureUrl) {
        this.courseForm.pictureUid = row[0].fileUid
        this.courseForm.pictureUrl = row[0].coverUrl
      }
      if(!this.courseForm.picturePasterUrl) {
        this.courseForm.picturePasterUrl = row[0].coverUrl
      }
      this.courseForm.firstFrameUrl = row[0].fileUrl
      this.courseForm[this.videoUrl] = row[0].fileUrl
      this.courseForm.videoUid = row[0].fileUid
    },
    settingRoute(id,url) {
      this.urlId = id
      this.urlName = url;
      this.dialogTableVisible2 = true
    },
    selectPicture(row) {
      this.courseForm[this.urlId] = row[0].fileUid
      this.courseForm[this.urlName] = row[0].pictureUrl
    },
    async handleTiepChange(file, fileList) {
      file = file.raw;
      let limit = file.size / 1024 / 1024;
      //大于500M
      if (limit > 150) {
        this.$message({
          offset: 100,
          type: "warning",
          message: "您上传的文件超过了150M了",
        });
        return;
      }
      let formData = new FormData();
      formData.append("file", file);
      formData.append("userUid", "uid00000000000000000000000000000000");
      formData.append("userUid", "uid00000000000000000000000000000000");
      formData.append("source", "picture");
      formData.append("sortName", "admin");
      formData.append("projectName", "blog");
      let result = await uploadFile(formData)
      //上传成功
      if (result.code === this.$ECode.SUCCESS) {
        let { picUrl, uid } = result.data[0];
        this.courseForm.picturePasterUrl = picUrl;
      }
    },
    // 上传视频
    async handleVideoChange(file, fileList) {
      file = file.raw;
      let limit = file.size / 1024 / 1024;
      //大于500M
      if (limit > 150) {
        this.$message({
          offset: 100,
          type: "warning",
          message: "您上传的文件超过了150M了",
        });
        return;
      }
      let formData = new FormData();
      formData.append("file", file);
      formData.append("userUid", "uid00000000000000000000000000000000");
      formData.append("userUid", "uid00000000000000000000000000000000");
      formData.append("source", "picture");
      formData.append("sortName", "admin");
      formData.append("projectName", "blog");
      let result = await uploadFile(formData)
      console.log("xx", result)
      //上传成功
      if (result.code === this.$ECode.SUCCESS) {
        let { picUrl, uid } = result.data[0];
        this.courseForm.videoUrl = picUrl;
        this.courseForm.videoUid = uid;
        this.courseForm.videoName = file.name;
        this.findvideocover(this.imgBasePath + picUrl);
      }
    },
    handleVideoExceed() {

    },
    //获取知识体系数据
    getTypeList() {
      getCurriculumCategoryList().then(res => {
        this.typeList = [...res.data]
      }).catch(err => { })
    },
    // 保存操作
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 防抖
          const data = JSON.parse(JSON.stringify(this.courseForm));
          console.log("data", data);
          data.viewStatus = data.viewStatus ? '0' : '1';
          data.upRelease = this.shelvesradio == 1 ? '0' : this.shelvesradio == 3 ? '0' : '1'
          data.downRelease = this.shelvesradio == 3 ? 0 : (this.checked || this.checked1 ? '1' : '0')
          data.downTime = this.shelvesradio == 1 ? this.date1 : this.shelvesradio == 2 ? this.date3 : ''
          data.upTime = this.shelvesradio == 2 ? this.date2 : ''
          data.status = this.shelvesradio == 1 || this.shelvesradio == 2 ? '1' : 0
          if (data.uid) {
            let params = omit(cloneDeep(data), ['designation', 'studyStatus', 'payStatus', 'buyNum', 'snowId', 'topoImage', 'topology', 'Designation', 'firstFrame', 'shelvesradio', 'date1', 'date2', 'date3', 'subscription']) // 过滤掉不需要的属性
            data.uid = data.uid || ''
            this.submitSingleCourseForm(params);
          } else {
            this.submitSingleCourseForm(data);
          }
        }
      })
    },
    // 获取树形数据
    getTreeList() {
      const data = JSON.parse(JSON.stringify(this.treeForm))
      this.$request.materialCenter.categoList(data).then(res => {
        if (res.data.code == 200) {
          this.folderList[0].categoryName = '全部' + { 1: '视频', 2: '图片' }[this.treeForm.type]
          // this.folderList[0].child = res.data.data.records
          let defaultGroup = {}
          this.folderList[0].allNums = 0
          this.folderList[0].defaultNums = 0
          let list = []
          res.data.data.records.forEach(item => {
            this.folderList[0].allNums += item.total
            if (item.defaultCatalog == 1) {
              this.folderList[0].defaultNums++
              defaultGroup = { ...item, defaultNums: this.folderList[0].defaultNums }
            } else if (item.addType == 1) {
              this.folderList[0].defaultNums++
              list.push({ ...item, defaultNums: this.folderList[0].defaultNums })
            }
          })
          this.folderList[0].child = [defaultGroup, ...list, ...res.data.data.records.filter(item => item.defaultCatalog == 0 && item.addType != 1)]
        }
      })
    },
    // 获取选择的列表数据
    getChoiceList() {
      // this.listLoading = true
      // const data = {
      //   current: this.selectForm.current,
      //   size: this.selectForm.size,
      //   status: 1,
      //   superUid: this.choiceParams == 'allMaterials' ? '' : this.choiceParams,
      //   name: this.selectForm.name
      // }
      // const requestFun = this.treeForm.type == 1 ? 'videoList' : 'materialList'
      // this.$request.materialCenter[requestFun](data).then(res => {
      //   if (res.data.code == 200) {
      //     const finalData = res.data.data
      //     this.choiceList = this.choiceList.concat(finalData.records)
      //     this.hasNextPage = finalData.records?.length == finalData.size && finalData.total > finalData.size && finalData.total > this.choiceList.length
      //     if (this.choiceList.length) {
      //       this.$nextTick(() => {
      //         const ele = document.querySelector('.chioce-area')
      //         ele && ele.addEventListener('scroll', this.handleScroll)
      //       })
      //     }
      //   }
      // }).finally(() => {
      //   this.listLoading = false
      // })

    },
    // 提交选择
    subChoice() {
      if (!this.radio) {
        this.$message.warning(`请选择${this.treeForm.type == 1 ? '视频' : '图片'}`)
        return
      }
      if (this.treeForm.type == 2) {
        if (this.choiceType == 'paster') {
          const img = this.choiceList.find((item) => item.uid == this.radio)
          this.courseForm.picturePasterUrl = img && img.fileUrl
          this.choiceDialog = false
        } else if (this.choiceType == 'cover') {
          const img = this.choiceList.find((item) => item.uid == this.radio)
          this.courseForm.pictureUrl = img && img.fileUrl
          this.courseForm.pictureUid = img && img.fileUid
          this.choiceDialog = false
        }
      } else if (this.treeForm.type == 1) {
        const video = this.choiceList.find((item) => item.uid == this.radio)
        this.courseForm.videoUrl = video && video.fileUrl
        this.courseForm.curriculumName = video && video.name
        this.findvideocover((this.imgBasePath + video.fileUrl))
        this.choiceDialog = false
      }
    },
    // 打开选择视频或者图片弹窗
    openChoice(choiceType, type) {
      let inputDom = document.cre
    },
    // 选择树形某一个时
    handleNodeClick(data) {
      this.radio = ''
      this.selectForm.current = 1
      this.choiceList = []
      this.choiceParams = data.uid
      this.getChoiceList()
    },
    // 取消选择
    cancelChoice() {
      this.radio = ''
      this.choiceDialog = false
    },
    // 上传视频图片分组搜索
    realSearch(searchType) {
      this.choiceList = []
      this.selectForm.current = 1
      if (searchType == 'tree') {
        this.getTreeList()
      } else if (searchType == 'content') {
        this.getChoiceList()
      }
    },
    getBaseUrl(url) {
      return (localStorage.getItem('localPictureBaseUrl') + url)
    },
    //截取视频第一帧作为播放前默认图片
    findvideocover(url, file) {
      this.uploadLoading = true
      // const  video = document.getElementById("myvideo"); // 获取视频对象
      let finalluUrl = ''
      const video = document.createElement("video") // 也可以自己创建video
      video.src = url // url地址 url跟 视频流是一样的

      // var canvas = document.getElementById('mycanvas') // 获取 canvas 对象
      var canvas = document.createElement('canvas') // 获取 canvas 对象
      const ctx = canvas.getContext('2d'); // 绘制2d
      video.crossOrigin = 'anonymous' // 解决跨域问题，也就是提示污染资源无法转换视频
      video.currentTime = 1 // 第一帧

      video.oncanplay = () => {
        // console.log(video.clientWidth, video.clientHeight);
        canvas.width = video.clientWidth ? video.clientWidth : 320; // 获取视频宽度
        canvas.height = video.clientHeight ? video.clientHeight : 320; //获取视频高度
        // 利用canvas对象方法绘图
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        // 转换成base64形式
        this.videoFirstimgsrc = canvas.toDataURL("image/png"); // 截取后的视频封面
        // file.url = this.videoFirstimgsrc;
        // base64转成bolb文件
        const fileBolb = this.base64toFile(this.videoFirstimgsrc)
        // 把首图上传生成云上地址
        finalluUrl = this.getFirstPngUrl(fileBolb);
        video.remove();
        canvas.remove();
        return finalluUrl
      }
    },

    base64toFile(baseUrl, filename = 'file') {
      let arr = baseUrl.split(',');
      let type = arr[0].match(/:(.*?);/)[1];   // 解锁图片类型
      let bytes = atob(arr[1]); // 解码base64
      let n = bytes.length
      let bufferArray = new Uint8Array(n);
      while (n--) {
        bufferArray[n] = bytes.charCodeAt(n);
      }
      return new File([bufferArray], filename, { type });

    },

    async getFirstPngUrl(file) {
      let formData = new FormData();
      formData.append("file", file);
      formData.append("userUid", "wehsbanmsbdwehqwie");
      formData.append("source", "picture");
      formData.append("sortName", "web");
      formData.append("projectName", "blog");
      // 再次上传文件后，拿到图片地址
      await uploadFile(formData).then((res) => {
        if (res.code == this.$ECode.SUCCESS) {
          const data = res.data[0]
          this.firstFrame = data.url;
          if (!this.courseForm.picturePasterUrl) {
            this.courseForm.picturePasterUrl = data.picUrl
          }
          if (!this.courseForm.pictureUrl) {
            this.courseForm.pictureUrl = data.picUrl;
            this.courseForm.pictureUid = data.uid;
          }
          this.courseForm.firstFrameUrl = data.picUrl;
          this.uploadLoading = false;
        }
      }).finally(() => {
        this.uploadLoading = false;
      })
    },

    getFileName(file) {
      const type = file.raw.type.split("/")[1];
      let name = '';
      if (type) {
        name = file.raw.name.substring(0, file.raw.name.indexOf(`.${type}`));
      }
      return `${name}.png`;
    },
    // 开启本地上传
    localUpload() {
      this.$refs.uploadRef.$children[0].handleClick()
    },
    // 上传本地文件前
    beforeUpload(file) {
      const suffix = String(file.name).substring(String(file.name).lastIndexOf('.') + 1)
      if (this.treeForm.type == 1) {
        let types = ['mp4']
        let sizeM = 1024 * 1024 * 150 < file.size
        if (!types.includes(suffix)) {
          this.$message.warning('视频上传格式只支持mp4')
          return false
        }
        if (sizeM) {
          this.$message.warning('视频文件大小不能超过150M')
          return false
        }
      } else if (this.treeForm.type == 2) {
        let types = ['png', 'jpg', 'jpeg']
        let sizeM = 1024 * 1024 * 2 < file.size
        if (!types.includes(suffix)) {
          this.$message.warning('图片上传格式只支持png,jpeg,jpg')
          return false
        }
        if (sizeM) {
          this.$message.warning('图片文件大小不能超过2M')
          return false
        }
      }
      this.localLoading = true
    },
    // 上传本地文件失败
    uploadEorror() {
      this.$message.warning('文件上传失败')
      this.localLoading = false
      this.choiceDialog = false
    },
    // 本地文件上传成功
    uploadImgSuccess(res, file) {
      if (res.code == 200) {
        this.radio = ''
        const data = res.data[0]
        if (this.treeForm.type == 2) {
          if (this.choiceType == 'paster') {
            this.courseForm.picturePasterUrl = data.picUrl
            this.choiceDialog = false
          } else if (this.choiceType == 'cover') {
            this.courseForm.pictureUrl = data.picUrl
            this.courseForm.pictureUid = data.uid
            this.choiceDialog = false
          }
        } else if (this.treeForm.type == 1) {
          this.courseForm.videoUrl = data.picUrl
          this.courseForm.curriculumName = String(file.name).substring(0, String(file.name).lastIndexOf('.'))
          this.findvideocover((this.imgBasePath + data.picUrl))
          this.choiceDialog = false
        }
      }
      this.localLoading = false
    },
    // 监听滚动
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e && e.target
      if (Math.ceil(scrollHeight) - Math.ceil(scrollTop) <= Math.ceil(clientHeight)) {
        if (this.hasNextPage) {
          this.selectForm.current++
          this.getChoiceList()
        }
      }
    },
    // 选择某一项视频或者图片
    choiceItem(uid) {
      if (this.radio == uid) {
        this.radio = ''
      } else {
        this.radio = uid
      }
    },
    getVideoTime(row) {
      setTimeout(() => {
        if (this.treeForm.type == 1) {
          if (!document.getElementById(`video-${row.uid}`)) return
          const time = document.getElementById(`video-${row.uid}`).duration
          let resultTime = this.$getHMSTime(time)
          if (time && time > 0) this.$set(this.videoTime, row.uid, `${resultTime[0]}:${resultTime[1]}:${resultTime[2]}`)
        } else {
          var img = new Image()
          img.src = this.imgBasePath + row.fileUrl
          if (img.width && img.height) this.$set(this.videoTime, row.uid, img.width + 'x' + img.height)
        }
      }, 800)
    },
    goToPage(url) {
      if (!url) return
      this.$router.push({
        path: url
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.singel-course-set {

  .video-open {
    border: 1px dashed #d9d9d9;
    width: 160px;
    height: 120px;
    line-height: 22px;
    border-radius: 6px;
  }

  .video-open:hover {
    border-color: #409EFF;
  }

  .img-open {
    border: 1px dashed #d9d9d9;
    width: 160px;
    height: 120px;
    line-height: 22px;
    border-radius: 6px;
  }

  .img-open:hover {
    border-color: #409EFF;
  }

  .line-input {
    /deep/ .el-input__inner {
      height: 44px !important;
    }
  }

  /deep/ .el-select {
    height: 44px;

    .el-input {
      height: inherit;

      .el-input__inner {
        height: inherit;
      }
    }
  }

  .hy-dialog {
    .el-button--small {
      padding: 0px !important;
      font-size: 14px;
      width: 120px;
      height: 36px;
      line-height: 36px;
      border-radius: 3px;
    }

    .tree-content {
      overflow: auto;

      // 滚动条整体样式
      &::-webkit-scrollbar {
        width: 4px !important;
        /* 纵向滚动条 宽度 */
        border-radius: 5px;
        /* 整体 圆角 */
      }

      //轨道部分
      &::-webkit-scrollbar-track {
        background: #fff !important;
      }

      // 滑块部分
      &::-webkit-scrollbar-thumb {
        background: #D4DBE0;
        min-height: 167px;
        width: 2px !important;
        border-radius: 5px;
      }

      scrollbar-width: thin !important;
      /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      -ms-overflow-style: none !important;

      /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
    }

    .chioce-area {
      overflow: auto;

      .choice-content {

        // /deep/ .el-radio-group {
        //   display: flex;
        //   flex-direction: row;
        //   flex-wrap: wrap;
        // }

        .choice-item {
          width: 31%;
          border-radius: 5px;
          height: 205px;
          position: relative;

          .item-cover {
            height: 140px;
          }

          .my-radio {
            position: absolute;
            top: 10px;
            right: 10px;
            display: none;

            /deep/ .el-radio__label {
              display: none;
            }
          }
        }

        .active-choice {
          border: 1px solid sandybrown;

          .my-radio {
            display: inline-block;
          }
        }

        .choice-item:hover {
          border: 1px solid sandybrown;

          .my-radio {
            display: inline-block;
          }
        }
      }

      // 滚动条整体样式
      &::-webkit-scrollbar {
        width: 4px !important;
        /* 纵向滚动条 宽度 */
        border-radius: 5px;
        /* 整体 圆角 */
      }

      //轨道部分
      &::-webkit-scrollbar-track {
        background: #fff !important;
      }

      // 滑块部分
      &::-webkit-scrollbar-thumb {
        background: #D4DBE0;
        min-height: 167px;
        width: 2px !important;
        border-radius: 5px;
      }

      scrollbar-width: thin !important;
      /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      -ms-overflow-style: none !important;

      /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
    }

  }


}

.upload_border {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #8c939d;
    cursor: pointer;

    .el-icon-plus {
      font-size: 30px;
      color: #8c939d;
    }
  }
</style>
