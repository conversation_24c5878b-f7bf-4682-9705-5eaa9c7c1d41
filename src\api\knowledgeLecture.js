import request from "@/utils/request";

//字典
export function getDictionaryList(params) {
  return request({
    url: process.env.ADMIN_API + "/course/price/range",
    method: "get",
    params
  });
}
export function addSysDictData(params) {
  return request({
    url: process.env.ADMIN_API + "/sysDictData/add",
    method: "post",
    data: params
  });
}

export function editSysDictData(params) {
  return request({
    url: process.env.ADMIN_API + "/sysDictData/edit",
    method: "post",
    data: params
  });
}

export function deleteBatchSysDictData(params) {
  return request({
    url: process.env.ADMIN_API + "/sysDictData/deleteBatch",
    method: "post",
    data: params
  });
}
//知识学堂工作台统计
export function workbenchStatistics(params = {}) {
  return request({
    url: process.env.ADMIN_API + "/course/price/getCurriculumCount",
    method: "get",
    params
  });
}
