<template>
    <div class="test-paper-list">
        <el-row type="flex" justify="end">
            <div>
                <el-form :inline="true" :model="formData" size="medium">
                    <el-form-item>
                        <el-input v-model="formData.name" clearable prefix-icon="el-icon-search" placeholder="请输入关键词搜索"
                            @change="initTestPaperData()" />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="initTestPaperData()">搜索</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-row>
        <el-row :gutter="20">
            <!-- 分类 -->
            <el-col :span="6">
                <!-- 分类 -->
                <categoryTree ref="categoryTree" :isAllowConfig="false" :type="1" @change="handleCategoryTree" />
            </el-col>
            <!-- 表格 -->
            <el-col :span="18">
                <el-table height="400" v-loading="loading" ref="testPaperTable" :data="tableData" style="width:100%"
                    highlight-current-row
                    @current-change="handleSelectionChange">
                    <el-table-column label="试卷" prop="name" />
                    <el-table-column prop="hasRandom" width="100">
                        <template slot="header" slot-scope="scope">
                            随机选题
                            <el-popover placement="right" width="320" trigger="hover">
                                <div>随机试卷开启：随机抽取题目数量/总题目数量</div>
                                <div>随机试卷关闭：总题目数量</div>
                                <i slot="reference" class="el-icon-info" style="cursor: pointer;color: #ccc" />
                            </el-popover>
                        </template>
                        <template slot-scope="{ row }">
                            {{ row.hasRandom == 1 ? '开启' : '关闭' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="题目" prop="titleNum" />
                    <el-table-column label="总分" prop="score" />
                    <el-table-column label="状态" prop="status">
                        <template slot-scope="scope">
                            {{ scope.row.status == 1 ? '未删除' : '已删除' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" prop="updateTime" width="160" />
                </el-table>
                <el-row type="flex" justify="space-between" style="margin-top:15px;">
                    <div>
                        <el-pagination :disabled="loading" :current-page.sync="currentPage"
                            :page-sizes="[15, 20, 50, 100]" :page-size.sync="pageSize" :total="total"
                            layout="total, sizes, prev, pager, next, jumper" @size-change="initTestPaperData(false)"
                            @current-change="initTestPaperData(true)" />
                    </div>
                </el-row>
            </el-col>
        </el-row>
    </div>
</template>

<script>
// 添加试卷组件
import addTestPaper from '@/views/content/components/testPaperList/addTestPaper.vue'
import categoryTree from '@/views/content/components/categoryTree/index.vue'
import categoryList from '@/views/content/components/categoryList/index.vue'
import dialogPreview from '@/views/content/components/testPaperList/dialogPreview.vue'
// 接口
import {
    getTestPaperList,
    copyTestPaper
} from '@/api/content/testPaperList'
// export
export default {
    name: 'TestPaper',
    components: {
        addTestPaper,
        categoryTree,
        categoryList,
        dialogPreview
    },
    data() {
        return {
            formData: {
                name: ''
            },
            // 列表数据
            loading: false,
            tableData: [],
            currentPage: 1,
            pageSize: 15,
            total: 0,
            categoryUid: '',
            currentSelectData: null
        }
    },
    created() {
        // 获取列表数据
        this.getTestPaperData()
    },
    methods: {
        // 点击分类触发
        handleCategoryTree(data) {
            if (data.uid) {
                this.categoryUid = data.uid
                this.initTestPaperData()
            }
        },
        // 监听选择试卷
        handleSelectionChange(data) {
            this.currentSelectData = data;
            this.$emit("update:currentSelectPapers", data);
        },
        initTestPaperData(hasPage) {
            if (!hasPage) this.currentPage = 1
            this.total = 0
            this.tableData = []
            this.getTestPaperData()
        },
        async getTestPaperData() {
            const params = {
                currentPage: this.currentPage, // 当前页
                pageSize: this.pageSize, // 页大小
                status:1
            }
            // 试卷分类uid
            if (this.categoryUid) params.categoryUid = this.categoryUid
            // 试卷名称
            if (this.formData.name) params.name = this.formData.name
            // 请求数据
            this.loading = true
            const res = await getTestPaperList(params)
            this.loading = false
            // 判断
            if (res.code === this.$ECode.SUCCESS) {
                this.tableData = res.data.records || []
                this.currentPage = res.data.current || 1
                this.total = res.data.total || 0
            } else {
                this.$commonUtil.message.error(res.message)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.test-paper-list {
    /deep/ .el-table thead .el-table__cell {
        background-color: #f6f8fa;
    }
}
</style>
