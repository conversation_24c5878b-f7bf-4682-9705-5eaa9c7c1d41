import request from '@/utils/request'

// 添加讲者分类
export function addCategory(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/category/add',
        method: 'post',
        data: params
    })
}

// 编辑讲者分类
export function editCategory(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/category/edit',
        method: 'post',
        data: params
    })
}


// 获取分类列表
export function getList(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/category/getList',
        method: 'post',
        data: params
    })
}

// 获取分类列表
export function deleteBatch(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/category/deleteBatch',
        method: 'post',
        data: params
    })
}

// 添加讲者
export function addSpeaker(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/add',
        method: 'post',
        data: params
    })
}

// 讲者分页列表
export function getSpeakerList(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/getList',
        method: 'post',
        data: params
    })
}

// 删除讲者
export function speakerDeleteBatch(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/deleteBatch',
        method: 'post',
        data: params
    })
}

// 编辑讲者
export function speakerEdit(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/edit',
        method: 'post',
        data: params
    })
}

// 编辑讲者
export function speakerUpdateTop(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/updateTop',
        method: 'post',
        data: params
    })
}

// 讲者粉丝列表
export function fansList(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/live/fans/list',
        method: 'post',
        data: params
    })
}

// 讲者拖拽排序
export function dragSpeakerSort(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/sort',
        method: 'post',
        data: params
    })
}

// 关联首页banner列表
export function getBannerList(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/getBannerList',
        method: 'get',
        params
    })
}

// 取消关联banner
export function speakerBinding(params) {
    return request({
        url: process.env.ADMIN_API + '/classroom/speaker/binding',
        method: 'post',
        data:params
    })
}

// （更换实验室创建人）讲者个人列表
export function selectCreatorByUid(params) {
    return request({
        url: process.env.ADMIN_API + `/classroom/enterprise/selectCreator/${params.enterpriseUid}`,
        method: 'get',
        params
    })
}


// 资讯媒体工作台发布指令者动态时获取讲者列表
export function getTrendSpeakerList(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/getSpeakerList`,
        method: 'post',
        data
    })
}

// 获取指令者动态列表
export function getTrendList(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/selectPageList`,
        method: 'post',
        data
    })
}

// 指令者动态根据讲者id来获取该讲者对应加入的企业
export function getEnterpriseListBySpeakerUid(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/getEnterpriseBySpeakerUid`,
        method: 'post',
        params:data
    })
}

// 添加（发布）指令者动态
export function incrementTrend(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/add`,
        method: 'post',
        data
    })
}

// 删除指令者动态
export function deleteTrend(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/deleteList`,
        method: 'post',
        data
    })
}

// 审核指令者动态
export function examineTrend(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/updateManualStatus`,
        method: 'post',
        data
    })
}

// 上下架指令者动态
export function updateTrendStatus(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/updateLaboratoryState`,
        method: 'post',
        data
    })
}

// 根据指令者动态uid来获取指令者动态详情信息
export function getTrendDetailByUid(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/selectLaboratoryStateByUid`,
        method: 'post',
        params:data
    })
}

// 根据指令者动态uid来编辑指令者动态
export function editTrendDetailByUid(data) {
    return request({
        url: process.env.ADMIN_API + `/admin/laboratoryState/updateLaboratoryStateById`,
        method: 'post',
        data
    })
}
