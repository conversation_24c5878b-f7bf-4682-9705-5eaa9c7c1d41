<template>
  <div v-loading="graphLoading" :element-loading-text="graphLoadingText" class="_tp_container">
    <div class="_tp_tools">
      <div v-if="showSave" class="_tp_operation_column">
        <div>
          <img src="@/assets/reset.png" alt="" title="居中" @click="moveView">
          <img v-if="enabled" src="@/assets/choicegrouping.png" alt="" title="分组" @click="enabledFn">
          <img v-else src="@/assets/grouping.png" alt="" title="分组" @click="enabledFn">
        </div>
        <div>
          <img v-if="currentArrow == '1'" src="@/assets/choicestraightline.png" alt="" title="直线">
          <img v-else src="@/assets/straightline.png" alt="" title="直线" @click="changeEdgeType('normal')">
          <img v-if="currentArrow == '2'" src="@/assets/choicecurve.png" title="曲线" alt="">
          <img v-else src="@/assets/curve.png" alt="" title="曲线" @click="changeEdgeType('smooth')">
          <img v-if="currentArrow == '3'" src="@/assets/choicebrokenline.png" title="直角" alt="">
          <img v-else src="@/assets/brokenline.png" alt="" title="直角" @click="changeEdgeType('')">
        </div>
        <div>
          <img src="@/assets/enlarge.png" alt="" title="放大" @click="setZoom(graph,0.1)">
          <img src="@/assets/narrow.png" alt="" title="缩小" @click="setZoom(graph,-0.1)">
        </div>
      </div>
      <el-button v-if="showSave" type="primary" size="mini" @click="realTopo">确定</el-button>
      <el-button type="danger" size="mini" @click="cancel">取消</el-button>
      <!-- <el-button v-if="showSave" type="primary" size="mini" @click="handleSaveTopo">保存</el-button> -->
      <!-- <el-button v-if="showStart" type="primary" size="mini" @click="handleStartTopo">启动</el-button>
      <el-button v-if="showRelease" type="primary" size="mini" @click="handleReleaseTopo">释放</el-button> -->
    </div>
    <div v-show="showControl" ref="_tp_control" class="_tp_control" />
    <div ref="_tp_graph" class="_tp_graph"/>
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      title="选择您需要链接端口"
      width="50%">
      <div class="_port_choice">
        <div v-if="sourceNode">
          <div v-if="sourceNode.data.facilityCategoryId === 102187 || sourceNode.data.facilityCategory === 102188 || sourceNode.data.facilityCategory === 102364">
            <div v-if="sourceNode.data.facilityPhysicsDetailVoList.length != 0">
              <el-select v-model="value" :multiple="targetNode.data.sourceMultiple" collapse-tags placeholder="请选择">
                <el-option
                  v-for="item in sourceNode.data.facilityPhysicsDetailVoList"
                  :key="item.id"
                  :label="item.portName"
                  :value="item.id"
                  :disabled="item.disabled || value.length == targetNum"/>
              </el-select>
            </div>
            <div v-else>
              该节点没有链接端口
            </div>
          </div>
          <div v-else>
            该节点没有链接端口
          </div>
        </div>
        <div v-if="targetNode">
          <div v-if="targetNode.data.facilityCategoryId === 102187 || targetNode.data.facilityCategory === 102188 || targetNode.data.facilityCategory === 102364">
            <div v-if="targetNode.data.facilityPhysicsDetailVoList.length != 0">
              <el-select v-model="value1" :multiple="targetNode.data.targetMultiple" collapse-tags placeholder="请选择">
                <el-option
                  v-for="item in targetNode.data.facilityPhysicsDetailVoList"
                  :key="item.id"
                  :label="item.portName"
                  :value="item.id"
                  :disabled="item.disabled || value1.length == sourceNum"/>
              </el-select>
            </div>
            <div v-else>
              该节点没有链接端口
            </div>
          </div>
          <div v-else>
            该节点没有链接端口
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="portDetermine">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer
      :visible.sync="showEquipmentDrawer"
      :direction="direction"
      :modal="false"
      size="23%"
      title="设备详情">
      <div class="_topo_info_">
        <editIntegration
          v-if="showEquipmentDrawer"
          :form-data="equipmentInfoData"
          @preservation="_preservation"
        />
      </div>
    </el-drawer>
    <!-- 创建终端或者应用服务器 -->
    <create-dialog
        v-if="terminalModalVisible"
        :dialog-visible="terminalModalVisible"
        title="选择参数配置"
        @obtainData="getTerminalData"
        @deleteAddedNode="_deleteAddedNode"
      />
    <el-dialog
      append-to-body
      :visible.sync="dialogVisibleSwitch"
      :before-close="_deleteAddedNode"
      title="交换机参数配置"
      width="40%">
      <div>
        <el-form ref="addSwitchFrom" :model="addSwitchFrom" :rules="addSwitchFromRules" label-width="100px">
          <el-form-item label="交换机名称" prop="name">
            <el-input v-model="addSwitchFrom.name" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="IP版本" prop="ip_version">
            <el-select v-model="addSwitchFrom.ip_version" placeholder="请选择">
              <el-option label="4" value="4"/>
              <el-option label="6" value="6"/>
            </el-select>
          </el-form-item>
          <el-form-item label="网络地址" prop="subnet_addr">
            <el-input v-model="addSwitchFrom.subnet_addr" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="开启网关" prop="enable_gateway_ip">
            <el-radio-group v-model="addSwitchFrom.enable_gateway_ip">
              <el-radio-button :label="true">开启</el-radio-button>
              <el-radio-button :label="false">关闭</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="addSwitchFrom.enable_gateway_ip" label="网关地址">
            <el-input v-model="addSwitchFrom.gateway_ip" placeholder="请输入内容"/>
          </el-form-item>
          <el-form-item label="开启DHCP" prop="enable_dhcp">
            <el-radio-group v-model="addSwitchFrom.enable_dhcp">
              <el-radio-button :label="true">开启</el-radio-button>
              <el-radio-button :label="false">关闭</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="域名解析服务">
            <el-input v-model="addSwitchFrom.dns_nameservers_string" placeholder="请输入内容"/>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="_deleteAddedNode">取 消</el-button>
        <el-button type="primary" @click="submitForm('addSwitchFrom')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="dialogVisibleRouter"
      :before-close="_deleteAddedNode"
      title="选择参数配置"
      width="30%">
      <div>
        <el-form ref="addRouterFrom" :model="addRouterFrom" :rules="addRouterFromRules" label-width="100px">
          <el-form-item label="路由器名称" prop="name">
            <el-input v-model="addRouterFrom.name" placeholder="请输入内容"/>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="_deleteAddedNode">取 消</el-button>
        <el-button type="primary" @click="submitForm('addRouterFrom')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { createPhysicalNodes, createVirtualNodes } from './components/nodes'
import { Graph, Shape } from '@antv/x6'
import { Group } from './components/shape'
import createDialog from '@/components/dialogGroup/createDisposeDialog'
import editIntegration from '@/components/sleps/editIntegration'
import buildStencil from './components/stencil'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Selection } from '@antv/x6-plugin-selection'
import { Export } from '@antv/x6-plugin-export'

export default {
  components: {
    createDialog,
    editIntegration
  },
  props: {
    options: {
      type: Object
    }
  },
  data() {
    return {
      timeStamp:'',
      graph: null,
      cell: null,
      addSwitchFrom: {
        name: '', // 网络名称
        ipv6_pd_enabled: false, // 是否开启IPV6前缀代理
        subnet_name: '', // 子网名称
        ip_version: '', // IP版本：4为IPV4，6为IPV6
        subnet_addr: '10.0.0.0/24', // 网络地址
        enable_gateway_ip: true, // 是否开启网关：true 开启，false 禁用
        gateway_ip: 'auto', // 网关地址
        enable_dhcp: true, // 是否开启DHCP：true 开启，false 禁用
        dns_nameservers_string: '***************' // 域名解析服务："*******\n14.14.14.14"
      },
      addRouterFrom: {
        name: ''
      },
      ruleForm_modify: {
        name: '', // 网络名称
        ipv6_pd_enabled: false, // 是否开启IPV6前缀代理
        subnet_name: '', // 子网名称
        ip_version: '', // IP版本：4为IPV4，6为IPV6
        subnet_addr: '10.0.0.0/24', // 网络地址
        enable_gateway_ip: true, // 是否开启网关：true 开启，false 禁用
        gateway_ip: 'auto', // 网关地址
        enable_dhcp: true, // 是否开启DHCP：true 开启，false 禁用
        dns_nameservers_string: '' // 域名解析服务："*******\n14.14.14.14"
      },
      addSwitchFromRules: {
        name: [{
          required: true, message: '请输入交换机名称', trigger: 'blur'
        }],
        ip_version: [
          { required: true, message: '请选择IP版本', trigger: 'change' }
        ],
        subnet_addr: [
          { required: true, message: '请输入网络地址', trigger: 'blur' }
        ]
      },
      addRouterFromRules: {
        name: [{
          required: true, message: '请输入路由器名称', trigger: 'blur'
        }]
      },
      showEquipmentDrawer: false,
      dialogVisibleSwitch: false,
      dialogVisibleRouter: false,
      direction: 'rtl',
      terminalModalVisible: false,
      value1: '',
      cellDom: null,
      equipmentInfoData: {
        topoName: '',
        availability_zone: '',
        flavor_id: '',
        cloudCategory: '',
        ram: '',
        diskTypeId: '',
        diskSize: '',
        securityGroupId: ''
      }, // 设备详情数据
      value: '',
      dialogVisible: false,
      showSave: false, // 显示保存按钮
      showStart: false, // 显示启动按钮
      showRelease: false, // 显示释放按钮
      showTools: false, // 显示删除等工具
      showControl: true, // 拓扑图标
      graphLoading: false,
      graphLoadingText: '',
      addedNode: null,
      sourceNum: 1,
      typeOptions: [],
      subnetList: [],
      targetNum: 1,
      currentArrow: 1,
      sourceNode: null, // 起点节点
      targetNode: null, // 终点节点
      portEdge: null, // 端口节点链接线
      enabled: false,
      connectEdgeType: { // 连线方式
        connector: 'normal',
        router: {
          name: ''
        }
      },
      edge: null,
      color: ['#B0E0E6', '#F5F5DC'],
      deviceIdList: [102206, 102207],
      routerSwitchConnector: [102380, 102208],
      showVNCBtn: 'none',
      showEquipmentBtn: 'none',
      showDelBtn: 'none',
      showContextmenu: 'none',
      currentNode: null
    }
  },
  watch: {
    options: {
      handler: function (newValue) {
        if (newValue.data) { 
          this.graph.fromJSON(newValue.data)
        }
        this.moveView()
        this.checkStatus()
      },
      deep: true
    },
    showVNCBtn() {
      const vncLoginEl = document.getElementById('menu-vnc')
      vncLoginEl.style.display = this.showVNCBtn
    },
    showEquipmentBtn() {
      const equipmentEl = document.getElementById('menu-equipment')
      equipmentEl.style.display = this.showEquipmentBtn
    },
    showDelBtn() {
      const deleteEl = document.getElementById('menu-delete')
      deleteEl.style.display = this.showDelBtn
    }
  },
  mounted() {
    this.buildGraph()
    this.initEvent()
    this.buildMenu()
    this.checkStatus()
    this.initStencil()
  },
  beforeDestroy() {
    this.graph.dispose()
    document.getElementById('contextMenu').remove()
  },
  methods: {
    // 数据回显
    dataBackShow() {
      if (this.graph) {
        if (this.options.data) {
          this.graph.fromJSON(this.options.data)
          this.nodes = this.options.data
        }
        this.moveView()
      }
    },
    // 保存
    // 保存拓扑
    handleSaveTopo() {
      const cells = this.graph.getCells()
      if (this.checkTopoGraph(cells)) {
        // 获取 拓扑图片
        this.graph.toPNG((dataUri) => {
          this.$emit('saveTopo', cells, dataUri);
        }, {
          padding: {
            top: 20,
            right: 30,
            bottom: 40,
            left: 50
          }
        })
      } else {
        this.$message.error('请检查拓扑图是否正确.')
      }
    },
    cancel() {
      this.$emit('cancelwindow', false);
    },
    // 节点弹窗保存
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (formName === 'addSwitchFrom') {
            this.addSwitchFrom.ipv6_pd_enabled = this.addSwitchFrom.ip_version == 6
            this.addSwitchFrom.gateway_ip = this.addSwitchFrom.gateway_ip || 'auto'
            this.addSwitchFrom.subnet_name = this.addSwitchFrom.name + '_subnet'
            this.addedNode.updateAttrs({ title: { ...this.addedNode.attrs.title, text: this.addSwitchFrom.name }})
            this.addedNode.data = { ...this.addedNode.data, ...this.addSwitchFrom }
            this.$refs['addSwitchFrom'].resetFields()
            this.dialogVisibleSwitch = false
          }
          if (formName == 'addRouterFrom') {
            this.addedNode.updateAttrs({ title: { ...this.addedNode.attrs.title, text: this.addRouterFrom.name }})
            this.addedNode.data = { ...this.addedNode.data, ...this.addRouterFrom }
            this.$refs['addRouterFrom'].resetFields()
            this.dialogVisibleRouter = false
          }
        }
        return false
      })
    },
    // 设备详情更新
    _preservation() {
      this.cellDom.data.volumes.size = this.equipmentInfoData.diskSize
      this.cellDom.data.availability_zone = this.equipmentInfoData.availability_zone
      this.cellDom.data.flavor_id = this.equipmentInfoData.flavor_id
      this.cellDom.data.volume_type = this.equipmentInfoData.diskTypeId
      this.cellDom.data.name = this.equipmentInfoData.topoName
      this.cellDom.data.admin_user = this.equipmentInfoData.admin_user
      this.cellDom.data.admin_pass = this.equipmentInfoData.admin_pass
      this.cellDom.data.securityGroupId = this.equipmentInfoData.securityGroupId
      if (this.cellDom.data.networks.length != 0) {
        this.cellDom.data.networks.forEach(item => {
          item.security_group_ids = [this.cellDom.data.securityGroupId]
        })
      }
      this.cellDom.updateAttrs({ title: { ...this.cellDom.attrs.title, text: this.equipmentInfoData.topoName }})
      this.showEquipmentDrawer = false
    },
    // 用于获取创建云主机表单数据
    getTerminalData(formData) {
      this.equipmentInfoData = {
        'availability_zone': formData.clusterName, // 资源群集名称
        'catalog': '', // 主机所在目录
        'flavor_id': formData.specificationId, // 云主机类型ID
        'count': formData.count, // 创建数量
        securityGroupId: formData.securityGroupId,
        'firmware_type': 'bios', // 启动类型：uefi,bios
        'volumes': [
          {
            'source_type': 'image', // 资源类型，光盘时不需要此参数。可选范围：image:镜像 blank:数据盘
            'source_id': this.addedNode.data.sourceId, // 镜像id
            'size': formData.diskSize, // 大小（G）
            'volume_type': formData.diskTypeId, // 云硬盘类型。光盘时不需要此参数。
            'metadata': {
              'hw:disk_bus': 'virtio' // 元数据，
            }
          }
        ],
        'name': formData.terminalName, // 云主机名称
        'description': '', // 描述可以为空
        'hostname': null,
        'user_data': null,
        'admin_user': formData.username, // 云主机登录用户名
        'admin_pass': formData.security, // 云主机登录密码
        'metadata': {
          'use_static_ip': 'false',
          'dns_servers': '',
          'image_hw_qemu_guest_agent': 'true',
          'hw:enable_hyperv': 'true',
          'image_hw_vif_model': 'virtio',
          'image_img_config_drive': 'mandatory'
        }
      }
      this.addedNode.updateAttrs({ title: { ...this.addedNode.attrs.title, text: formData.terminalName } })
      this.addedNode.data = { ...this.addedNode.data, ...this.equipmentInfoData }
      // this.addedNode.setData({ ...this.addedNode.getData(), ...formData })
      this.terminalModalVisible = false
    },
    _deleteAddedNode() {
      this.dialogVisibleSwitch = false
      this.dialogVisibleRouter = false
      if (this.$refs['addSwitchFrom']) {
        this.$refs['addSwitchFrom'].resetFields()
      }
      if (this.$refs['addRouterFrom']) {
        this.$refs['addRouterFrom'].resetFields()
      }
      this.graph.removeCell(this.addedNode)
    },
    portDetermine() {
    },
    handleClose() {
      this.portConnectCancel()
      this.dialogVisible = false
    },
    // 节点是否可以选择
    enabledFn() {
      // 控制显示样式
      this.enabled = !this.enabled
      // 判断是和否是可选择状态
      if (this.graph.isSelectionEnabled()) {
        this.graph.disableSelection()
      } else {
        this.graph.enableSelection()
      }
    },
    // 改变边形状
    changeEdgeType(e) {
      if (e === 'normal') {
        this.connectEdgeType = {
          connector: 'normal',
          router: { name: '' }
        }
        this.currentArrow = 1
      } else if (e === 'smooth') {
        this.connectEdgeType = {
          connector: 'smooth',
          router: { name: '' }
        }
        this.currentArrow = 2
      } else {
        this.connectEdgeType = {
          connector: 'rounded',
          router: { name: 'manhattan' }
        }
        this.currentArrow = 3
      }
    },
    checkStatus() {
      console.log("this.options", this.options)
      if (this.options.readonly) {
        this.graphLoading = false
        this.showSave = false
        this.showStart = false
        this.showRelease = false
        this.showTools = false
        this.showControl = false
        return
      }
      if (this.options.scene) {
        this.graphLoading = false
        this.showSave = true
        this.showStart = false
        this.showRelease = false
        this.showTools = true
        return
      }
      // 不可编辑
      if (!this.options.editable) {
        this.showSave = false
        this.showControl = false
        this.showTools = false
        this.showStart = true
        return
      }
      // 未启动
      if (this.options.status === 0) {
        this.graphLoading = false
        this.showSave = true
        this.showStart = true
        this.showRelease = false
        this.showTools = true
        this.showControl = true
      }
      // 已启动
      if (this.options.status === 1) {
        this.graphLoading = false
        this.showSave = false
        this.showStart = false
        this.showRelease = true
        this.showTools = false
        this.showControl = false
      }
      // 启动中
      if (this.options.status === 2) {
        this.graphLoading = true
        this.graphLoadingText = '拓扑启动中...'
        this.showSave = false
        this.showStart = false
        this.showRelease = false
        this.showTools = false
      }
      // 释放中
      if (this.options.status === 3) {
        this.graphLoading = true
        this.graphLoadingText = '拓扑资源释放中...'
        this.showSave = false
        this.showStart = false
        this.showRelease = false
        this.showTools = false
      }
    },
    buildMenu() {
      const spanEl = document.createElement('span')
      spanEl.innerText = 'VNC登录'
      spanEl.style.display = 'none'
      spanEl.style.padding = '6px 20px'
      spanEl.style.cursor = 'pointer'
      spanEl.id = 'menu-vnc'
      spanEl.className = 'menu-vnc'
      spanEl.addEventListener('click', this.vncLogin)

      const spanEl2 = document.createElement('span')
      spanEl2.innerText = '设备详情'
      spanEl2.style.display = 'none'
      spanEl2.style.padding = '6px 20px'
      spanEl2.style.cursor = 'pointer'
      spanEl2.className = 'menu-equipment'
      spanEl2.id = 'menu-equipment'
      spanEl2.addEventListener('click', this.equipmentInfo)

      const spanEl3 = document.createElement('span')
      spanEl3.innerText = '删除'
      spanEl3.style.display = 'none'
      spanEl3.className = 'menu-delete'
      spanEl3.style.padding = '6px 20px'
      spanEl3.style.cursor = 'pointer'
      spanEl3.id = 'menu-delete'
      spanEl3.addEventListener('click', this.deleteInfo)

      const divEl = document.createElement('div')
      divEl.id = 'contextMenu'
      divEl.style.position = 'absolute'
      divEl.style.zIndex = '9999'
      divEl.style.display = 'none'
      divEl.style.backgroundColor = '#FFFFFF'
      divEl.style.border = '1px solid #EEEEEE'
      divEl.style.flexDirection = 'column'
      divEl.style.alignItems = 'center'
      divEl.style.boxShadow = '4px 4px 5px #888888'

      divEl.appendChild(spanEl)
      divEl.appendChild(spanEl2)
      divEl.appendChild(spanEl3)

      document.body.appendChild(divEl)

      document.body.addEventListener('click', event => {
        divEl.style.display = 'none'
      })
    },
    vncLogin() {
      if (this.currentNode) {
        this.$emit('vncLogin', this.currentNode.id)
      }
    },
    equipmentInfo() {
      if (this.currentNode) {
        this.showEquipmentDrawer = true
        this.equipmentInfoData = this.currentNode.data
        this.ruleForm_modify = this.currentNode.data
        console.log("this.currentNode.data", this.currentNode.data);
      }
    },
    deleteInfo() {
      if (this.currentNode) {
        this.graph.removeNode(this.currentNode.id)
      }
    },
    moveView() {
      this.graph.centerContent()
    },
    setZoom(event, number) {
      event.zoom(number)
    },
    handleContextmenu(event) {
      const contextMenuEl = document.getElementById('contextMenu')
      contextMenuEl.style.display = 'flex'
      contextMenuEl.style.top = event.clientY + 'px'
      contextMenuEl.style.left = event.clientX + 'px'
    },
    // 启动
    handleStartTopo() {
      this.$emit('startTopo')
    },
    // 释放
    handleReleaseTopo() {
      this.$emit('releaseTopo')
    },
    // 检查拓扑图连线
    checkTopoGraph(cells) {
      // 找出所有边
      const edgeList = cells.filter(c => c.shape === 'edge')
      // 找出所有和边相连的nodeId
      const nodeIdList = [...edgeList.map(c => c.source.cell), ...edgeList.map(c => c.target.cell)]
      const nodeList = cells.filter(c => c.shape === 'node-image')
      let flag = true
      // 判断和边相连的节点是否包含节点。如果不包含，则说明节点没有连线
      for (let i = 0; i < nodeList.length; i++) {
        if (!nodeIdList.includes(nodeList[i].id)) {
          flag = false
          flag = true
          break
        }
      }
      return flag
    },
    // 保存拓扑
    realTopo() {
      const cells = this.graph.getCells()
      if (this.checkTopoGraph(cells)) {
      // 获取 拓扑图片
        this.graph.toPNG((dataUri) => {
          this.$emit('realTopo', cells, dataUri);
          // this.$emit('saveTopo', cells, dataUri)
        }, {
          padding: {
            top: 20,
            right: 30,
            bottom: 40,
            left: 50
          }
        })
      } else {
        this.$message.error('请检查拓扑图是否正确.')
      }
    },
    // 构建画布
    buildGraph() {
      var _that = this
      this.graph = new Graph({
        container: this.$refs._tp_graph,
        grid: {
          visible: true,
          type: 'doubleMesh',
          args: [
            {
              color: '#eee', // 主网格线颜色
              thickness: 1 // 主网格线宽度
            },
            {
              color: '#ddd', // 次网格线颜色
              thickness: 1, // 次网格线宽度
              factor: 4 // 主次网格线间隔
            }
          ]
        },
        // 移动范围
        translating: {
          restrict(view) {
            if (view) {
              const cell = view.cell
              if (cell.isNode()) {
                const parent = cell.getParent()
                if (parent) {
                  return parent.getBBox()
                }
              }
            }
            return null
          }
        },
        // 画布是否可以拖动
        panning: true,
        // 鼠标滚轮缩放
        mousewheel: {
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 3
        },
        // 交互
        connecting: {
          allowNode: false,
          allowLoop: false,
          allowMulti: false,
          connectionPoint: 'anchor',
          createEdge() {
            return new Shape.Edge({
              attrs: {
                line: {
                  targetMarker: null
                }
              },
              connector: _that.connectEdgeType.connector,
              router: { // 路由
                name: _that.connectEdgeType.router.name
              },
              zIndex: 0
            })
          }
        }
      })
      this.graph.use(
        new Keyboard({
          enabled: true,
          global: true
        }),
      )
      this.graph.use(
        new Selection({
          enabled: false,
          rubberband: true
        })
      )
      this.graph.use(new Export())
    },
    // 根据 元素 的x,y轴跟 宽高 来生成 父节点
    createGroup(x, y, width, height, fill, zIndex) {
      if (width < 0) {
        width = width * -1
      }
      if (height < 0) {
        height = height * -1
      }
      height = height + 50
      width = width + 40
      const group = new Group({
        x,
        y,
        zIndex: zIndex,
        width,
        height,
        attrs: {
          body: {
            fill: fill,
            stroke: '#000' },
          label: { text: '' }
        },
        data: {
          facilityCategory: ''
        }
      })
      this.graph.addNode(group)
      return group
    },
    // 分组
    groupChild(group, element) {
      group.addChild(element)
    },
    portConnectCancel() {
      this.graph.removeEdge(this.portEdge)
      this.value1 = []
      this.value = []
      this.dialogVisible = false
    },
    // 初始化事件
    initEvent() {
      // ctrl+c事件
      this.graph.bindKey('ctrl+c', () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.copy(cells)
        }
        return false
      })
      // ctrl+v事件
      this.graph.bindKey('ctrl+v', () => {
        if (!this.graph.isClipboardEmpty()) {
          const cells = this.graph.paste({ offset: 32 })
          this.graph.cleanSelection()
          this.graph.select(cells)
        }
        return false
      })
      // 控制连接桩显示/隐藏
      const showPorts = (ports, show) => {
        for (let i = 0, len = ports.length; i < len; i = i + 1) {
          ports[i].style.visibility = show ? 'visible' : 'hidden'
        }
      }
      this.graph.on('node:mouseenter', ({ e, node }) => {
        if (this.showTools) {
          const ports = this.$refs._tp_graph.querySelectorAll('.x6-port-body')
          showPorts(ports, true)
        }
      })
      this.graph.on('node:mouseleave', () => {
        if (this.showTools) {
          const ports = this.$refs._tp_graph.querySelectorAll('.x6-port-body')
          showPorts(ports, false)
        }
      })

      this.graph.on('node:contextmenu', ({ e, node }) => {
        this.currentNode = node
        // 初始化右键菜单
        this.handleContextmenu(e)
        this.showEquipmentBtn = 'block'
        if (this.options.status === 0) {
          this.showVNCBtn = 'none'
          this.showDelBtn = 'block'
        }
        if (this.options.status === 1) {
          if (this.deviceIdList.includes(node.data.facilityCategoryId)) {
            this.showVNCBtn = 'block'
          } else {
            this.showVNCBtn = 'none'
          }
          this.showDelBtn = 'none'
        }
      })

      this.graph.on('node:added', ({ node }) => {
        if (node.attrs.type) {
          // 拖动虚拟设备才触发
          if (node.attrs.type.name === '虚拟') {
            if (this.deviceIdList.includes(node.data.facilityCategoryId)) {
              this.terminalModalVisible = true
              this.timeStamp = Date.now();
            }
            if (node.data.facilityCategoryId == 102208) {
              // alert(node.data.facilityCategoryId)
              this.dialogVisibleSwitch = true
            }
            // 交换机
            if (node.data.facilityCategoryId == 102380) {
              // alert(node.data.facilityCategoryId)
              this.dialogVisibleRouter = true
            }
            this.addedNode = node
          }
        }
        let num = 0
        if (node.attrs.type && node.attrs.type.name === '物理') {
          this.graph.getNodes().forEach(item => {
            if (item.attrs.title && node.attrs.title) {
              if (item.attrs.title.text === node.attrs.title.text &&
                  item.attrs.title.text !== '红队' && item.attrs.title.text !== '蓝队') {
                num++
                if (num > 1) {
                  this.graph.removeNode(node)
                }
              }
            }
          })
        }
      })
      this.graph.on('node:change:size', ({ cell }) => {
        if (!cell.children) {
          cell.attrs.image.width = cell.size().width
          cell.attrs.image.height = cell.size().height
          cell.attrs.title.fontSize = cell.size().height / 5
        }
      })

      this.graph.on('edge:mouseenter', ({ cell }) => {
        // cell.addTools([
        //   'source-arrowhead',
        //   {
        //     name: 'target-arrowhead',
        //     args: {
        //       attrs: {
        //         fill: 'red'
        //       }
        //     }
        //   }
        // ])
      })
      this.graph.on('edge:mouseleave', ({ cell }) => {
        cell.removeTools()
      })

      // 选择两个以上 节点 并且 该节点没有父节点 根据节点的属性来生成 父节点的大小 与所在位置
      this.graph.on('selection:changed', ({ added, removed, selected, options }) => {
        const arr = []
        let num = 0
        let zIndex = -10
        selected.forEach(element => {
          if (!element.parent) {
            arr.push(element)
          }
          if (element._children || element.children) {
            num++
            zIndex--
          }
        })
        if (arr.length > 1) {
          const xObj = []
          const yObj = []
          arr.forEach(item => {
            xObj.push(item.position().x, (item.position().x + item.size().width))
            yObj.push(item.position().y, (item.position().y + item.size().height))
          })
          const x = xObj.sort((a, b) => { return a - b })
          const y = yObj.sort((a, b) => { return a - b })
          let height = ''
          let width = ''
          if (x[x.length - 1] > 0 && x[0] < 0) {
            width = x[x.length - 1] + (x[0] * -1)
          } else {
            width = x[x.length - 1] - x[0]
          }
          if (y[y.length - 1] > 0 && y[0] < 0) {
            height = y[y.length - 1] + (y[0] * -1)
          } else {
            height = y[y.length - 1] - y[0]
          }
          const a = this.createGroup(
            x[0] - 20,
            y[0] - 25,
            width,
            height, this.color[ (num % 2) ], zIndex)
          arr.forEach(item => {
            a.addChild(item)
          })
        }
      })
    },
    async initStencil() {
      const pNodes = await createPhysicalNodes(this.graph)
      const vNodes = await createVirtualNodes(this.graph)
      // 初始化 stencil
      const stencil = buildStencil(this.graph, pNodes.length, vNodes.length)
      stencil.load(pNodes, 'group1')
      stencil.load(vNodes, 'group2')
      this.$refs._tp_control.appendChild(stencil.container)
      this.$emit("stencilinited")
    },
    handlShowTools(cell) {
      if (cell.isNode()) {
        cell.addTools([
          {
            name: 'boundary',
            args: {
              attrs: {
                fill: '#7c68fc',
                stroke: '#333',
                'stroke-width': 1,
                'fill-opacity': 0.2
              }
            }
          }
        ])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
._tp_container {
  display: flex;
  position: relative;
  height: 100%;
  width: 100%;

  ._tp_tools {
    position: absolute;
    display: flex;
    align-items: center;
    left: 50%;
    top: 10px;
    transform: translateX(-50%);
    z-index: 1;
    ._tp_operation_column{
      padding: 10px 25px;
      display: flex;
      background: #fff;
      margin: 0 40px 0 80px;
      border-radius: 8px;
      box-shadow: 0px 0px 6px rgba(40, 143, 239,0.5);
      div{
        margin: 0 15px;
      }
      img{
        height: 25px;
        width: 25px;
        cursor: pointer;
        margin: 0 5px;
      }
    }
  }
  ._tp_control {
    width: 260px;
    height: 100%;
    position: absolute;
    // border: 1px solid #f0f0f0;
    z-index: 1;
  }
  ._tp_graph{
    height: 100% !important;
    width: 100% !important;
  }
}
._port_choice{
  display: flex;
  align-items: center;
  justify-content: space-around;
  ::v-deep{
    .el-select{
      width: 250px;
    }
  }
}
._topo_info_{
  ::v-deep{
    ._radio_item_{
      display: flex;
      flex-direction: column;
      .el-form-item__label{
        width: 200px !important;
        text-align: left;
        margin-left: 35px;
      }
    }
  }
  .btn_div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      margin-bottom: 20px;
    }
  }
}
</style>
