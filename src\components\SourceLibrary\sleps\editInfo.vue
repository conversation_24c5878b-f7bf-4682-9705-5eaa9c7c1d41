<template>
  <div class="edit-experiment-info">
    <el-form ref="formData" :model="formData" :rules="rules" label-width="100px" class="demo-formData">
      <el-form-item label="设备名称" prop="facilityName">
        <el-input v-model="formData.facilityName" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="设备图标" prop="facilityIcon" class="facilityIcon">
        <img v-if="formData.facilityIcon" :src="formData.facilityIcon" width="120" height="120" style="margin-right: 20px"
          alt>
        <el-button class="choose_icon" @click="dialogVisibleIcon = true">选择图标</el-button>
      </el-form-item>
      <el-form-item label="设备分类" prop="facilityCategoryArr">
        <el-cascader v-model="formData.facilityCategoryArr" :options="facilityCategoryOptions" :props="optionProps"
          @change="handleChange" />
      </el-form-item>
      <el-form-item v-if="formData.facilityCategoryId === 102206" label="端口" prop="ports">
        <el-input v-model="formData.ports" placeholder="端口列表，使用英文逗号分隔" />
      </el-form-item>
      <el-form-item v-if="formData.facilityCategoryId === 102207 || formData.facilityCategoryId === 102206" label="系统镜像"
        prop="systemImageName">
        <el-radio-group v-model="formData.systemImageName" @change="changeSystemImagetype">
          <el-radio-button label="windows" />
          <el-radio-button label="linux" />
        </el-radio-group>
        <!-- <el-input v-model="formData.systemImageName" readonly style="width: 60%;"></el-input> -->
      </el-form-item>
      <el-form-item
        v-if="formData.systemImageName == 'linux' && formData.facilityCategoryId === 102207 || formData.facilityCategoryId === 102206"
        prop="systemImageId">
        <el-select ref="headerSearchSelect" v-model="formData.systemImageId" filterable class="header-search-select">
          <el-option v-for="option in linuxOptions" :key="option.id" :value="option.id" :label="option.name" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.systemImageName == 'windows' && formData.facilityCategoryId === 102207" label
        prop="systemImageId">
        <el-select ref="headerSearchSelect" v-model="formData.systemImageId" filterable class="header-search-select">
          <el-option v-for="option in windowsOptions" :key="option.id" :value="option.id" :label="option.name" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="设备描述">
        <el-input v-model="formData.facilityDescription" style="width: 60%;"></el-input>
      </el-form-item>-->
    </el-form>
    <el-dialog :modal="false" :visible.sync="dialogVisibleIcon" title="请选择图标" width="30%" class="dialog_images">
      <el-row class="_dialogIcon">
        <el-col v-for="(item, index) in imagesList" :span="8" :key="index">
          <el-card :body-style="{ padding: '0px' }" class="_card">
            <img :src="item.iconAddr" class="image" width="100" height="100" style="margin-left:30px">
            <div class="_imgIcon">
              <p>{{ item.iconName }}</p>
            </div>
            <div class="_imgIconSele" @click="chooseIcon(item.iconAddr)">
              <p>选择</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { listEquipmentIcon } from '@/api/sourceLibrary/equipmentIcon'
import { getImagesList, treeListById } from '@/api/sourceLibrary/virtualApi'
import { addVirtual } from '@/api/sourceLibrary/virtualApi'
export default {
  name: 'Editinfo',
  // eslint-disable-next-line vue/require-prop-types
  props: ['isSubmit'],
  data() {
    return {
      facilityCategoryOptions: [],
      formData: {
        facilityName: '',
        systemImageName: 'windows',
        systemImageId: '',
        facilityCategoryId: '',
        facilityIcon: '',
        ports: ''
      },
      dialogVisibleIcon: false,
      chooseFlag: false,
      imagesList: [],
      fileSrcDialogVisible: false,
      windowsOptions: [],
      linuxOptions: [],
      systemImageAllOptions: [],
      fileSrc: '',
      percent: 0,
      fileList: [],
      optionProps: {
        value: 'id',
        label: 'label',
        children: 'children'
      },
      rules: {
        facilityName: [
          { required: true, message: '请输入设备名称', trigger: 'blur' }
        ],
        systemImageName: [
          {
            required: true,
            message: '请选择系统镜像',
            trigger: ['blur', 'change']
          }
        ],
        systemImageId: [
          {
            required: true,
            message: '请选择系统镜像',
            trigger: ['blur', 'change']
          }
        ],
        facilityIcon: [
          { required: true, message: '请选择设备图标', trigger: 'change' }
        ],
        facilityCategoryArr: [
          { required: true, message: '请选择设备分类', trigger: 'change' }
        ]
        // facilityCategory: [
        //   { required: true, message: '请选择设备分类', trigger: 'change' },
        // ],
      },
      videoUrl: '',
      expId: ''
    }
  },
  computed: {
    myHeaders() {
      const authCode = 'Bearer ' + this.$store.state.user.token
      return { Authorization: authCode }
    }
  },
  watch: {
    isSubmit: function (newVal) {
      if (newVal) {
        // 提交
        this.$refs['formData'].validate(async (valid) => {
          if (valid) {
            const params = {
              facilityName: this.formData.facilityName,
              systemImageName: this.formData.systemImageName,
              systemImageId: this.formData.systemImageId,
              facilityCategoryId: this.formData.facilityCategoryId,
              facilityIcon: this.formData.facilityIcon,
              ports: this.formData.ports
            }
            await addVirtual(params)
              .then((res) => {
                console.log(res)
                // this.$message.success(res.msg)
                this.$parent.disFlag = false
                this.$emit('formOk')
              })
              .catch(() => {
                this.$parent.disFlag = false
                this.$emit('formError')
              })
            // this.$emit('formOk')
            /* try {
              // if (this.expId) {
              //   //修改
              //   await updateExperiment(params)
              // } else {
              //   // 新增
              //   const {
              //     data: { experimentId },
              //   } = await addExperiment(params)
              //   this.$store.dispatch('experiment/setExpId', experimentId)
              // }
              this.$emit('formOk')
            } catch {
              this.$emit('formError')
            }*/
          } else {
            this.msgError('请完善表单！')
            this.$emit('formError')
            return false
          }
        })
      }
    }
  },
  created() {
    this.getImages()
    this.getOptionList()
    this.getIconOption()
  },
  async mounted() {
    // this.videoUrl = process.env.VUE_APP_BASE_API + uploadPraticleImg()
  },
  methods: {
    changeButton(item, index) {
      this.imagesList[index].mouseOverFlag = false
      this.$forceUpdate()
    },
    // 选择图标
    chooseIcon(url) {
      console.log(url)
      this.formData.facilityIcon = url
      this.dialogVisibleIcon = false
    },
    handleChange(val) {
      this.formData.facilityCategoryId = val[val.length - 1]
      // console.log('object, ', this.formData)
    },
    // 获取设备图标列表
    getIconOption() {
      const params = {
        pageSize: Number.MAX_VALUE,
        pageNum: 1
      }
      listEquipmentIcon(params)
        .then((res) => {
          if (res.code == 200 || res.code == 0) {
            this.imagesList = res.rows.filter(
              (item) => item.iconType == '0' || item.iconType == null
            )

            this.imagesList.forEach((item) => {
              item.mouseOverFlag = false
            })
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取品牌型号下拉框
    getOptionList() {
      const id = 101
      treeListById(id)
        .then((res) => {
          console.log(res)
          if (res.code == 200 || res.code == 0) {
            this.facilityCategoryOptions = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 改变系统镜像类型
    changeSystemImagetype(val) {
      const a = this.formData.systemImageId
      this.formData.systemImageId = ''
      const b = this.linuxOptions.some(
        (item) => item.id == this.formData.systemImageId
      )
      const c = this.windowsOptions.some(
        (item) => item.id == this.formData.systemImageId
      )
      if ((val == 'linux' && b) || (val == 'windows' && c)) {
        this.formData.systemImageId = a
      } else {
        this.formData.systemImageId = ''
      }
    },
    async getImages() {
      await getImagesList().then((res) => {
        this.linuxOptions = res.data.result.filter((item) => item.os_type == 'linux')
        console.log(this.linuxOptions)
        // this.linuxOptions = res.data
        this.windowsOptions = res.data.result.filter(
          (item) => item.os_type == 'windows'
        )
        // if (this.formData.systemImageName) {
        //   // this.formData.systemImageId =
        //   this.changeSystemImagetype(this.formData.systemImageName)
        // }
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    _onChange(file) {
      if (
        (file.type && !file.type.includes('image')) ||
        !file.raw.type.includes('image')
      ) {
        this.msgError(`请上传格式为.jpg、.gif、.png、.jpeg的图片!`)
        return false
      }
      const isLt = file.size / 1024 / 1024 < 5
      if (!isLt) {
        this.msgError(`上传文件大小不能超过 5MB!`)
        return false
      }
      // 读取文件的字符流
      const reader = new FileReader()
      // 将文件读取为 DataURL 以data:开头的字符串
      reader.readAsDataURL(file.raw)
      reader.onload = (e) => {
        // 读取到的图片base64 数据编码 将此编码字符串传给后台即可
        const code = e.target.result
        this.formData.facilityIcon = code
      }
      // this.formData.facilityIcon = this.image2Base64(file)
    },

    image2Base64(img) {
      var canvas = document.createElement('canvas')
      canvas.width = img.width
      canvas.height = img.height
      var ctx = canvas.getContext('2d')
      ctx.drawImage(img, 0, 0, img.width, img.height)
      var dataURL = canvas.toDataURL('image/png')
      return dataURL
    },
    _beforeUpload(file) {
      if (!file.type.includes('image')) {
        this.msgError(`请上传格式为.jpg、.gif、.png、.jpeg的图片!`)
        return false
      }
      const isLt = file.size / 1024 / 1024 < 5
      if (!isLt) {
        this.msgError(`上传文件大小不能超过 5MB!`)
        return false
      }
      return true
    },
    _onSuccess(response, file, fileList) {
      if (response.code == 500) {
        this.$message.error(response.msg)
        return
      }
      console.log(response)
      this.fileList = fileList
      this.fileSrc = response.data.url
      this.formData.url = response.data.url
      this.formData.imgName = response.data.name
    },
    _onError() {
      this.msgError(`文件上传出错，请重新上传!`)
      this.fileList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-experiment-info {
  width: 100%;
  height: 100%;
  padding: 0 0 0 30px;
  box-sizing: border-box;
  position: relative;

  // @include paddingBoxSizing(0 0 0 30px);
  .el-form::v-deep {
    .facilityIcon {
      .el-form-item__content {
        display: flex;
      }

      .choose_icon {
        width: 120px;
        height: 120px;
      }

      .choose_dialog {
        width: 500px;
        height: 300px;
        position: absolute;
        top: 0;
        right: 20px;
        border: 1px solid #f5f5f5;
        z-index: 1;
        background: #fff;
        border: 1px solid #b5b5b5;

        // display: flex;
        // flex-wrap: wrap;
        // padding: 10px;
        // box-sizing: border-box;
        // justify-content: flex-start;
        // align-items: flex-start;
        .choose_dialog_list {
          width: 100%;
          height: 100%;
          position: relative;

          .choose_dialog_con {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            overflow-y: auto;

            .choose_dialog_con_img {
              width: 120px;
              height: 150px;
              margin-bottom: 20px;
              text-align: center;

              .text {
                text-align: center;

                .sure_icon {
                  border: none;
                }
              }

              .send {
                display: none;
              }

              &:hover {
                .text {
                  display: none;
                }

                .send {
                  display: block;
                }
              }
            }
          }

          .close_icon {
            position: absolute;
            top: 5px;
            right: 20px;
            width: 10px;
            height: 10px;
            text-align: center;
            line-height: 10px;
            font-size: 16px;
            padding: 0;
            border: none;
          }
        }
      }
    }

    .el-upload,
    .el-upload__tip {
      display: inline-block;
      margin-left: 10px;
    }
  }

  .dialog_images::v-deep {
    .el-dialog {
      margin-top: 15vh !important;
    }

    // height: 60%;
    .el-dialog__body {
      height: 60vh;

      ._dialogIcon {
        height: 100%;
        overflow: auto;

        ._card {
          margin: 10px;

          ._imgIcon,
          ._imgIconSele {
            text-align: center;
            margin: 10px;
            cursor: pointer;
            height: 20px;

            >p {
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          ._imgIconSele {
            display: none;
          }

          &:hover {
            ._imgIcon {
              display: none;
            }

            ._imgIconSele {
              display: block;
              background-color: rgb(189, 188, 188);
            }
          }
        }
      }
    }
  }
}
</style>
