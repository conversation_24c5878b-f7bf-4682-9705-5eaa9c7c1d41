import request from '@/utils/request'

const api = process.env.ADMIN_API

// 查询知识库数据列表
export function listKbs(query) {
  return request({
    url: api + '/knowledge/openapi/list',
    method: 'get',
    params: query
  })
}

// 查询知识库数据详细
export function getKbs(kbsId) {
  return request({
    url: api + '/knowledge/query/' + kbsId,
    method: 'get'
  })
}

// 新增知识库数据
export function addKbs(data) {
  return request({
    url: api + '/knowledge/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改知识库数据
export function updateKbs(data) {
  return request({
    url: api + '/knowledge/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除知识库数据
export function delKbs(kbsId) {
  return request({
    url: api + '/knowledge/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: kbsId
  })
}


// 上传文件
export function uploadKbs() {
  return api + '/knowledge/accFile'
}
// 下载文件
export function downloadFiles() {
  return api + '/file/downloadFiles'
}
