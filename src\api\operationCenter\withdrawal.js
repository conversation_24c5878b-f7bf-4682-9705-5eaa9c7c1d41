import request from "@/utils/request";


export function getOperationList(params) {
  return request({
    url: process.env.ADMIN_API + "/withdraw/getLog",
    method: "post",
    data: params
  });
}


export function getSettings() {
  return request({
    url: process.env.ADMIN_API + "/withdraw/get",
    method: "get",
  });
}

export function addSettings(params) {
  return request({
    url: process.env.ADMIN_API + "/withdraw/add",
    method: "post",
    data: params
  });
}

export function orderPageList(params) {
  return request({
    url: process.env.ADMIN_API + "/withdraw/order/admin/orderPageList",
    method: "post",
    data: params
  });
}
