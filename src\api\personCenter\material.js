import request from '@/utils/request'
 
const api = process.env.ADMIN_API

// 添加素材中心-分组 
export function addMaterial(data) {
  return request({
    url: api + '/material/add',
    method: 'post',
    data: data
  })
}

// 编辑素材中心-分组 
export function editMaterial(data) {
  return request({
    url: api + '/material/edit',
    method: 'post',
    data: data
  })
}

// 删除素材中心-分组 
export function delMaterial(data) {
  return request({
    url: api + '/material/deleteMaterial',
    method: 'get',
    params: data
  })
}

// 列表素材中心-分组 
export function getMaterialList(data) {
  return request({
    url: api + '/material/getMaterialList',
    method: 'get',
    params: data
  })
}