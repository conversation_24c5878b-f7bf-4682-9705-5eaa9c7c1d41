import request from '@/utils/request'

// 获取专栏分页列表
export function getPageList (params = {}) {
  return request({
    url: process.env.ADMIN_API + '/videoCourse/getList',
    method: 'post',
    data: params
  })
}

// 获取专栏分页列表
export function downCourse (params = {}) {
  return request({
    url: process.env.ADMIN_API + `/videoCourse/down?status=${params.status}`,
    method: 'post',
    data: params.uids
  })
}