import request from '@/utils/request'

// 添加视频
export function addVideo(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/video/add',
        method: 'post',
        data: params
    })
}
// 编辑视频
export function editorVideo(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/video/edit',
        method: 'post',
        data: params
    })
}
// 视频列表
export function videoList(params = {}) {
    return request({
        url: process.env.ADMIN_API + '/video/getPageList',
        method: 'post',
        data: params
    })
}