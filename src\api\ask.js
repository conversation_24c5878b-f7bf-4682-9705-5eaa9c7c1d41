import request from '@/utils/request'

export function getQuestionPageList(params) {
  return request({
    url: process.env.ADMIN_API + '/ask/getPageList',
    method: 'post',
    data: params
  })
}

export function deleteQuestion(params) {
  return request({
    url: process.env.ADMIN_API + "/ask/delete",
    method: "post",
    data: params
  });
}

export function deleteBatchQuestion(params) {
  return request({
    url: process.env.ADMIN_API + '/ask/deleteBatch',
    method: 'post',
    data: params
  })
}
