<!-- 按钮组件 -->
<template>
  <div class="hy-tag cursor-p" @click="handleClick()"
    :class="'hy-tag-' + type + (cursor ? ' hy-' + type + '-cursor' : '') + (radius ? ' radius' : '') + ' ' + fontSize">
    <slot>{{ hy_text }}</slot>
  </div>
</template>
<script>
export default {
  name: 'hyTag',
  props: {
    type: {
      type: String,
      defalut: 'primary'
    },
    cursor: {
      type: <PERSON>olean,
      defalut: false
    },
    radius: {
      type: <PERSON>olean,
      defalut: false
    },
    fontSize: {
      type: String,
      defalut: 'font-12'
    }
  },
  data () {
    return {
      hy_text: '标签'
    }
  },
  methods: {
    handleClick () {
      this.$emit('handleClick')
    }
  }
}
</script>
<style lang="scss" scoped>
.radius {
  border-radius: 3px;
}

.hy-tag {
  padding: 3px 8px;
  font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  line-height: 18px;
}

.hy-tag-primary {
  background: rgba(0, 110, 255, 0.15);
  color: rgba(0, 110, 255, 1);
}

.hy-primary-cursor:hover {
  background: rgba(0, 110, 255, 0.45);
}

.hy-tag-warning {
  background: rgba(247, 121, 0, 0.05);
  color: #F77900;
}

.hy-warning-cursor:hover {
  background: rgba(247, 121, 0, 0.15);
}

.hy-tag-info {
  background: #f4f6f7;
  color: #999999;
}

.hy-info-cursor:hover {
  background: rgba(51, 51, 51, 0.1);
}

.hy-tag-danger {
  color: #FF564D;
  background: rgba(255, 86, 77, 0.1);
}

.hy-danger-cursor:hover {
  background: rgba(255, 86, 77, 0.6);
}
</style>