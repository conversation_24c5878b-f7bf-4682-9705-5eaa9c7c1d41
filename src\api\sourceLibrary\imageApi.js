import request from '@/utils/request'

// 查询系统镜像列表
export function listImage(query) {
  return request({
    url: process.env.ADMIN_API + '/images/getImagesList',
    method: 'get',
    params: query
  })
}

// 查询系统镜像详细
export function getImage(id) {
  return request({
    url: process.env.ADMIN_API + '/images/imageInfo/' + id,
    method: 'get'
  })
}

// 创建磁盘镜像
export function addImages(data) {
  return request({
    url: process.env.ADMIN_API + '/images/createImages',
    method: 'post',
    data
  })
}

// 创建光盘镜像
export function addISOs(data) {
  return request({
    url: process.env.ADMIN_API + '/images/createISOs',
    method: 'post',
    data: data
  })
}

// 修改系统镜像
export function updateImage(data) {
  return request({
    url: process.env.ADMIN_API + '/images/updateImage',
    method: 'post',
    data: data
  })
}

// 删除系统镜像
export function delImage(id) {
  return request({
    url: process.env.ADMIN_API + '/images/deleteImage/' + id,
    method: 'get'
  })
}

// 上传
export function uploadImages() {
  return process.env.ADMIN_API + '/images/uploadImages'
}

// 创建光盘镜像源url
export function uploadImagesUrl(data) {
  return request({
    url: process.env.ADMIN_API + '/images/uploadImages',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
