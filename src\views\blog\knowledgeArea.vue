<template>
    <div class="knowledge-area-container">
        <!-- 表单搜索 -->
        <el-form :inline="true" :model="knowledgeAreaSearchForm" size="mini">
            <el-form-item label="请输入知识领域名称">
                <el-input v-model="knowledgeAreaSearchForm.tagName" placeholder="审批人"></el-input>
            </el-form-item>
            <el-form-item label="添加时间">
                    <el-date-picker
                        value-format="yyyy-MM-dd HH:mm:ss"
                        v-model="knowledgeAreaSearchForm.dates"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
            <el-form-item style="float: right;">
                <el-button type="danger" @click="batchDel">删除</el-button>
            </el-form-item>
            <el-form-item style="float: right;">
                <el-button type="primary" @click="addKnowledgeArea">添加知识领域</el-button>
            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column
                type="type"
                width="55">
                <template slot-scope="scope">
                    <span>{{ scope.$index }}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="知识领域名称">
                <template slot-scope="scope">{{ scope.row.date }}</template>
            </el-table-column>
            <el-table-column
                prop="name"
                label="关联实验室数量">
            </el-table-column>
            <el-table-column
                prop="address"
                label="添加时间"
                show-overflow-tooltip>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
         <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="knowledgeAreaSearchForm.currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="knowledgeAreaSearchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="knowledgeAreaSearchForm.total">
        </el-pagination>
    </div>
</template>
<script>
import { addArea, editArea, deleteArea, pageListArea } from '@/api/knowledgeArea'
export default{
    data(){
        return {
            knowledgeAreaForm: {

            },
            knowledgeAreaSearchForm: {
                dates: [],
                tagName:'',
                total: 0,
                pageSize: 10,
                currentPage:1
            },
            selectArrs:[]
        }
    },
    created() {
        this.initData();
    },
    methods: {
        handleSelectionChange(datas) { 
            this.selectArrs = datas;
        },
        addKnowledgeArea() { 

        },
        batchDel() { 

        },
        async initData() {
            let { pageSize, currentPage, dates=[], tagName } = this.knowledgeAreaSearchForm;
            let params = {
                pageSize,
                currentPage,
                startTime: dates.length ? dates[0] : '',
                endTime: dates.length ? dates[1] : '',
                keyword:tagName
            }
            
            let result = await pageListArea(params);
        },
        search() { 
            this.initData();
        },
        handleSizeChange(val) { 
            this.knowledgeAreaSearchForm.pageSize = val;
            this.initData();
        },
        handleCurrentChange(val) { 
            this.knowledgeAreaSearchForm.currentPage = val;
            this.initData();
        }
    },
}
</script>
<style lang='scss' scoped>
.knowledge-area-container{
    overflow: hidden;
    padding: 20px;
    min-height: 100%;
}
</style>