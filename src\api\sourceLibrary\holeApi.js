import request from '@/utils/request'

// 查询漏洞库列表
export function listHole(query) {
  return request({
    url: process.env.ADMIN_API + '/hole/list',
    method: 'get',
    params: query
  })
}

// 查询漏洞库详细
export function getHole(bugId) {
  return request({
    url: process.env.ADMIN_API + '/hole/getInfo',
    params:{bugId},
    method: 'get'
  })
}

// 新增漏洞库
export function addHole(data) {
  return request({
    url: process.env.ADMIN_API + '/hole/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改漏洞库
export function updateHole(data) {
  return request({
    url: process.env.ADMIN_API + '/hole/edit',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除漏洞库
export function delHole(bugId) {
  return request({
    url: process.env.ADMIN_API + '/hole/delete',
    params: {bugIds:bugId.join(',')},
    method: 'get'
  })
}

export function importFile() {
  return process.env.ADMIN_API + '/hole/importFile'
}
