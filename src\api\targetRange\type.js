import request from '@/utils/request'

// process.env.ADMIN_API
const api = process.env.ADMIN_API

// 列表
export function categoryList(data) {
  return request({
    url: api + '/category/getList',
    method: 'post',
    data
  })
}

export function categoryAdd(data) {
  return request({
    url: api + '/category/add',
    method: 'post',
    data
  })
}

export function categoryEdit(data) {
  return request({
    url: api + '/category/edit',
    method: 'post',
    data
  })
}

export function categoryDel(data) {
  return request({
    url: api + '/category/deleteBatch',
    method: 'post',
    data
  })
}

export function categoryUpdateStatus(data) {
  return request({
    url: api + '/category/updateStatus',
    method: 'get',
    params: data
  })
}