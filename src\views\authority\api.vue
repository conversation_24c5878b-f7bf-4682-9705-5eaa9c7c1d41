<template>
  <div class="app-container">
    <!-- 查询和其他操作 -->
    <div class="filter-container" style="margin: 10px 0 10px 0;">
      <el-cascader
        :options="options"
        filterable
        placeholder="请选择菜单名"
        v-model="keyword"
        :props="{ checkStrictly: true, emitPath: false, children: 'childCategoryMenu', label: 'name', value: 'uid' }"
        clearable
      ></el-cascader>
      <el-select v-model="functionType" clearable placeholder="请选择">
        <el-option label="前台" value="1"> </el-option>
        <el-option label="后台" value="0"> </el-option>
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFind"
        v-permission="'/categoryMenu/getList'"
        >查找</el-button
      >
      <el-button
        class="filter-item"
        type="primary"
        @click="handleAdd"
        icon="el-icon-edit"
        v-permission="'/categoryMenu/add'"
        >添加接口</el-button
      >
    </div>

    <el-table :data="tableData" style="width: 100%">
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-table
              :data="scope.row.childCategoryMenu"
              :show-header="showHeader"
              style="width: 100%"
            >
              <!-- <el-table-column label align="center"></el-table-column> -->
              <el-table-column label align="right">
                <template slot-scope="scope_child">
                  <span>{{ scope_child.$index + 1 }}</span>
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <span>{{ scope_child.row.name }}</span>
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <el-tag
                    v-for="item in menuLevelDictList"
                    :key="item.uid"
                    v-if="scope_child.row.menuLevel == item.dictValue"
                    :type="item.listClass"
                    >{{ item.dictLabel }}</el-tag
                  >
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope">
                  <el-tag
                    v-for="item in menuTypeDictList"
                    :key="item.uid"
                    v-if="scope.row.menuType == item.dictValue"
                    :type="item.listClass"
                    >{{ item.dictLabel }}</el-tag
                  >
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <span>{{ scope_child.row.summary }}</span>
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <span>
                    {{ scope_child.row.functionType == 1 ? "前台" : "后台" }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <span>{{ scope_child.row.icon }}</span>
                  <i :class="scope_child.row.icon" />
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <span>{{ scope_child.row.url }}</span>
                </template>
              </el-table-column>

              <el-table-column align="center">
                <template slot-scope="scope">
                  <el-tag
                    v-for="item in yesNoDictList"
                    :key="item.uid"
                    v-if="scope.row.isShow == item.dictValue"
                    :type="item.listClass"
                    >{{ item.dictLabel }}</el-tag
                  >
                </template>
              </el-table-column>

              <el-table-column align="center">
                <template slot-scope="scope">
                  <el-tag type="warning">{{ scope.row.sort }}</el-tag>
                </template>
              </el-table-column>

              <el-table-column label align="center">
                <template slot-scope="scope_child">
                  <template v-if="scope_child.row.status == 1">
                    <span>正常</span>
                  </template>
                  <template v-if="scope_child.row.status == 2">
                    <span>推荐</span>
                  </template>
                  <template v-if="scope_child.row.status == 0">
                    <span>已删除</span>
                  </template>
                </template>
              </el-table-column>

              <el-table-column fixed="right" min-width="180">
                <template slot-scope="scope_child">
                  <div style="display:flex; justify-content: space-between;">
                    <el-button
                      @click="handleStick(scope_child.row)"
                      type="warning"
                      size="small"
                      v-permission="'/categoryMenu/stick'"
                    >
                      置顶
                    </el-button>
                    <el-button
                      @click="handleEdit(scope.row, scope_child.row)"
                      type="primary"
                      size="small"
                      v-permission="'/categoryMenu/edit'"
                    >
                      编辑
                    </el-button>
                    <el-button
                      @click="handleDelete(scope_child.row)"
                      type="danger"
                      size="small"
                      v-permission="'/categoryMenu/delete'"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </template>
      </el-table-column>

      <el-table-column label="序号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="菜单名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="菜单级别" align="center">
        <template slot-scope="scope">
          <el-tag
            v-for="item in menuLevelDictList"
            :key="item.uid"
            v-if="scope.row.menuLevel == item.dictValue"
            :type="item.listClass"
            >{{ item.dictLabel }}</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column label="菜单类型" align="center">
        <template slot-scope="scope">
          <el-tag
            v-for="item in menuTypeDictList"
            :key="item.uid"
            v-if="scope.row.menuType == item.dictValue"
            :type="item.listClass"
            >{{ item.dictLabel }}</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column label="菜单简介" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.summary }}</span>
        </template>
      </el-table-column>

      <el-table-column label="功能类别" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.functionType == 1 ? "前台" : "后台" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="功能类别" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.icon }}</span>
          <i :class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column label="路由" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.url }}</span>
        </template>
      </el-table-column>

      <el-table-column label="是否显示" align="center">
        <template slot-scope="scope">
          <el-tag
            v-for="item in yesNoDictList"
            :key="item.uid"
            v-if="scope.row.isShow == item.dictValue"
            :type="item.listClass"
            >{{ item.dictLabel }}</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column label="排序" align="center">
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.sort }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.status == 1">
            <span>正常</span>
          </template>
          <template v-if="scope.row.status == 2">
            <span>推荐</span>
          </template>
          <template v-if="scope.row.status == 0">
            <span>已删除</span>
          </template>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" min-width="180">
        <template slot-scope="scope"> </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item
          label="接口名称"
          :label-width="formLabelWidth"
          prop="name"
        >
          <el-input
            v-model="form.name"
            placeholder="请输入接口名称"
            auto-complete="off"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="菜单类型"
          :label-width="formLabelWidth"
          prop="menuType"
        >
          <el-radio-group v-model="form.menuType" size="small" disabled>
            <el-radio
              v-for="item in menuTypeDictList"
              :key="item.uid"
              :label="parseInt(item.dictValue)"
              border
              >{{ item.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>

        <!-- <el-form-item
          label="菜单等级"
          :label-width="formLabelWidth"
          prop="menuLevel"
        >
          <el-select v-model="form.menuLevel" placeholder="请选择" disabled>
            <el-option
              v-for="item in menuLevelDictList"
              :key="item.uid"
              :label="item.dictLabel"
              :value="parseInt(item.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item> -->

        <!-- 用于接口 -->
        <el-form-item
          v-if="form.menuType == 1"
          label="父菜单名"
          :label-width="formLabelWidth"
        >
          <el-cascader
            :options="options"
            filterable
            placeholder="请选择父菜单"
            v-model="buttonParentUid"
            :props="{ checkStrictly: true, emitPath:false, children:'childCategoryMenu', label:'name', value:'uid' }"
            clearable
          ></el-cascader>
        </el-form-item>

        <el-form-item
          label="接口介绍"
          :label-width="formLabelWidth"
          prop="summary"
        >
          <el-input
            v-model="form.summary"
            placeholder="请输入接口简介"
            auto-complete="off"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="图标"
          :label-width="formLabelWidth"
          prop="icon"
          v-if="form.menuType == 0"
        >
          <el-input v-model="form.icon" auto-complete="off"></el-input>
        </el-form-item>

        <el-form-item label="路由" :label-width="formLabelWidth" prop="url">
          <el-input
            v-model="form.url"
            placeholder="路由对应的是前端router表中的路径"
            auto-complete="off"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="是否显示"
          :label-width="formLabelWidth"
          prop="isShow"
        >
          <el-radio-group v-model="form.isShow" size="small">
            <el-radio
              v-for="item in yesNoDictList"
              :key="item.uid"
              :label="parseInt(item.dictValue)"
              border
              >{{ item.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="功能类型"
          :label-width="formLabelWidth"
          prop="functionType"
        >
          <el-radio-group v-model="form.functionType" size="small">
            <el-radio :label="1" border>前台</el-radio>
            <el-radio :label="0" border>后台</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="排序" :label-width="formLabelWidth" prop="sort">
          <el-input v-model="form.sort" auto-complete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addMenu,
  deleteMenu,
  editMenu,
  getAllMenu,
  getButtonAll,
  stickMenu
} from "@/api/categoryMenu";
import { getListByDictTypeList } from "@/api/sysDictData";

export default {
  data() {
    return {
      functionType: null,
      showHeader: false, //是否显示表头
      tableData: [],
      keyword: '',
      menuLevel: "",
      currentPage: 1,
      pageSize: 10,
      total: 0, //总数量
      title: "增加接口",
      dialogFormVisible: false, //控制弹出框
      formLabelWidth: "120px",
      isEditForm: false,
      menuLevelDictList: [], //菜单等级字典
      yesNoDictList: [], // 是否字典
      menuTypeDictList: [], //菜单类型字典
      yesNoDefault: null, // 是否 默认值
      menuTypeDefault: null, // 菜单类型默认值
      buttonParentUid: '', // 选中的button父UID
      form: {
        uid: null,
        name: "",
        summary: "",
        icon: "",
        url: "",
        sort: 0,
        functionType: null
      },
      loading: false,
      rules: {
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1到20个字符" }
        ],
        menuLevel: [
          { required: true, message: "菜单等级不能为空", trigger: "blur" }
        ],
        parentUid: [
          { required: true, message: "父菜单名不能为空", trigger: "blur" }
        ],
        summary: [
          { required: true, message: "菜单简介不能为空", trigger: "blur" }
        ],
        icon: [{ required: true, message: "图标不能为空", trigger: "blur" }],
        url: [{ required: true, message: "URL不能为空", trigger: "blur" }],
        isShow: [
          { required: true, message: "显示字段不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "排序字段不能为空", trigger: "blur" },
          { pattern: /^[0-9]\d*$/, message: "排序字段只能为自然数" }
        ],
        functionType: [
          { required: true, message: "功能类型不能为空", trigger: "blur" }
        ]
      },
      options: []
    };
  },
  created() {
    // 得到菜单列表
    this.menuList();
    this.getDictList();
    this.buttonList();
  },
  methods: {
    menuList: function() {
      getAllMenu().then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          let tableData = response.data;
          function deepSetMenu(menus) { 
            menus.forEach(item => {
              // 菜单类型
              if (item.menuType == 0) {
                item.disabled = false;
              } else { 
                item.disabled = true;
              }
              if (item.childCategoryMenu.length) {
                deepSetMenu(item.childCategoryMenu);
              }
            });
          }
          deepSetMenu(tableData);
          this.options = tableData;
        }
      });
    },
    buttonList: function() {
      var params = {};
      let functionType = this.functionType;
      params.functionType = functionType;
      params.keyword = this.keyword;
      getButtonAll(params).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          this.tableData = response.data;
        }
      });
    },
    /**
     * 字典查询
     */
    getDictList: function() {
      var dictTypeList = ["sys_menu_level", "sys_yes_no", "sys_menu_type"];
      getListByDictTypeList(dictTypeList).then(response => {
        if (response.code == this.$ECode.SUCCESS) {
          var dictMap = response.data;

          this.menuLevelDictList = dictMap.sys_menu_level.list;

          this.yesNoDictList = dictMap.sys_yes_no.list;

          this.menuTypeDictList = dictMap.sys_menu_type.list;

          if (dictMap.sys_yes_no.defaultValue) {
            this.yesNoDefault = parseInt(dictMap.sys_yes_no.defaultValue);
          }

          if (dictMap.sys_menu_type.defaultValue) {
            this.menuTypeDefault = parseInt(dictMap.sys_menu_type.defaultValue);
          }
        }
      });
    },
    getFormObject: function() {
      var formObject = {
        uid: null,
        name: "",
        summary: "",
        icon: "",
        url: "",
        sort: 0,
        menuLevel: 3,
        isShow: this.yesNoDefault,
        isJumpExternalUrl: 0,
        menuType: 1
      };
      return formObject;
    },
    handleFind: function() {
      this.buttonList();
    },
    handleAdd: function() {
      this.title = "增加接口";
      this.dialogFormVisible = true;
      this.form = this.getFormObject();
      this.isEditForm = false;
    },
    handleEdit: function(parentRow, row) {
      this.dialogFormVisible = true;
      this.isEditForm = true;
      // 设置级联的父菜单名
      var parentUid = [];
      this.buttonParentUid = parentRow.parentUid;
      this.title = "编辑接口";
      this.form = Object.assign({}, row);
    },
    handleStick: function(row) {
      this.$confirm("此操作将会把该接口放到首位, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {};
          params.uid = row.uid;
          stickMenu(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.buttonList();
              this.$commonUtil.message.success(response.message);
            } else {
              this.$commonUtil.message.error(response.message);
            }
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消置顶");
        });
    },
    handleDelete: function(row) {
      var that = this;
      this.$confirm("此操作将把接口删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let params = {};
          params.uid = row.uid;
          deleteMenu(params).then(response => {
            if (response.code == this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message);
            } else {
              this.$commonUtil.message.error(response.message);
            }
            that.buttonList();
          });
        })
        .catch(() => {
          this.$commonUtil.message.info("已取消删除");
        });
    },

    submitForm: function() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          console.log("校验失败");
        } else {
          // 如果菜单类型是 接口，那么设置菜单等级为 3
          let menuType = this.form.menuType;
          if (menuType == 1) {
            this.form.menuLevel = 3;
          }
          let buttonParentUid = this.buttonParentUid;
          if (buttonParentUid) {
            // 选取最后一个元素
            this.form.parentUid = buttonParentUid;
          } else {
            this.$commonUtil.message.error("请选中父菜单");
            return;
          }

          if (this.isEditForm) {
            editMenu(this.form).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message);
                this.dialogFormVisible = false;
                this.buttonList();
              } else {
                this.$commonUtil.message.error(response.message);
              }
            });
          } else {
            addMenu(this.form).then(response => {
              if (response.code == this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message);
                this.dialogFormVisible = false;
                this.buttonList();
              } else {
                this.$commonUtil.message.error(response.message);
              }
            });
          }
        }
      });
    }
  }
};
</script>
<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>
