/**
 * 嵌套request存放次数，
 *  => 某个方法需要先发送request请求，拿到数据后再根据该数据发送request时使用
 */

const requestNum = {
  state: {
    number: 0
  },

  mutations: {
    SET_NUMBER: state => {
      state.number++;
    },
    GET_NUMBER: state => {
      state.number--;
    },
    CLEAR_NUMBER: state => {
      state.number = 0;
    }
  },

  actions: {
    // 发送请求加一
    sendRequest({ commit }) {
      commit("SET_NUMBER");
    },
    // 请求返回减一
    returnRequest({ commit }) {
      commit("GET_NUMBER");
    },
    // 发生错误清零
    clearNumber({ commit }) {
      commit("CLEAR_NUMBER");
    }
  }
};

export default requestNum;
