<template>
  <div class="app-container f fd-c">
    <!-- 查询和其他操作 -->
    <div class="filter-container" style="margin: 10px 0 10px 0;">
      <div class="left">
        <el-input
          v-model="searchForm.name"
          size="mini"
          clearable
          class="filter-item"
          style="width: 200px; margin-right:10px"
          placeholder="请输入接口名称"/>
        <el-input
          v-model="searchForm.url"
          size="mini"
          clearable
          class="filter-item"
          style="width: 200px; margin-right:10px"
          placeholder="请输入接口路径"/>
        <el-input
          v-model="searchForm.remark"
          size="mini"
          clearable
          class="filter-item"
          style="width: 200px; margin-right:10px"
          placeholder="请输入备注"/>
        <div style="margin-right:10px">
          <el-select
            v-model="searchForm.status"
            size="small"
            clearable
            placeholder="全部状态"
            style="width:140px  margin-right:10px"
            @change="pageList()"
            @clear="pageList()">
            <el-option label="已删除" value="0"/>
            <el-option label="生效中" value="1"/>
          </el-select>
          <el-select
            v-model="searchForm.type"
            size="small"
            clearable
            placeholder="全部类型"
            style="width:140px  margin-right:10px"
            @change="pageList()"
            @clear="pageList()">
            <el-option label="后台" value="0"/>
            <el-option label="前台" value="1"/>
          </el-select>
        </div>
        <el-button
          v-permission="'/interfaceSkip/pageList'"
          size="mini"
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFind">
          查找
        </el-button>
        <el-button
          v-permission="'/interfaceSkip/add'"
          size="mini"
          class="filter-item"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd">
          添加白名单
        </el-button>
      </div>
    </div>

    <el-table :data="tableData" height="auto" class="f1" style="width: 100%">
      <el-table-column type="selection"/>

      <el-table-column label="接口名称" >
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="接口路径" >
        <template slot-scope="scope">
          <span>{{ scope.row.url }}</span>
        </template>
      </el-table-column>

      <el-table-column label="接口类型" >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" type="success">前台</el-tag>
          <el-tag v-if="scope.row.type === 0" type="info">后台</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="添加人" >
        <template slot-scope="scope">
          <span>{{ scope.row.createByName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="添加时间" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>

      <el-table-column label="更新时间" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.updateTime }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <div ref="operation" style="display:flex;justify-content:space-between;">
            <el-button
              v-permission="'/interfaceSkip/edit'"
              type="primary"
              size="small"
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              v-permission="'/interfaceSkip/restore'"
              v-if="scope.row.status == 0"
              type="success"
              size="small"
              @click="restoreSkip(scope.row)">
              恢复
            </el-button>
            <el-button
              v-permission="'/interfaceSkip/deleteBatch'"
              v-if="scope.row.status == 1"
              type="danger"
              size="small"
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </div>
          <!--          <el-button @click="handRest(scope.row)" type="warning" size="small" v-permission="'/admin/restPwd'">重置密码</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <!--分页-->
    <div class="block">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :total="total"
        style="float: right; margin-top:10px"
        layout="total, prev, pager, next, jumper"
        @current-change="handleCurrentChange"/>
    </div>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item
          label="接口名称"
          :label-width="formLabelWidth"
          prop="name"
        >
          <el-input
            v-model="form.name"
            placeholder="请输入接口名称"
            auto-complete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="接口介绍"
          :label-width="formLabelWidth"
          prop="remark"
        >
          <el-input
            v-model="form.remark"
            placeholder="请输入接口简介"
            auto-complete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="路由" :label-width="formLabelWidth" prop="url">
          <el-input
            v-model="form.url"
            placeholder="路由对应的是前端router表中的路径"
            auto-complete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="功能类型"
          :label-width="formLabelWidth"
          prop="type"
        >
          <el-radio-group v-model="form.type" size="small">
            <el-radio :label="1" border>前台</el-radio>
            <el-radio :label="0" border>后台</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <avatar-cropper
      v-show="imagecropperShow"
      :key="imagecropperKey"
      :width="300"
      :height="300"
      :url="url"
      lang-type="zh"
      @close="close"
      @crop-upload-success="cropSuccess" />
  </div>
</template>

<script>

import {
  getSkipList,
  addSkip,
  editSkip,
  deleteSkip,
  restoreSkip
} from '@/api/interface'

import AvatarCropper from '@/components/AvatarCropper'

export default { 
  components: {
    AvatarCropper
  },
  data() {
    return {
      url:'',
      imagecropperKey:"",
      imagecropperShow:false,
      tableData: [],
      roleOptions: [], // 角色候选框
      loading: false, // 搜索框加载状态
      roleData: [], // 角色列表
      roleValue: [], // 选择的角色列表
      keyword: '',
      searchForm: {
        name: '',
        url: '',
        remark: '',
        type: null,
        status: null
      },
      currentPage: 1,
      pageSize: 10,
      total: 0, // 总数量
      title: '增加白名单',
      dialogFormVisible: false, // 控制弹出框
      formLabelWidth: '120px',
      isEditForm: false,
      form: {},
      rules: {
        name: [
          { required: true, message: '接口名称不能为空', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '接口路由不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '接口类型不能为空', trigger: 'blur' }
        ]
      },
      uid: ''
    }
  },
  created() {
    this.uid = window.localStorage.getItem('huanyu-admin-i')
    this.pageList()
  },
  methods: {
    pageList: function() {
      const params = {}
      params.url = this.searchForm.url
      params.name = this.searchForm.name
      params.remark = this.searchForm.remark
      params.status = this.searchForm.status
      params.type = this.searchForm.type
      params.currentPage = this.currentPage
      params.pageSize = this.pageSize
      getSkipList(params).then(response => {
        if (response.code === this.$ECode.SUCCESS) {
          const tableData = response.data.records
          for (let a = 0; a < tableData.length; a++) {
            tableData[a].maxStorageSize =
              tableData[a].maxStorageSize / 1024 / 1024
          }
          this.tableData = tableData
          // 设置操作栏长度
          this.$nextTick(() =>
            this.$store.dispatch(
              'setWidth',
              this.$refs.operation.children.length
            )
          )
          this.currentPage = response.data.current
          this.pageSize = response.data.size
          this.total = response.data.total
        }
      })
    },
    cropSuccess(resData) {
      this.imagecropperShow = false
      this.imagecropperKey = this.imagecropperKey + 1
      const photoList = []
      photoList.push(resData[0].url)
      this.form.photoList = photoList
      this.form.avatar = resData[0].uid
    },
    close() {
      this.imagecropperShow = false
    },
    deletePhoto: function() {
      this.form.photoList = null
      this.form.ava = ''
      this.icon = false
    },
    checkPhoto() {
      this.photoList = []
      this.avatar = ''
      this.imagecropperShow = true
    },
    getFormObject: function() {
      var formObject = {
        uid: null,
        name: "",
        url: "",
        remark: "",
        type: 1,
        isShow: this.yesNoDefault
      };
      return formObject;
    },
    handleFind: function() {
      this.pageList()
    },
    handleAdd: function() {
      this.title = '增加白名单'
      this.dialogFormVisible = true
      this.form = this.getFormObject()
      this.isEditForm = false
    },
    handleEdit: function(row) {
      this.title = '编辑白名单'
      this.dialogFormVisible = true
      this.isEditForm = true
      this.form = Object.assign({}, row)
      this.roleValue = []
      var roleList = []
      // 设置选择的角色列表
      if (row.roleList) {
        row.roleList.forEach(element => {
          roleList.push(element.uid)
        })
        this.roleValue = roleList
      }
    },
    restoreSkip: function(row) {
      let title = '停用'
      if (row.status === 1) {
        title = '恢复'
      }
      this.$confirm('此操作将' + title + '该白名单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const uidList = []
          uidList.push(row.uid)
          restoreSkip(uidList).then(response => {
            if (response.code === this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
            this.pageList()
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消操作')
        })
    },
    handleDelete: function(row) {
      this.$confirm('此操作将删除该接口白名单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const uidList = []
          uidList.push(row.uid)
          deleteSkip(uidList).then(response => {
            if (response.code === this.$ECode.SUCCESS) {
              this.$commonUtil.message.success(response.message)
            } else {
              this.$commonUtil.message.error(response.message)
            }
            this.pageList()
          })
        })
        .catch(() => {
          this.$commonUtil.message.info('已取消删除')
        })
    },
    handleCurrentChange: function(val) {
      this.currentPage = val
      this.pageList()
    },
    submitForm: function() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          console.log('校验出错')
        } else {
          if (this.isEditForm) {
            editSkip(this.form).then(response => {
              if (response.code === this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message)
                this.dialogFormVisible = false
                this.pageList()
              } else {
                this.$commonUtil.message.error(response.message)
              }
            })
          } else {
            addSkip(this.form).then(response => {
              if (response.code === this.$ECode.SUCCESS) {
                this.$commonUtil.message.success(response.message)
                this.dialogFormVisible = false
                this.pageList()
              } else {
                this.$commonUtil.message.error(response.message)
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  height: calc(100vh - 130px);
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin: 0, 0, 0, 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.imgBody {
  width: 100px;
  height: 100px;
  border: solid 2px #ffffff;
  float: left;
  position: relative;
}

.uploadImgBody {
  margin-left: 5px;
  width: 100px;
  height: 100px;
  border: dashed 1px #c0c0c0;
  float: left;
  position: relative;
}

.uploadImgBody :hover {
  border: dashed 1px #00ccff;
}

.inputClass {
  position: absolute;
}

img {
  width: 100px;
  height: 100px;
}

.filter-container {
  display: flex;
  justify-content: space-between;

  .left {
    display: flex;
    align-items: center;
  }
}
</style>
