import request from "@/utils/request";
//获取全部套餐列表
export function getAllSetMealList(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/getPage",
        method: "post",
        data: params
    });
}

// 添加套餐
export function addSetMeal(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/addSetMeal",
        method: "post",
        data: params
    });
}

// 修改套餐
export function updateSetMeal(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/updSetMeal",
        method: "post",
        data: params
    });
}

// 获取套餐信息
export function getSetMealInfo(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/getInfo",
        method: "get",
        params
    });
}

// 删除套餐
export function deleteSetMeal(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/delSetMeal",
        method: "post",
        data: params
    });
}

// 发布或者下架套餐
export function releaseSetMeal(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/releaseSetMeal",
        method: "post",
        data: params
    });
}

// 添加或修改目录
export function addOrUpdDirectory(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealDirectory/addOrUpdDirectory",
        method: "post",
        data: params
    });
}

// 根据套餐ID获取目录列表
export function getListBySetMealUid(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealDirectory/getListBySetMealUid",
        method: "get",
        params
    });
}

// 删除目录
export function delDirectory(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealDirectory/delDirectory",
        method: "post",
        data: params
    });
}

// 套餐添加课程
export function setMealAddCourse(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealCourse/addCourse",
        method: "post",
        data: params
    });
}

// 根据套餐ID和目录ID获取套餐课程分页
export function getSetMealCourseList(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealCourse/getPage",
        method: "post",
        data: params
    });
}

// 删除套餐下的课程
export function delSetMealCourse(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealCourse/delCourseById",
        method: "post",
        data: params
    });
}

// 获取套餐目录总数和课程总数
export function getSetMealTotal(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/getTotal",
        method: "get",
        params
    });
}

// 根据套餐ID获取套餐资料
export function getSetMealFileList(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealFile/getPage",
        method: "post",
        data: params
    });
}

// 修改套餐资料是否是免费
export function updateSetMealFile(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealFile/updMealFile",
        method: "post",
        data: params
    });
}

// 删除套餐资料
export function deleteSetMealFile(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealFile/delMealFile",
        method: "post",
        data: params
    });
}

// 添加套餐资料
export function addSetMealFile(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMealFile/addMealFile",
        method: "post",
        data: params
    });
}

// 套餐阶段拖动排序
export function dragSortState(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/admin/setMealDirectory/dragSort",
        method: "post",
        data: params
    });
}


// 套餐课程拖动排序
export function dragSortCourse(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/admin/setMealCourse/dragSort",
        method: "post",
        data: params
    });
}

// 套餐课程审核
export function artificialAudit(params = {}) {
    return request({
        url: process.env.ADMIN_API + "/setMeal/artificialAudit",
        method: "post",
        data: params
    });
}