<template>
  <div class="tiny-mce-container">
    <textarea maxlength="20" v-show="show" :id="editorId">
        {{ content }}
    </textarea>
  </div>
</template>
<script>
import { uploadFile } from '@/api/file'
import '@/utils/wordlimit.js'
export default {
    name: 'tinyMce',
    model: {
        prop: 'content',
        // 随便命名事件，对应下面$emit即可
        event: 'input'
    },
    beforeDestroy() {
        tinymce.remove();
    },
    props: {
        editorId: {
            type: String,
            default: 'tiny-mce-textarea'
        },
        minHeight: {
            type: Number,
            default: 500
        },
        maxHeight: {
            type: Number,
            default: 500
        },
        height: {
            type: Number,
            default: 500
        },
        content: {
            type: String,
            default: ''
        },
        toolbar: {
            type: Array,
            default: () => [
                'wordcount newdocument undo redo removeformat blockquote | numlist bullist | link bold italic underline strikethrough | lineheight letterspacing | fontfamily fontsize blocks | forecolor backcolor | template codesample | alignleft aligncenter alignright alignjustify | outdent indent',
                'table preview | paste pastetext cut copy | fullscreen | insertdatetime pagebreak quickvideo quickimage code emoticons | ltr rtl'
            ]
        },
        placeholder:{ 
            type: String,
            default:"暂无内容"
        },
        showMenuBar: {
            type: Boolean,
            default: true
        },
        readonly: {
            type: Boolean,
            default: false
        },
        showToolbar: {
            type: Boolean,
            default: true
        }
    },
    watch: { 
        content: { 
            handler: function (newVal,oldVal) {
                if (tinymce && !this.stopWatchShowContent) { 
                    this.$nextTick(() => {
                        if (tinymce && tinymce.get) {
                            tinymce.get(this.editorId).setContent(newVal);
                        }
                    });
                }    
            },
            immediate: true
        }  
    },
    methods: {
        // 视频上传
        async video_upload_handler(blob) { 
            return new Promise(async(resolve, reject) => {
                let file = blob;
                if (!/(mp4|mpeg)$/.test(file.type)) {
                    return reject({ message: '视频类型必须是.mp4,mpeg中的一种', remove: true });
                }
                let formData = new FormData();
                formData.append("file", file);
                formData.append("userUid", "uid00000000000000000000000000000000");
                formData.append("source", "picture");
                formData.append("sortName", "admin");
                formData.append("projectName", "blog");
                let result = await uploadFile(formData).catch(err => {
                    return reject({ message: err.message, remove: true });
                })
                //上传成功
                if (result.code === this.$ECode.SUCCESS) {
                    let videoUrl = result.data[0].url;
                    resolve(videoUrl);
                }
            });
        },
        images_upload_handler(blobInfo, progress) { 
            let file = blobInfo.blob();
            console.log(file)
            return new Promise(async (resolve, reject) => {
                if (!/(gif|jpg|jpeg|png|bmp|GIF|JPG|PNG)$/.test(file.type)) {
                    return reject({ message: '图片类型必须是.gif,jpeg,jpg,png,bmp中的一种', remove: true });
                }
                let formData = new FormData();
                formData.append("file", file);
                formData.append("userUid", "uid00000000000000000000000000000000");
                formData.append("source", "picture");
                formData.append("sortName", "admin");
                formData.append("projectName", "blog");
                let result = await uploadFile(formData).catch(err => { 
                    return reject({ message: err.message, remove: true });
                })
                //上传成功
                if (result.code === this.$ECode.SUCCESS) {
                    let videoUrl = result.data[0].url;
                    resolve(videoUrl);
                }
            });
        }
    },
    mounted() {
        // 展示textareas
        this.show = true,
        tinymce.init({
            selector: `#${this.editorId}`,
            language: 'zh-Hans',
            promotion: false,
            statusbar: true,
            readonly: this.readonly,
            paste_data_images: true,
            images_upload_handler: this.images_upload_handler,
            video_upload_handler:this.video_upload_handler,
            min_height: this.minHeight,
            max_height: this.maxHeight,
            height: this.height,
            quickbars_selection_toolbar: 'forecolor backcolor bold italic | quicklink h2 h3 blockquote quickimage quickvideo quicktable | alignleft aligncenter alignright alignjustify',
            placeholder: this.placeholder,
            font_family_formats:"微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
            font_size_formats:"26pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 9pt 7.5pt",
            menubar: this.showMenuBar ? 'wordcount file edit view insert format tools table help' : false,
            image_advtab: true,//图片高级选项
            templates: [
                {
                    title: '模板一',
                    description: '模板一',
                    content: '<span>模板一</span>'
                }
            ],
            plugins: [
                'wordcount','table','quickbars','directionality', 'lists', 'advlist', 'link', 'template', 'preview', 'fullscreen', 'letterspacing',
                'insertdatetime','pagebreak','quickvideo','image','emoticons','code','autoresize','wordlimit'
            ],
            toolbar: this.showToolbar ? this.toolbar : false,
            setup: editor => {
                //初始化完成后的一个钩子
                editor.on("init", function () {

                });

                //文本内容改变的钩子
                editor.on("input Change Undo Redo",async () => {
                    this.stopWatchShowContent = true;
                    //这里联动父级，给父级传递文本内容
                    this.$emit("input", editor.getContent());
                    await this.$nextTick();
                    //防止输入,监听重复设置内容
                    this.stopWatchShowContent = false;
                });
            },
            wordlimit: {
                max: 2100000000, // 最多可以输入多少字
                spaces: false, // 是否含空格
                isInput: false, // 是否在超出后还可以输入
                // 自定义的提示方法, 默认用编辑器自带
                toast: function (message) {

                }
            },
            // 初始化实例的回调
            init_instance_callback: (editor) => {
                // 只有超出字数限制才会触发
                editor.on('wordlimit', (e) => {
                    console.log(e.wordCount,e.maxCount);
                });
            },
        });
    },
    data(){
        return {
            show:false
        }
    }
}
</script>
<style lang='scss' scoped>
.tox{
        z-index: 99999;
    }
.tiny-mce-container{
    textarea{
        opacity: 0.0001;
    }
}
</style>