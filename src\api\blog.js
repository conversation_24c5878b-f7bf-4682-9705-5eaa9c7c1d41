import request from '@/utils/request'

export function getBlogList(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/getList',
    method: 'post',
    data: params
  })
}

export function addBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/add',
    method: 'post',
    data: params
  })
}

export function uploadLocalBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/uploadLocalBlog',
    method: 'post',
    data: params
  })
}

export function editBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/edit',
    method: 'post',
    data: params
  })
}

export function articleEaudit(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/articleEaudit',
    method: 'post',
    data: params
  })
}

export function editBatchBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/editBatch',
    method: 'post',
    data: params
  })
}

export function deleteBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/delete',
    method: 'post',
    data: params
  })
}

export function deleteBatchBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/deleteBatch',
    method: 'post',
    data: params
  })
}

export function adminDelete(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/adminDelete',
    method: 'post',
    data: params
  })
}

export function adminDeleteBatch(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/adminDeleteBatch',
    method: 'post',
    data: params
  })
}

// 置顶博客
export function blogSetTop(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/setTop',
    method: 'post',
    data: params
  })
}

// 置顶博客-取消
export function blogUnTop(params) {
  return request({
    url: process.env.ADMIN_API + '/blog/unTop',
    method: 'post',
    data: params
  })
}

// 查询点赞评论列表
export function blogSelectCommentBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/selectCommentBlog',
    method: 'post',
    data: params
  })
}

// 删除点赞评论列表
export function blogDeleteCommentBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/deleteCommentBlog',
    method: 'post',
    data: params
  })
}

// 查询收藏列表
export function blogSelectCollectBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/selectCollectBlog',
    method: 'post',
    data: params
  })
}

// 删除收藏列表
export function blogDeleteCollectBlog(params) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/deleteCollectBlog',
    method: 'post',
    data: params
  })
}

//文章审核
export function selectCheckBlogList(params) { 
  return request({
    url: process.env.ADMIN_API + '/admin/blog/selectBlogList',
    method: 'post',
    data: params
  })
}

//所属栏目列表
export function columnList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/selectColumn',
    method: 'get',
    params
  })
}

//所属栏目列表
export function batchCheck(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/updateBlogStatus',
    method: 'post',
    data:params
  })
}

//所属栏目列表
export function batchDelete(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/deleteBlogList',
    method: 'post',
    data:params
  })
}


// 批量删除文章
export function batchDeleteBlogList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/deleteBlogList',
    method: 'post',
    data: params
  })
}

// 批量审核文章
export function updateBlogStatus(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/updateBlogStatus',
    method: 'post',
    data: params
  })
}

//视频、素材审核列表
export function selectWaitForManualCheckList(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/newMedia/selectWaitForManualCheckList',
    method: 'post',
    data: params
  })
}

// 批量审核视频、图片
export function manualBatchCheck(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/newMedia/manualBatchCheck',
    method: 'post',
    data: params
  })
}

// 批量删除
export function batchDeleteMaterial(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/newMedia/batchDeleteMaterial',
    method: 'post',
    data: params
  })
}

// 查看文章详情
export function watchArticleDetail(params = {}) {
  return request({
    url: process.env.ADMIN_API + '/admin/blog/selectBlogByIdAndType',
    method: 'get',
    params
  })
}