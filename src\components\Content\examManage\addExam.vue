<template>
  <div class="addCamp-period-container">
    <!-- 创建完成 -->
    <template v-if="finish">
      <div class="finish">
        <i class="el-icon-success"/>
        <p class="title">{{ examParentThis.editorFlag == 1 ?'添加试卷成功！':'编辑试卷成功！' }}</p>
        <!-- <p class="desc">为了给参与者带来更好的体验，建议您进行结课证书设置</p> -->
        <el-button @click="goBackList">返回列表</el-button>
      </div>
    </template>
    <template v-else>
      <!-- 步骤条 -->
      <div class="step">
        <el-steps :active="active" simple>
          <el-step
            v-for="(item, index) in stepArr"
            :key="index"
            :title="item"
            :icon="index + 1 == active ? 'el-icon-edit' : 'el-icon-tickets'"/>
        </el-steps>
      </div>
      <!-- form表单区域 -->
      <div class="form-box">
        <el-form
          ref="addExamForm"
          :rules="addExamPeriodRule"
          :model="addExamForm"
          class="form"
          label-position="right"
          label-width="110px">
          <!-- 考试基础信息 -->
          <template v-if="active == 1">
            <div class="base-info">
              <span>基本信息</span>
            </div>
            <el-form-item label="考试名称" prop="examName">
              <el-input v-model="addExamForm.examName"/>
            </el-form-item>
            <el-form-item label="考试封面" prop="examPeriodCover">
              <el-upload
                :auto-upload="false"
                :on-change="changeCover"
                :show-file-list="false"
                class="avatar-uploader"
                action="">
                <img
                  v-if="addExamForm.examPeriodCover"
                  :src="addExamForm.examPeriodCover"
                  class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"/>
              </el-upload>
            </el-form-item>
            <el-form-item label="考试详情" prop="examContent">
              <el-input v-model="addExamForm.examContent" rows="6" type="textarea"/>
            </el-form-item>
            <el-form-item label="参考人员">
              <span slot="label">
                参考人员
                <el-tooltip class="item" effect="light" placement="top">
                  <div slot="content">选择“同步培训班学员”，此时考生列表与培训班的学员列表一致，不可进行更改，若未关联培训班，考生列表为空；选择“不同步”时候，考生列表为空，可自行添加考生。</div>
                  <i class="el-icon-warning-outline"/>
                </el-tooltip>
              </span>
              <el-radio-group v-model="addExamForm.synchronizationClass" :disabled="examParentThis.editorFlag === 2">
                <el-radio :label="0">同步培训班学员</el-radio>
                <el-radio :label="1">不同步</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addExamForm.synchronizationClass === 0" label="考试类型">
              <el-radio-group v-model="addExamForm.examType" :disabled="examParentThis.editorFlag === 2">
                <el-radio :label="0">模拟考试</el-radio>
                <el-radio :label="1">在线考试</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addExamForm.synchronizationClass === 0" label="关联课程" prop="campPeriodDetail">
              <el-button size="small" type="primary" @click="associatedCourses">关联课程</el-button>
            </el-form-item>
            <div v-if="addExamForm.synchronizationClass === 0" class="selected-course">
              <div v-for="item in selectedCourseArr" :key="item.uid" class="item">
                <span>{{ item | showTitleFilter }}</span>
                <el-tag>{{ item | showTypeTagFilter }}</el-tag>
                <div class="close" @click="closeItem(item)">
                  <i class="el-icon-circle-close"/>
                </div>
              </div>
            </div>
            <el-form-item label="关联证书">
              <el-select
                v-model="addExamForm.certificateUid"
                placeholder="请选择关联证书"
                clearable
                filterable
              >
                <el-option
                  v-for="item in certificateRegionList"
                  :key="item.uid"
                  :label="item.shortName"
                  :value="item.uid"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="addExamForm.examType !== 0 || addExamForm.synchronizationClass === 1" label="考试须知">
              <el-radio-group v-model="addExamForm.showExaminationNotes">
                <el-radio :label="1">展示</el-radio>
                <el-radio :label="0">不展示</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addExamForm.showExaminationNotes === 1" prop="examinationNotes">
              <el-input v-model="addExamForm.examinationNotes" rows="6" type="textarea" maxlength="1000"/>
            </el-form-item>
            <div v-if="addExamForm.examType !== 0 || addExamForm.synchronizationClass === 1" class="open-class-info">
              <span>信息采集</span>
            </div>
            <!-- <el-form-item label="信息采集" prop="admissionsTime">
              <el-radio-group v-model="addExamForm.informationAcquisition">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
              <div>
                <span>开启后，可在特定页面采集用户信息</span>
              </div>
            </el-form-item> -->
            <el-form-item v-if="addExamForm.examType !== 0 || addExamForm.synchronizationClass === 1" label="身份验证">
              <el-radio-group v-model="addExamForm.authentication">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addExamForm.examType !== 0 || addExamForm.synchronizationClass === 1" label="人脸识别">
              <el-radio-group v-model="addExamForm.facialVerification">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <div class="mode-info">
              <span>考试设置</span>
            </div>
            <el-form-item label="参与考试时间">
              <el-radio-group v-model="addExamForm.limitExaminationTime" :disabled="addExamForm.synchronizationClass === 1 || addExamForm.examType === 1">
                <el-radio :label="0">不限制</el-radio>
                <el-radio :label="1">限时</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addExamForm.limitExaminationTime == 1" class="exam-time" label="" prop="examTime">
              <el-date-picker
                v-model="addExamForm.examTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"/>
              <div><span>限制考试时长的情况下，学员考试中离开，倒计时不会停止，建议提醒学员不要长时间离开考试</span></div>
            </el-form-item>
            <el-form-item label="参与考试时长" prop="examinationDuration">
              <div class="exam-time-long">
                <el-input v-model="addExamForm.examinationDuration" placeholder="0表示无限制"/>
                <span>分钟</span>
              </div>
              <span>限制考试时长的情况下，学员考试中离开，倒计时不会停止，建议提醒学员不要长时间离开考试</span>
            </el-form-item>
            <el-form-item v-if="addExamForm.limitExaminationTime == 1" label="禁止参与考试" prop="closedTime">
              <div class="exam-time-long">
                <el-input v-model="addExamForm.closedTime"/>
                <span>分钟内未进入考试的考生禁止参与考生</span>
              </div>
            </el-form-item>
            <el-form-item label="参与考试次数">
              <el-radio-group v-model="addExamForm.examNum">
                <el-radio :label="-1">不限次数</el-radio>
                <el-radio :label="2">限制</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addExamForm.examNum==2" class="examCount-box" label="限制" prop="examCount">
              <el-input v-model="addExamForm.examCount"/>
              <span>次</span>
            </el-form-item>
            <el-form-item label="重考间隔" prop="reExamSpan">
              <div class="retest-interval">
                <el-input v-model="addExamForm.reExamSpan"/>
                <span>分钟</span>
              </div>
              <span>0分钟表示无限制</span>
            </el-form-item>
            <!-- <el-form-item label="答题水印">
              <el-switch v-model="addExamForm.watermark" :inactive-value="0" :active-value="1" active-color="#13ce66" inactive-color="#ff4949"/>
              <span>关闭水印</span>
            </el-form-item>
            <el-form-item label="重考限制">
              <el-radio-group v-model="addExamForm.reExamCondition">
                <el-radio :label="1">批阅后可重考</el-radio>
                <el-radio :label="0">未批阅可重考</el-radio>
              </el-radio-group>
            </el-form-item>
            <div class="mode-info">
              <span>结果设置</span>
            </div>
            <el-form-item label="成绩展示">
              <el-radio-group v-model="addExamForm.scoreDisplay">
                <el-radio :label="1">批改完成后立即展示</el-radio>
                <el-radio :label="0">隐藏（不展示成绩）
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="题目展示">
              <el-radio-group v-model="addExamForm.problemDisplay">
                <el-radio :label="1">提交考试后立即展示</el-radio>
                <el-radio :label="2">批改完成后展示</el-radio>
                <el-radio :label="0">隐藏</el-radio>
              </el-radio-group>
              <p>可配置考试结束后，题目是否展示</p>
            </el-form-item>
            <el-form-item label="答案展示">
              <el-radio-group v-model="addExamForm.problemScoreDisplay">
                <el-radio :label="1">提交考试后立即展示</el-radio>
                <el-radio :label="2">批改完成后展示</el-radio>
                <el-radio :label="0">隐藏</el-radio>
              </el-radio-group>
              <p>可配置考试结束后，答案是否展示</p>
            </el-form-item>
             -->
          </template>
          <!-- 选择试卷 -->
          <template v-if="active == 2">
            <div class="exam-select">
              <div class="exam-question-bank">
                <el-form-item label="" label-width="0" prop="paperUid">
                  <el-button @click="selectPapers">从试卷库中进行选择</el-button>
                </el-form-item>
              </div>
              <div class="right">
                <el-tag>当前试卷ID：{{ addExamForm.paperUid || examParentThis.editExamInfo.uid ? addExamForm.paperUid||examParentThis.editExamInfo.uid :'您当前未选择试卷' }}</el-tag>
                <el-tag style="margin-top:10px;" type="info">试卷名称：{{ addExamForm.paperName }}</el-tag>
                <el-form-item label="是否设置通过分数" label-width="130px">
                  <el-radio-group v-model="addExamForm.limitPassExam">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="addExamForm.limitPassExam==1" label-width="0" label="">
                  <el-input v-model="addExamForm.passExamScore" placeholder="请输入考试通过的分数"/>
                </el-form-item>
              </div>
            </div>
          </template>
          <!-- 发布设置 -->
          <template v-if="active == 3">
            <div class="shop-info">
              <span>发布设置</span>
            </div>
            <el-form-item class="release-set" label="发布设置">
              <div class="release-type">
                <el-radio-group v-model="addExamForm.scheduledRelease">
                  <el-radio :label="1">立即发布</el-radio>
                  <!-- 未关联课程的考试无法设置定时发布 -->
                  <el-radio :label="2">定时发布</el-radio>
                  <el-radio :label="0">暂不发布（选择后无法推送考试提醒）</el-radio>
                </el-radio-group>
                <el-form-item v-if="addExamForm.scheduledRelease ==2" class="timing-release" label="">
                  <el-date-picker v-model="addExamForm.releaseTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间"/>
                </el-form-item>
              </div>
            </el-form-item>
          </template>
        </el-form>
      </div>

      <!-- 取消、上一步、下一步 -->
      <div
        :style="[
        sidebar.opened ? { width: 'calc(100% - 180px)' }:{width: 'calc(100% - 35px)' } ]"
        class="submit-btn">
        <el-button v-if="active == 1" type="info" @click="cancel">取消</el-button>
        <el-button v-if="active != 1" @click="pre">上一步</el-button>
        <el-button v-if="active != 3" type="primary" @click="next">下一步</el-button>
        <el-button v-if="active == 3" type="primary" @click="complete">完成</el-button>
      </div>
    </template>
    <!-- 关联课程弹窗 -->
    <template>
      <el-dialog
        :visible.sync="associatedCoursesDialog"
        class="associatedCoursesDialog"
        title="关联课程"
        width="45%"
        center>
        <el-tabs v-model="associatedCoursesType" @tab-click="associatedCoursesTab">
          <el-tab-pane label="专栏" name="3">
            <el-table v-loadMore="columnLoad" ref="columnForm" :row-key="getRowKeys" :data="columnModel.columnList" height="300" style="width: 100%" @selection-change="columnSelectionChange">
              <el-table-column :reserve-selection="true" type="selection" width="55"/>
              <!-- <el-table-column align="center" prop="uid" label="专栏ID" show-overflow-tooltip width="180">
                            </el-table-column> -->
              <el-table-column align="center" prop="title" label="标题" show-overflow-tooltip/>
              <el-table-column align="center" prop="coverUrl" label="封面" width="180">
                <template slot-scope="scope">
                  <el-image
                    :preview-src-list="[scope.row.coverUrl]"
                    :src="scope.row.coverUrl"
                    style="width: 100px; height: 100px"
                    fit="fill"/>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="视频" name="2">
            <el-table v-loadMore="videoLoad" ref="videoForm" :row-key="getRowKeys" :data="videoModel.videoList" height="300" style="width: 100%" @selection-change="videoSelectionChange">
              <el-table-column :reserve-selection="true" type="selection" width="55"/>
              <el-table-column align="center" prop="videoUid" label="视频ID" show-overflow-tooltip width="180"/>
              <el-table-column align="center" prop="title" label="标题"/>
              <el-table-column align="center" prop="fileUrl" label="视频封面" width="180">
                <template slot-scope="scope">
                  <el-image
                    :preview-src-list="[scope.row.fileUrl]"
                    :src="scope.row.fileUrl"
                    style="width: 100px; height: 100px"
                    fit="fill"/>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="训练营" name="1">
            <el-table v-loadMore="trainingLoad" ref="trainingForm" :row-key="getRowKeys" :data="trainingModel.trainingList" height="300" style="width: 100%" @selection-change="trainingSelectionChange">
              <el-table-column :reserve-selection="true" type="selection" width="55"/>
              <el-table-column prop="campName" label="名称" width="180"/>
              <el-table-column prop="campPeriodNumber" label="营期数" width="180"/>
              <el-table-column prop="enrollment" label="报名数"/>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="培训班" name="5">
            <el-table v-loadMore="trainingCourseLoad" ref="trainingCourseForm" :row-key="getRowKeys" :data="trainingCourseModel.trainingCourseList" height="300" style="width: 100%" @selection-change="trainingCourseSelectionChange">
              <el-table-column :reserve-selection="true" type="selection" width="55"/>
              <el-table-column prop="uid" label="培训班ID" show-overflow-tooltip/>
              <el-table-column prop="name" label="名称" show-overflow-tooltip/>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
          <el-button @click="associatedCoursesDialog = false">取 消</el-button>
          <el-button type="primary" @click="associatedCoursesDialog = false">确 定</el-button>
        </span>
      </el-dialog>
    </template>
    <!-- 试卷库弹窗 -->
    <template>
      <el-dialog :visible.sync="papersDialogVisible" title="选择试卷" width="70%">
        <libraryExaminationPapers :current-select-papers.sync="currentSelectPapers"/>
        <span slot="footer" class="dialog-footer">
          <el-button @click="papersDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="realAddPaper">确 定</el-button>
        </span>
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { getDropDownList } from '@/api/authentication.js'
import { getColumnPageList } from '@/api/content/column'
import { addExam, editExam } from '@/api/content/exam'
import { getCampList } from '@/api/content/trainingCamp'
import { videoList } from '@/api/content/video'
import { getListData } from '@/api/course/course'
import { uploadFile } from '@/api/upload'
import libraryExaminationPapers from '@/components/Content/examManage/libraryExaminationPapers.vue'
import { getToken } from '@/utils/auth'
import { debounce } from '@/utils/commonUtil'
import dayjs from 'dayjs'
export default {
  inject: ['examParentThis'],
  components: {
    libraryExaminationPapers
  },
  filters: {
    showTitleFilter(data) {
      switch (data.type) {
        case 1:
          return data.campName
        case 2:
          return data.title
        case 3:
          return data.title
        case 5:
          return data.name
      }
    },
    showTypeTagFilter(data) {
      switch (data.type) {
        case 1:
          return '训练营'
        case 2:
          return '视频'
        case 3:
          return '专栏'
        case 5:
          return '培训班'
      }
    }
  },
  data() {
    // 正整数校验
    const numberValidate = (rule, value, callback) => {
      const numReg = /^([0]|[1-9][0-9]*)$/
      if (!numReg.test(value)) {
        return callback(new Error('请输入正确的格式'))
      }
      callback()
    }
    // 正整数校验以及判断禁止考试时长不可输入大于考试时长
    const closedTimeValidate = (rule, value, callback) => {
      const numReg = /^([0]|[1-9][0-9]*)$/
      if (!numReg.test(value)) {
        callback(new Error('请输入正确的格式'))
      } else {
        if (Number(this.addExamForm.examinationDuration) < Number(value)) {
          callback(new Error('请输入在考试时长内的分钟数'))
        } else {
          callback()
        }
      }
    }
    const durationValidate = (rule, value, callback) => {
      if (this.addExamForm.limitExaminationTime === 1) {
        if (this.addExamForm.examTime[0] && this.addExamForm.examTime[1]) {
          const examTimeDiff = dayjs(this.addExamForm.examTime[1]).diff(dayjs(this.addExamForm.examTime[0]), 'minutes')
          if (Number(value) > examTimeDiff) {
            callback(new Error('参与考试时长不能大于考试时间范围'))
            return
          }
        }
      }
      callback()
    }
    const examTimeValidate = (rule, value, callback) => {
      if (this.addExamForm.limitExaminationTime === 1) {
        if (this.addExamForm.examTime[0] && this.addExamForm.examTime[1]) {
          if (this.addExamForm.examTime[1] === this.addExamForm.examTime[0]) {
            callback(new Error('考试结束时间必须大于开始时间'))
            return
          }
        }
      }
      callback()
    }
    return {
      papersDialogVisible: false,
      associatedCoursesType: '3',
      associatedCoursesDialog: false,
      finish: false,
      stepArr: ['1 设置考试信息', '2 选择试卷', '3 发布'],
      active: 1,
      addExamForm: {
        paperName: '',
        examTime: [],
        examPeriodCover: '',
        examName: '',
        pictureUid: '',
        examContent: '',
        showExaminationNotes: 0,
        examinationNotes: '',
        examCount: '',
        paperUid: '',
        synchronizationClass: 0,
        examType: 0,
        // informationAcquisition: 0,
        authentication: 0,
        facialVerification: 0,
        limitExaminationTime: 0,
        examinationDuration: 0,
        closedTime: 0,
        examNum: -1,
        reExamSpan: 0,
        // watermark: '',
        // reExamCondition: 0,
        // scoreDisplay: 1,
        // problemDisplay: 1,
        // problemScoreDisplay: 1,
        limitPassExam: 0,
        passExamScore: '',
        scheduledRelease: 1,
        releaseTime: '',
        trainModuleContentList: [],
        certificateUid: ''
      },
      trainingCourseSelect: [],
      columnSelect: [],
      videoSelect: [],
      trainingSelect: [],
      trainingCourseModel: {
        total: 0,
        columnPage: 1,
        columnPageSize: 10,
        type: 5,
        trainingCourseList: []
      },
      columnModel: {
        total: 0,
        columnPage: 1,
        columnPageSize: 10,
        searchForm: {
          keyword: ''
        },
        columnList: []
      },
      videoModel: {
        total: 0,
        columnPage: 1,
        columnPageSize: 10,
        searchForm: {
          keyword: ''
        },
        videoList: []
      },
      trainingModel: {
        total: 0,
        columnPage: 1,
        columnPageSize: 10,
        searchForm: {
          keyword: ''
        },
        trainingList: []
      },
      currentSelectPapers: null,
      addExamPeriodRule: {
        examName: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
        examPeriodCover: [{ required: true, message: '请上传考试封面', trigger: 'blur' }],
        examContent: [{ required: true, message: '请输入考试详情', trigger: 'blur' }],
        examinationNotes: [{ required: true, message: '请输入考试须知', trigger: 'blur' }],
        examinationDuration: [
          { required: true, message: '请输入考试时长', trigger: 'blur' },
          { validator: numberValidate, message: '请输入正确格式' },
          { validator: durationValidate, message: '参与考试时长不能大于考试时间' }
        ],
        closedTime: [
          { required: true, message: '请输入禁止参与考试时长', trigger: 'blur' },
          { validator: closedTimeValidate, message: '请输入在考试时长内的分钟数' }
        ],
        reExamSpan: [
          { required: true, message: '请输入重考间隔', trigger: 'blur' },
          { validator: numberValidate, message: '请输入正确格式' }
        ],
        examTime: [
          { required: true, message: '请选择考试时间', trigger: 'blur' },
          { validator: examTimeValidate, message: '考试结束时间必须大于开始时间', trigger: 'change' }
        ],
        examCount: [
          { required: true, message: '请输入参与考试次数', trigger: 'blur' },
          { validator: numberValidate, message: '请输入正确格式' }
        ],
        paperUid: [{ required: true, message: '请选择一份试卷', trigger: ['blur', 'change'] }],
        limitPassExam: [
          { required: true, message: '请输入考试通过的分数', trigger: 'blur' },
          { validator: numberValidate, message: '请输入正确格式' }
        ],
        releaseTime: [{ required: true, message: '请选择定时发布的时间', trigger: 'blur' }]
      },
      certificateRegionList: []
    }
  },
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar
    },
    selectedCourseArr: ({ columnSelect, videoSelect, trainingSelect, trainingCourseSelect }) => {
      return [...columnSelect, ...videoSelect, ...trainingSelect, ...trainingCourseSelect]
    },
    // 用户最新选择除外的数组
    columnUnSelect({ columnSelect }) {
      const ids = columnSelect.map(item => {
        return item.uid
      })
      return this.columnModel.columnList.filter(item => {
        return !ids.includes(item.uid)
      })
    },
    // 用户最新选择除外的数组
    videoUnSelect({ videoSelect }) {
      const ids = videoSelect.map(item => {
        return item.uid
      })
      return this.videoModel.videoList.filter(item => {
        return !ids.includes(item.uid)
      })
    },
    // 用户最新选择除外的数组
    trainingUnSelect({ trainingSelect }) {
      const ids = trainingSelect.map(item => {
        return item.uid
      })
      return this.trainingModel.trainingList.filter(item => {
        return !ids.includes(item.uid)
      })
    },
    // 用户最新选择除外的数组
    trainingCourseUnSelect({ trainingCourseSelect }) {
      const ids = trainingCourseSelect.map(item => {
        return item.uid
      })
      return this.trainingCourseModel.trainingCourseList.filter(item => {
        return !ids.includes(item.uid)
      })
    }
  },
  watch: {
    selectedCourseArr(newVal, oldVal) {
      this.addExamForm.trainModuleContentList = newVal.map(item => {
        return {
          contentUid: item.contentUid || item.uid,
          contentType: item.type,
          name: this.setTitleModuleName(item)
        }
      })
    },
    'addExamForm.synchronizationClass'(newVal, oldVal) {
      if (newVal === 1) {
        this.addExamForm.limitExaminationTime = 1
        this.addExamForm.examType = 1
      }
    },
    'addExamForm.examType'(newVal, oldVal) {
      if (newVal === 1) {
        this.addExamForm.limitExaminationTime = 1
      }
    }
  },
  async mounted() {
    // 加载专栏
    await this.initColumnData()
    // await this.initVideoData();
    // await this.initTrainingData();
    // await this.inittrainingCourseData();
    this.editorInfo()
    this.certificateRegion()
  },
  methods: {
    certificateRegion() {
      getDropDownList().then((res) => {
        if (res.code === 200) {
          this.certificateRegionList = res.data
        }
      })
    },
    // 滑到底部进行加载
    columnLoad() {
      debounce(async() => {
        if (this.columnModel.columnList.length >= this.columnModel.total) return
        this.columnModel.columnPage++
        const params = {
          currentPage: this.columnModel.columnPage,
          pageSize: this.columnModel.columnPageSize,
          keyword: this.columnModel.searchForm.keyword,
          status: 1
        }
        const result = await getColumnPageList(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.columnModel.total = result.data.total
          this.columnModel.columnList.push(...result.data.records)
        }
      })()
    },
    videoLoad() {
      debounce(async() => {
        if (this.videoModel.videoList.length >= this.videoModel.total) return
        this.videoModel.columnPage++
        const params = {
          currentPage: this.videoModel.columnPage,
          pageSize: this.videoModel.columnPageSize,
          keyword: this.videoModel.searchForm.keyword,
          status: 0
        }
        const result = await videoList(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.videoModel.total = result.data.total
          this.videoModel.videoList.push(...result.data.records)
        }
      })()
    },
    trainingLoad() {
      debounce(async() => {
        if (this.trainingModel.trainingList.length >= this.trainingModel.total) return
        this.trainingModel.columnPage++
        const params = {
          currentPage: this.trainingModel.columnPage,
          pageSize: this.trainingModel.columnPageSize,
          keyword: this.trainingModel.searchForm.keyword
        }
        const result = await getCampList(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.trainingModel.total = result.data.total
          this.trainingModel.trainingList.push(...result.data.records)
        }
      })()
    },
    trainingCourseLoad() {
      debounce(async() => {
        if (this.trainingCourseModel.trainingCourseList.length >= this.trainingCourseModel.total) return
        this.trainingCourseModel.columnPage++
        const params = {
          currentPage: this.trainingCourseModel.columnPage,
          pageSize: this.trainingCourseModel.columnPageSize,
          type: this.trainingCourseModel.type,
          status: 1
        }
        const result = await getListData(params)
        if (result.code == this.$ECode.SUCCESS) {
          this.trainingCourseModel.total = result.data.total
          this.trainingCourseModel.trainingCourseList.push(...result.data.records)
        }
      })()
    },
    // 删除关联课程
    closeItem(data) {
      switch (data.type) {
        case 1:
          // 训练营
          this.trainingSelect.forEach((item, index) => {
            if (data.uid === item.uid) {
              this.trainingSelect.splice(index, 1)
              index--
            }
          })
          break
        case 2:
          // 视频
          this.videoSelect.forEach((item, index) => {
            if (data.uid === item.uid) {
              this.videoSelect.splice(index, 1)
              index--
            }
          })
          break
        case 3:
          // 专栏
          this.columnSelect.forEach((item, index) => {
            if (data.uid === item.uid) {
              this.columnSelect.splice(index, 1)
              index--
            }
          })
          break
        case 5:
          // 培训班
          this.trainingCourseSelect.forEach((item, index) => {
            if (data.uid === item.uid) {
              this.trainingCourseSelect.splice(index, 1)
              index--
            }
          })
          break
      }
    },
    // 设置标题模块名称
    setTitleModuleName(data) {
      switch (data.type) {
        case 1:
          return data.campName
        case 2:
          return data.title
        case 3:
          return data.title
        case 5:
          return data.name
      }
    },
    // 编辑信息填充
    editorInfo() {
      // 编辑考试
      if (this.examParentThis.editorFlag == 2) {
        this.addExamForm.paperName = this.examParentThis.editExamInfo.paper.name
        this.addExamForm.examTime = [this.examParentThis.editExamInfo.examStartTime ? this.examParentThis.editExamInfo.examStartTime : '', this.examParentThis.editExamInfo.examEndTime ? this.examParentThis.editExamInfo.examEndTime : '']
        this.addExamForm.pictureUid = this.examParentThis.editExamInfo.pictureUid
        this.addExamForm.examName = this.examParentThis.editExamInfo.examName
        this.addExamForm.examContent = this.examParentThis.editExamInfo.examContent
        this.addExamForm.paperUid = this.examParentThis.editExamInfo.paperUid
        this.addExamForm.synchronizationClass = this.examParentThis.editExamInfo.synchronizationClass
        this.addExamForm.examType = this.examParentThis.editExamInfo.examType
        this.addExamForm.showExaminationNotes = this.examParentThis.editExamInfo.showExaminationNotes
        this.addExamForm.examinationNotes = this.examParentThis.editExamInfo.examinationNotes
        // this.addExamForm.informationAcquisition = this.examParentThis.editExamInfo.informationAcquisition
        this.addExamForm.authentication = this.examParentThis.editExamInfo.authentication
        this.addExamForm.facialVerification = this.examParentThis.editExamInfo.facialVerification
        this.addExamForm.limitExaminationTime = this.examParentThis.editExamInfo.limitExaminationTime
        this.addExamForm.examinationDuration = this.examParentThis.editExamInfo.examinationDuration
        this.addExamForm.closedTime = this.examParentThis.editExamInfo.closedTime
        this.addExamForm.examCount = this.examParentThis.editExamInfo.examNum
        this.addExamForm.reExamSpan = this.examParentThis.editExamInfo.reExamSpan
        // this.addExamForm.watermark = this.examParentThis.editExamInfo.watermark
        // this.addExamForm.reExamCondition = this.examParentThis.editExamInfo.reExamCondition
        // this.addExamForm.scoreDisplay = this.examParentThis.editExamInfo.scoreDisplay
        // this.addExamForm.problemDisplay = this.examParentThis.editExamInfo.problemDisplay
        // this.addExamForm.problemScoreDisplay = this.examParentThis.editExamInfo.problemScoreDisplay
        this.addExamForm.limitPassExam = this.examParentThis.editExamInfo.limitPassExam
        this.addExamForm.passExamScore = this.examParentThis.editExamInfo.passExamScore
        this.addExamForm.scheduledRelease = this.examParentThis.editExamInfo.scheduledRelease
        this.addExamForm.releaseTime = this.examParentThis.editExamInfo.releaseTime
        this.addExamForm.examPeriodCover = this.examParentThis.editExamInfo.picturePath
        this.addExamForm.certificateUid = this.examParentThis.editExamInfo.certificateUid
        if (this.examParentThis.editExamInfo.examNum == -1) {
          // 不限制考试次数选项
          this.addExamForm.examNum = -1
        } else {
          this.addExamForm.examNum = 2
        }
        const trainModuleContentList = this.examParentThis.editExamInfo.trainModuleContentList
        this.addExamForm.trainModuleContentList = trainModuleContentList.map(item => {
          return {
            contentUid: item.contentUid,
            contentType: item.contentType,
            name: item.name
          }
        })
        // 编辑时-关联课程模块回显
        trainModuleContentList.forEach(item => {
          // 训练营
          if (item.contentType == 1) {
            this.trainingSelect.push(
              {
                ...item,
                type: item.contentType,
                campName: item.name
              }
            )
          } else if (item.contentType == 2) {
            // 视频
            this.videoSelect.push(
              {
                ...item,
                type: item.contentType,
                title: item.name
              }
            )
          } else if (item.contentType == 3) {
            // 专栏
            this.columnSelect.push(
              {
                ...item,
                type: item.contentType,
                title: item.name
              }
            )
          } else if (item.contentType == 5) {
            // 培训班
            this.trainingCourseSelect.push(
              {
                ...item,
                type: item.contentType,
                title: item.name
              }
            )
          }
        })
      }
    },
    async realAddPaper() {
      if (!this.currentSelectPapers) this.$message.warning('请选择试卷!')
      this.addExamForm.paperUid = this.currentSelectPapers.uid
      this.addExamForm.paperName = this.currentSelectPapers.name
      this.papersDialogVisible = false
      await this.$nextTick()
      // 重置表单校验
      this.$refs.addExamForm.validateField('paperUid')
    },
    // 选择试卷库
    selectPapers() {
      this.papersDialogVisible = true
    },
    // 标识表格的每一行
    getRowKeys(row) {
      return row.uid
    },
    trainingCourseSelectionChange(data) {
      this.trainingCourseSelect = data.map(item => {
        return {
          ...item,
          type: 5
        }
      })
    },
    columnSelectionChange(data) {
      this.columnSelect = data.map(item => {
        return {
          ...item,
          type: 3
        }
      })
    },
    videoSelectionChange(data) {
      this.videoSelect = data.map(item => {
        return {
          ...item,
          type: 2
        }
      })
    },
    trainingSelectionChange(data) {
      this.trainingSelect = data.map(item => {
        return {
          ...item,
          type: 1
        }
      })
    },
    async initColumnData() {
      const params = {
        currentPage: this.columnModel.columnPage,
        pageSize: this.columnModel.columnPageSize,
        keyword: this.columnModel.searchForm.keyword,
        status: 1
      }
      const result = await getColumnPageList(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.columnModel.total = result.data.total
        this.columnModel.columnList = result.data.records
      }
    },
    async initVideoData() {
      const params = {
        currentPage: this.videoModel.columnPage,
        pageSize: this.videoModel.columnPageSize,
        title: this.videoModel.searchForm.keyword,
        status: 0
      }
      const result = await videoList(params)
      if (result.code == this.$ECode.SUCCESS) {
        this.videoModel.total = result.data.total
        this.videoModel.videoList = result.data.records
      }
    },
    async initTrainingData() {
      const params = {
        currentPage: this.trainingModel.columnPage,
        pageSize: this.trainingModel.columnPageSize,
        campName: this.trainingModel.keyword
      }
      const result = await getCampList(params)
      if (result.code == 200) {
        this.trainingModel.total = result.data.total
        this.trainingModel.trainingList = result.data.records
      }
    },
    async inittrainingCourseData() {
      const params = {
        currentPage: this.trainingCourseModel.columnPage,
        pageSize: this.trainingCourseModel.columnPageSize,
        type: this.trainingCourseModel.type,
        status: 1
      }
      const result = await getListData(params)
      if (result.code == 200) {
        this.trainingCourseModel.total = result.data.total
        this.trainingCourseModel.trainingCourseList = result.data.records
      }
    },
    showSelected() {
      // 编辑
      if (this.examParentThis.editorFlag == 2) {
        // 表格勾选回显
        this.selectedCourseArr.forEach(item => {
          this.$nextTick(() => {
            // 训练营
            if (item.type == 1) {
              // 勾选
              this.trainingModel.trainingList.forEach(trainingItem => {
                if (trainingItem.uid === item.contentUid) {
                  this.$refs.trainingForm.toggleRowSelection(trainingItem, true)
                }
              })
            }
            // 视频
            if (item.type == 2) {
              // 勾选
              this.videoModel.videoList.forEach(videoItem => {
                if (videoItem.uid === item.contentUid) {
                  this.$refs.videoForm.toggleRowSelection(videoItem, true)
                }
              })
            }
            // 专栏
            if (item.type == 3) {
              // 勾选
              this.columnModel.columnList.forEach(columnItem => {
                if (columnItem.uid === item.contentUid) {
                  this.$refs.columnForm.toggleRowSelection(columnItem, true)
                } else {

                }
              })
            }
            // 培训班
            if (item.type == 5) {
              // 勾选
              this.trainingCourseModel.trainingCourseList.forEach(columnItem => {
                if (columnItem.uid === item.contentUid) {
                  this.$refs.trainingCourseForm.toggleRowSelection(columnItem, true)
                } else {

                }
              })
            }
          })
        })
      }
    },
    // 点击关联课程
    associatedCourses() {
      this.associatedCoursesDialog = true
      this.$nextTick(() => {
        this.trainingUnSelect.forEach(item => {
          this.$refs.trainingForm.toggleRowSelection(item, false)
        })
        this.videoUnSelect.forEach(item => {
          this.$refs.videoForm.toggleRowSelection(item, false)
        })
        this.columnUnSelect.forEach(item => {
          this.$refs.columnForm.toggleRowSelection(item, false)
        })
        this.trainingCourseUnSelect.forEach(item => {
          this.$refs.trainingCourseForm.toggleRowSelection(item, false)
        })
        // table回显
        this.showSelected()
      })
    },
    async associatedCoursesTab(tab, event) {
      switch (tab.name) {
        case '1':
          // 训练营
          this.trainingModel.columnPage = 1
          await this.initTrainingData()
          break
        case '2':
          // 视频
          this.videoModel.columnPage = 1
          await this.initVideoData()
          break
        case '3':
          // 专栏
          this.columnModel.columnPage = 1
          await this.initColumnData()
          break
        case '5':
          // 培训班
          this.trainingCourseModel.columnPage = 1
          await this.inittrainingCourseData()
          break
      }
      // table勾选回显
      this.showSelected()
    },

    // 数据重置
    dataReset() {
      this.examParentThis.editExamInfo = {}
      this.addExamForm = {
        examTime: [],
        examPeriodCover: '',
        examName: '',
        pictureUid: '',
        examContent: '',
        showExaminationNotes: 0,
        examinationNotes: '',
        paperUid: '',
        synchronizationClass: 0,
        examType: 0,
        // informationAcquisition: 0,
        authentication: 0,
        facialVerification: 0,
        limitExaminationTime: 0,
        examinationDuration: 0,
        closedTime: 0,
        examNum: -1,
        reExamSpan: '',
        // watermark: '',
        // reExamCondition: 0,
        // scoreDisplay: 1,
        // problemDisplay: 1,
        // problemScoreDisplay: 1,
        limitPassExam: 0,
        passExamScore: '',
        scheduledRelease: 1,
        releaseTime: '',
        trainModuleContentList: [],
        certificateUid: ''
      }
    },
    // 返回列表
    goBackList() {
      this.examParentThis.flag = 2
      this.dataReset()
    },
    // 取消
    cancel() {
      this.examParentThis.flag = 2
      this.dataReset()
    },
    // 上一步
    pre() {
      this.active--
    },
    // 下一步
    next() {
      this.$refs['addExamForm'].validate((valid) => {
        if (valid) {
          this.active++
          this.examParentThis.scrollTop()
        } else {
          return false
        }
      })
    },
    // 完成
    async complete() {
      this.$refs['addExamForm'].validate(async(valid) => {
        if (valid) {
          const params = {
            examName: this.addExamForm.examName,
            pictureUid: this.addExamForm.pictureUid,
            examContent: this.addExamForm.examContent,
            paperUid: this.addExamForm.paperUid ? this.addExamForm.paperUid : this.examParentThis.editExamInfo.uid ? this.examParentThis.editExamInfo.uid : '',
            synchronizationClass: this.addExamForm.synchronizationClass,
            examType: this.addExamForm.examType,
            showExaminationNotes: this.addExamForm.showExaminationNotes,
            examinationNotes: this.addExamForm.examinationNotes,
            // informationAcquisition: this.addExamForm.informationAcquisition,
            authentication: this.addExamForm.authentication,
            facialVerification: this.addExamForm.facialVerification,
            limitExaminationTime: this.addExamForm.limitExaminationTime,
            examStartTime: this.addExamForm.limitExaminationTime == 1 ? this.addExamForm.examTime[0] : '',
            examEndTime: this.addExamForm.limitExaminationTime == 1 ? this.addExamForm.examTime[1] : '',
            examinationDuration: this.addExamForm.examinationDuration,
            closedTime: this.addExamForm.closedTime,
            examNum: this.addExamForm.examNum == -1 ? -1 : this.addExamForm.examCount,
            reExamSpan: this.addExamForm.reExamSpan,
            // watermark: this.addExamForm.watermark,
            // reExamCondition: this.addExamForm.reExamCondition,
            // scoreDisplay: this.addExamForm.scoreDisplay,
            // problemDisplay: this.addExamForm.problemDisplay,
            // problemScoreDisplay: this.addExamForm.problemScoreDisplay,
            limitPassExam: this.addExamForm.limitPassExam,
            passExamScore: this.addExamForm.passExamScore,
            scheduledRelease: this.addExamForm.scheduledRelease,
            releaseTime: this.addExamForm.releaseTime,
            trainModuleContentList: this.addExamForm.trainModuleContentList,
            certificateUid: this.addExamForm.certificateUid
          }
          // 添加标识
          if (this.examParentThis.editorFlag == 1) {
            params.trainModuleContentList = this.addExamForm.synchronizationClass === 0 ? this.addExamForm.trainModuleContentList : []
            const result = await addExam(params)
            if (result.code == 200) {
              this.finish = true
            }
          } else {
            // 编辑
            params.uid = this.examParentThis.editExamInfo.uid
            const result = await editExam(params)
            if (result.code == 200) {
              this.finish = true
            }
          }
        } else {
          return false
        }
      })
    },
    // 封面图
    changeCover(file, fileList) {
      console.warn(file)
      const whiteList = ['image/jpeg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.warning('图片大小不能超过2M')
        return
      }
      if (!whiteList.includes(file.raw.type)) {
        this.$message.warning('请上传指定文件格式')
        return
      }
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('token', getToken())
      formData.append('source', 'picture')
      formData.append('projectName', 'blog')
      formData.append('sortName', 'admin')
      formData.append('file', file.raw)
      uploadFile(formData).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.addExamForm.pictureUid = res.data[0].uid
          this.addExamForm.examPeriodCover = res.data[0].url
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.addCamp-period-container {
    padding: 20px;
    .associatedCoursesDialog{
        /deep/ .el-dialog__body {
            padding: 0 25px;
        }
    }
    .finish {
        margin-top: 120px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;

        i {
            color: #00ce77;
            font-size: 120px;
        }

        .title {
            font-weight: bold;
            font-size: 24px;
            margin-bottom: 50px;
        }

        .desc {
            font-size: 16px;
            color: #cac9c9;
        }
    }

    .submit-btn {
        transition: all .5s;
        z-index: 99;
        width: calc(100% - 180px);
        right: 0;
        position: fixed;
        bottom: 0;
        background: white;
        box-shadow: 0 0 10px rgb(209, 209, 209);
        align-items: center;
        display: flex;
        justify-content: center;
        height: 60px;
    }

    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        border: 1px dashed #d9d9d9;
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
    }

    .avatar {
        width: 178px;
        height: 178px;
        display: block;
    }

    .form-box {
        .form {
            .selected-course{
                padding-left: 30px;
                .item{
                    position: relative;
                    width: 400px;
                    background: #f8f6f6;
                    margin-bottom: 10px;
                    padding: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .close{
                        display: none;
                        position: absolute;
                        width: 20px;
                        height: 20px;border-radius: 50%;
                        top: 0;
                        right: 0;
                        cursor: pointer;
                        color: red;
                    }
                    &:hover{
                        .close{
                            display: block;
                        }
                    }
                }
            }
            .examCount-box{
                /deep/ .el-form-item__content{
                    margin-left: 63px !important;
                    display: flex;
                    .el-input{
                        margin: 0 10px 0 0;
                        width: 300px;
                    }
                }
            }
            .el-form-item{
                .retest-interval{
                    display: flex;
                    span{
                        margin-left: 10px;
                    }
                    .el-input {
                        width: 300px;
                    }
                }
                .exam-time-long{
                    display: flex;
                    span{
                        margin-left: 10px;
                    }
                    .el-input{
                        width: 300px;
                    }
                }
                /deep/ .el-form-item__content{
                    .el-input__inner{
                        width: 300px;
                    }
                    .el-textarea__inner{
                        width: 600px;
                    }
                }
            }
            .exam-time {
                /deep/ .el-form-item__content{
                    .el-input__inner {
                        width: 380px;
                    }
                }
            }
            .exam-select{
                display: flex;
                justify-content: space-between;
                .exam-question-bank {
                    margin-top: 20px;
                }
                .right{
                    min-height: 640px;
                    background: #eff5fc;
                    flex-direction: column;
                    align-items: center;
                    padding: 20px;
                    display: flex;
                    width: 400px;
                }
            }

            .base-info,
            .open-class-info,
            .mode-info,
            .shop-info,
            .shelf-setting,
            .course-catalogue {
                padding-left: 10px;
                margin: 20px 0;
                border-left: 3px solid #2f97ec;
            }

            .release-set {
                .timing-release{
                    margin-top: 20px;
                }
                /deep/ .el-form-item__content {
                    .tip {
                        span:first-of-type {
                            color: rgb(51, 51, 51);
                        }

                        span:last-of-type {
                            margin-left: 20px;
                            color: #cac9c9;
                        }
                    }

                    .release-type {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        min-width: 500px;
                        min-height: 60px;
                        padding: 20px 20px;
                        background: #f5f5f5;
                    }
                }
            }

            .date-unlock {
                /deep/ .el-form-item__content {
                    .row-select {
                        display: flex;
                        align-items: center;

                        .date-unlock-everyday {
                            margin: 0 10px;
                            width: 100px;
                        }

                        .date-unlock-every-unit {
                            width: 70px;
                            margin-right: 10px;
                        }

                        .date-unloc-type {
                            margin-left: 10px;
                            width: 70px;
                        }
                    }

                    .row-time {
                        margin-top: 10px;
                    }
                }
            }
        }
    }
}
</style>
