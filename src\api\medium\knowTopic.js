import request from '@/utils/request'

// 知识话题一级分类列表
export function getParentBlogSortList (params) {
  return request({
    url: process.env.ADMIN_API + '/blogSort/getParentBlogSortList',
    method: 'get',
    params: params
  })
}

// 知识话题二级分类列表
export function getList (params) {
  return request({
    url: process.env.ADMIN_API + '/blogSort/getList',
    method: 'post',
    data: params
  })
}

// 知识话题新增
export function addBolbSort (params) {
  return request({
    url: process.env.ADMIN_API + '/blogSort/add',
    method: 'post',
    data: params
  })
}

// 知识话题更新
export function editBolbSort (params) {
  return request({
    url: process.env.ADMIN_API + '/blogSort/edit',
    method: 'post',
    data: params
  })
}

// 知识话题删除
export function delBolbSort (params) {
  return request({
    url: process.env.ADMIN_API + '/blogSort/deleteBatch',
    method: 'post',
    data: params
  })
}

// 知识话题排序更新
export function topicSort (params) {
  return request({
    url: process.env.ADMIN_API + '/blogSort/sort',
    method: 'post',
    data: params
  })
}