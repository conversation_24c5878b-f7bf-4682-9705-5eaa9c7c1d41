
const pageParams = {
  state: {
    currentPage: 1,
    pageSize: 10
  },

  mutations: {
    setCurrentPage(state, currentPage) {
      state.currentPage = currentPage;
    },
    setPageSize(state, pageSize) {
      state.pageSize = pageSize;
    }
  },
  actions: {
    updateCurrentPage({ commit }, currentPage) {
      commit('setCurrentPage', currentPage);
    },
    updatePageSize({ commit }, pageSize) {
      commit('setPageSize', pageSize);
    }
  }
};

export default pageParams;
