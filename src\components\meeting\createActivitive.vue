<template>
    <div class="createActivitive">
        <el-form :rules="createActivitiveRule" ref="createActivitiveForm" label-position="right" class="form" :model="createActivitiveForm" label-width="80px">
            <el-form-item prop="title" label-width="120px" label="活动标题">
                <el-input placeholder="输入活动标题" v-model="createActivitiveForm.title"></el-input>
            </el-form-item>
            <el-form-item label-width="120px" prop="applyEndTime" label="报名截止时间">
                <el-date-picker
                value-format="yyyy-MM-dd"
                v-model="createActivitiveForm.applyEndTime"
                type="date"
                placeholder="选择日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label-width="120px" prop="acticeTime" class="active-time" label="活动时间">
                <el-date-picker
                value-format="yyyy-MM-dd"
                v-model="createActivitiveForm.acticeTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item prop="reportTime" label-width="120px" class="active-time" label="报道时间">
                <el-date-picker
                placeholder="请输入报道时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="createActivitiveForm.reportTime"
                type="datetime"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label-width="120px" label="活动地址" prop="city">
                <el-select v-model="createActivitiveForm.province" placeholder="请选择省份">
                    <el-option
                    v-for="item in areaArr"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                    </el-option>
                </el-select>
                <el-select v-model="createActivitiveForm.city" placeholder="请选择城市">
                    <el-option
                    v-for="item in cityArr"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="cover" label-width="120px" label="活动封面">
                <div class="uploadPng">
                    <el-upload
                    :auto-upload="false"
                    :on-change="changeCover"
                    :show-file-list="false"
                    class="avatar-uploader"
                    action="">
                    <img v-if="createActivitiveForm.cover" :src="createActivitiveForm.cover" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <span class="desc">支持格式JPG、PNG、JPEG、GIF格式， 图片小于2M。</span>
                </div>
            </el-form-item>
            <el-form-item prop="fileUid" label-width="120px" label="活动详情">
                <div class="uploadFile">
                    <el-upload
                    :before-remove="beforeRemove"
                    :on-exceed="fileHandleExceed"
                    :on-change="changeFile"
                    :auto-upload="false"
                    :on-error="uploadFileError"
                    class="upload-demo"
                    action=""
                    multiple
                    :limit="1"
                    :file-list="createActivitiveForm.activeDetail">
                    <el-button size="small" type="primary">点击上传</el-button>
                    </el-upload>
                    <span class="desc">仅支持扩展名为pdf的文件，大小不超过10MB</span>
                </div>
            </el-form-item>
            <el-form-item label-width="120px" v-for="(item,index) in createActivitiveForm.hotels" :key="index" :label="index>0?'':'酒店名称'" class="hotel-box">
                <div class="row-box">
                    <el-input placeholder="输入酒店名称" v-model="item.name" class="name"></el-input>
                    <el-input class="priceName" placeholder="输入酒店价格" v-model="priceItem.price" v-for="(priceItem,priceIndex) in item.price" :key="priceIndex+index">
                        <el-tooltip slot="suffix" class="item" effect="dark" content="删除价格" placement="top-start">
                            <i
                                class="el-icon-close el-input__icon cursor"
                                @click="deletePrice(item.price,priceIndex)">
                            </i>
                        </el-tooltip>
                    </el-input>
                    <el-button type="primary" class="add-price" @click="addPrice(item)" plain>添加价格</el-button>
                    <!-- 删除酒店按钮 -->
                    <el-tooltip class="item" effect="dark" content="删除酒店" placement="top-start">
                        <i class="delete-hotel el-icon-error" @click="deleteHotel(index)"></i>
                    </el-tooltip>
                </div>
            </el-form-item>
            <el-form-item label-width="120px" label=" ">
                <el-button type="primary" @click="addHotel" plain>添加酒店</el-button>
            </el-form-item>
            <el-form-item label-width="120px" label=" ">
                <el-button type="primary" @click="createActive">{{actionFlag==1?'创建活动':'确认编辑'}}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import {uploadFile,activityUploadFile} from '@/api/upload'
import {addActivity,editorActivity} from '@/api/meeting/activity'
import { getToken } from "@/utils/auth";
import utils from "@/utils/commonUtil"
export default {
    props:{
        actionFlag:{
            type:Number|String,
            default:1
        },
        areaArr:{
            type:Array,
            default:[]
        },
        detail:{
            type:Object,
            default:null
        }
    },
    data(){
        return{
            createActivitiveRule:{
                applyEndTime:[
                    { required: true, message: '请选择报名截止时间', trigger: 'change' },
                    // {validator:(rule, value, callback)=>{
                    //     // 报名截止时间
                    //     let reportTimeTimestamp=new Date(value).getTime();
                    //     let currentDateTimestamp=new Date(utils.FUNCTIONS.formatDate(new Date(),"yyyy-MM-dd")).getTime();
                    //     if(reportTimeTimestamp< currentDateTimestamp){
                    //         callback(new Error('报名截止时间不能小于当前时间'));
                    //     }else{
                    //         callback();
                    //     }
                    // },trigger:'change'}
                ],
                acticeTime:[
                    { required: true, message: '请选择活动时间', trigger: 'change' },
                    {validator:(rule, value, callback)=>{
                        // 报名截止时间
                        let applyEndTimeTimestamp=new Date(this.createActivitiveForm.applyEndTime).getTime();
                        // 
                        if(new Date(value[0]).getTime()>applyEndTimeTimestamp){
                            callback();
                        }else{
                            callback(new Error('活动开始时间不能小于报名截止时间'));
                        }
                        console.log(applyEndTimeTimestamp)
                    },trigger:'change'},
                ],
                title:[
                    { required: true, message: '请输入活动标题', trigger: 'change' },
                ],
                reportTime:[
                    { required: true, message: '请选择报道时间', trigger: 'change' }
                ],
                city:[
                    { required: true, message: '请选择地区', trigger: 'change' },
                ],
                cover:[
                    { required: true, message: '请上传活动封面', trigger: 'change' },
                ],
                fileUid:[
                    { required: true, message: '请上传活动详情', trigger: 'change' },
                ],
            },
            createActivitiveForm:{
                reportTime:"",
                fileUid:"",
                pictureUid:"",
                title:"",
                applyEndTime:"",
                acticeTime:[],
                city:"",
                province:"",
                cover:"",
                activeDetail:[],
                hotels:[]
            }
        }
    },
    mounted(){
        // 编辑
        if(this.actionFlag==2){
            this.createActivitiveForm={
                title:this.detail.activityName,
                applyEndTime:this.detail.registrationEndTime,
                reportTime:this.detail.reportStartTime,
                acticeTime:this.detail.activityStartTime?[this.detail.activityStartTime,this.detail.activityEndTime]:[],
                city:this.detail.addressUid,
                province:this.detail.provinceId,
                cover:this.detail.picturePath,
                activeDetail:[{url:this.detail.filePath,name:this.detail.fileName}],
                hotels:this.detail.hotel,
                fileUid:this.detail.fileUid,
                pictureUid:this.detail.pictureUid
            }
        }
    },
    computed:{
        // 市
        cityArr({createActivitiveForm,areaArr}){
            let currentItem=areaArr.find(item=>{
                return item.id==createActivitiveForm.province;
            });
            return currentItem?currentItem.treeChild:[];
        }
    },
    beforeDestroy(){
        this.formDataReSet();
    },
    methods:{
        beforeRemove(){
            this.createActivitiveForm.fileUid="";
            return true;
        },
        deleteHotel(index){
            // let hotelLen=this.createActivitiveForm.hotels.length;
            // if(hotelLen==1){
            //     this.$message.warning("至少要保留一个酒店");
            //     return;
            // }
            this.createActivitiveForm.hotels.splice(index,1);
        },
        // 删除价格
        deletePrice(data,index){
            if(data.length==1){
                this.$message.warning("至少要保留一个价格");
                return;
            }
            data.splice(index,1);
            console.log(data);
        },
        async createActive() {
            this.$refs['createActivitiveForm'].validate(async (valid) => {
                if (valid) {
                    let params = {
                        activityName: this.createActivitiveForm.title,
                        pictureUid: this.createActivitiveForm.pictureUid,
                        fileUid: this.createActivitiveForm.fileUid,
                        registrationEndTime: this.createActivitiveForm.applyEndTime,
                        reportStartTime:this.createActivitiveForm.reportTime,
                        activityStartTime: this.createActivitiveForm.acticeTime[0],
                        activityEndTime: this.createActivitiveForm.acticeTime[1],
                        addressUid: this.createActivitiveForm.city,
                        hotel: this.createActivitiveForm.hotels,
                    };
                    // 创建
                    if (this.actionFlag == 1) {
                        let result = await addActivity(params);
                        if (result.code == this.$ECode.SUCCESS) {
                            this.$message.success("添加成功");
                            this.$parent.mode = 1;
                            // 刷新数据
                            this.$parent.initActivityList();
                        }
                    } else {
                        // 编辑
                        params.uid = this.detail.uid;
                        let result = await editorActivity(params);
                        if (result.code == this.$ECode.SUCCESS) {
                            this.$message.success("编辑成功");
                            this.$parent.mode = 1;
                            // 刷新数据
                            this.$parent.initActivityList();
                        }
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // form表单数据重置
        formDataReSet(){
            this.createActivitiveForm={
                fileUid:"",
                pictureUid:"",
                title:"",
                applyEndTime:"",
                acticeTime:[],
                city:"",
                province:"",
                cover:"",
                activeDetail:[],
                hotels:[]
            };
        },
        // 封面图
        changeCover(file,fileList) {
            console.warn(file)
            let whiteList=['image/jpeg','image/png','image/gif'];
            const isLt2M = file.size / 1024 / 1024 < 2;
            if(!isLt2M){
                this.$message.warning("图片大小不能超过2M");
                return;
            }
            if(!whiteList.includes(file.raw.type)){
                this.$message.warning("请上传指定文件格式");
                return;
            }
            let formData = new FormData();
            formData.append("file", file.raw);
            formData.append("token", getToken());
            formData.append("source", "picture");
            formData.append("userUid", "uid00000000000000000000000000000000");
            formData.append("adminUid", "uid00000000000000000000000000000000");
            formData.append("projectName", "blog");
            formData.append("sortName", "admin");
            formData.append("file",file.raw);
            uploadFile(formData).then(res=>{
                if (res.code == this.$ECode.SUCCESS){
                    this.createActivitiveForm.pictureUid=res.data[0].uid;
                    this.createActivitiveForm.cover=res.data[0].url;
                }
            })
        },
        // 上传文件错误触发
        uploadFileError(){
            this.createActivitiveForm.activeDetail=[];
        },
        fileHandleExceed(){
            this.$message.warning("最多只能上传一份活动详情文件");
        },
        // 活动文件
        changeFile(file,fileList) {
            console.log(file);
            const isPdf = file.raw.type === 'application/pdf';
            const isLt10M = file.size / 1024 / 1024 < 10;
            if(!isLt10M){
                this.$message.warning("文件大小不能超过10M");
                this.createActivitiveForm.activeDetail=[];
                return;
            }
            if(!isPdf){
                this.$message.warning("仅支持pdf格式文件上传");
                this.createActivitiveForm.activeDetail=[];
                return;
            }
            let formData=new FormData();
            formData.append("file",file.raw);
            formData.append("projectName","activity");
            formData.append("sortName","activity");
            activityUploadFile(formData).then(res=>{
                if (res.code == this.$ECode.SUCCESS){
                    this.createActivitiveForm.fileUid=res.data[0].uid;
                }
            })
        },
        // 添加价格
        addPrice(item){
            item.price.push({price:''});
        },
        // 添加酒店
        addHotel(){
            this.createActivitiveForm.hotels.push(
                {name:"",price:[{price:''}]}
            )
        }
    }
}
</script>
<style lang="scss">
.createActivitive{
    .form{
        .active-time{
            .el-form-item__content{
                .el-date-editor{
                    .el-range-separator{
                        width: 7%;
                    }
                }
            }
        }
        .hotel-box{
            .el-form-item__content{
                .row-box{
                    flex-wrap: wrap;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    .delete-hotel{
                        font-size: 25px;
                        margin: -10px 0 0 16px;
                        cursor: pointer;
                        color: #E6A23C;
                    }
                    .name{
                        margin-bottom: 10px;
                        width: 250px;
                        .el-input__inner{
                            width: 250px;
                        }
                    }
                    .priceName{
                        margin-bottom: 10px;
                        margin-left: 10px;
                        width: 165px;
                        .el-input__inner{
                            width: 165px;
                        }
                    }
                    .add-price{
                        margin-left: 10px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
        .uploadPng{
            display: flex;
            .desc{
                margin-left: 20px;
                margin-bottom: 5px;
                align-self: flex-end;
            }
        }
        .uploadFile{
            display: flex;
            align-items: center;
            .desc{
                margin-left: 20px;
            }
        }
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 178px;
            height: 178px;
            line-height: 178px;
            text-align: center;
        }
        .avatar {
            width: 178px;
            height: 178px;
            display: block;
        }
    }
}
</style>