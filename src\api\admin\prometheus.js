import request from '@/utils/request'

/**
 * 普罗米修斯配置保存
 * @param {*} data
 */
export function savePrometheusConfig(data) {
  return request({
    url: '/admin/prometheus/saveConf',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 获取普罗米修斯配置
 */
export function getPrometheusConfig() {
  return request({
    url: '/admin/prometheus/getConf',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
