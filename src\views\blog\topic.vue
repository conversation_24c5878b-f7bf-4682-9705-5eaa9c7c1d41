<!-- create by lbin -->
<template>
  <div class="content">
    <div class="header">
      <el-form :inline="true" :model="searchForm" ref="searchForm">
        <el-form-item>
          <!-- <el-select
            @change="getDataList"
            clearable
            size="mini"
            v-model="searchForm.tagUid"
            placeholder="标签"
          >
            <el-option
              v-for="item in tagList"
              :key="item.uid"
              :label="item.content"
              :value="item.uid"
            >
            </el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item>
          <el-select
            @change="search"
            clearable
            size="mini"
            v-model="searchForm.topicSortUid"
            placeholder="话题分类"
          >
            <el-option
              v-for="item in blogSortList"
              :key="item.uid"
              :label="item.sortName"
              :value="item.uid"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            @change="search"
            clearable
            size="mini"
            v-model="searchForm.author"
            placeholder="作者"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            @change="search"
            clearable
            size="mini"
            v-model="searchForm.content"
            placeholder="话题内容"
          ></el-input>
        </el-form-item>
        <el-form-item label=" ">
          <el-button
            type="primary"
            size="mini"
            @click="getDataList"
            icon="el-icon-search"
          >
            搜索
          </el-button>
          <el-button
            type="danger"
            :disabled="ids.length == 0"
            icon="el-icon-delete"
            size="mini"
            @click="deleteMsg(null)"
          >
            删除
          </el-button>
        </el-form-item>
      </el-form>
      <div class="left">
        <el-button
          type="warning"
          @click="exportExcel"
          icon="el-icon-download"
          size="mini"
        >
          导出
        </el-button>
      </div>
    </div>
    <div class="center">
      <el-table
        id="out-table"
        :data="dataList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection"> </el-table-column>
        <el-table-column align="center" label="话题内容">
          <template slot-scope="scope">
            <el-popover placement="right" trigger="hover">
              <div v-html="scope.row.content"></div>
              <div class="table-description" slot="reference">
                {{ scope.row.content }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="标签">
          <template slot-scope="scope">
            {{ getItemTag(scope.row.tagUid) }}
          </template>
        </el-table-column> -->
        <el-table-column align="center" label="分类">
          <template slot-scope="scope">
            {{ getItemTopSort(scope.row.topicSortUid) }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="clickCount"
          label="点击数"
          width="90px"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="contentCount"
          label="内容数"
          width="90px"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="joinCount"
          label="参与人数"
          width="90px"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="author"
          label="作者"
        ></el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间">
        </el-table-column>
        <el-table-column align="center" label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0" style="color:#FF3F3F">
              已下架
            </span>
            <span v-else-if="scope.row.status == 1" style="color:#15B564">
              已发布
            </span>
            <span v-else style="color:#FF3F3F"> 异常 </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="90px">
          <template slot-scope="scope">
            <div style="display:flex;justify-content:space-between;">
              <el-button
                type="text"
                size="mini"
                @click="openEditMsg(scope.row)"
              >
                {{ scope.row.status == 0 ? "上架" : "下架" }}
              </el-button>
              <!-- <el-button type="text" size="mini" @click="deleteMsg(scope.row)">
                删除
              </el-button> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页栏 -->
      <el-pagination
        style="float: right; margin-top:10px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import {
  getList,
  add,
  edit,
  deleteData,
  deleteBatch,
  downloadExcel
} from "@/api/blog/topic";
import { tagList } from "@/api/tag";
import { blogSortList } from "@/api/blogSort";
export default {
  data() {
    return {
      searchForm: {
        tagUid: null,
        topicSortUid: null,
        author: null,
        content:'',
      },
      dataList: [],
      page: 1,
      limit: 10,
      total: 100,
      ids: [],
      tagList: [],
      blogSortList: []
    };
  },

  created() {
    this.getDataList();
    this.getTagList();
    this.getBlogSortList();
  },
  methods: {
    initParams(){
      this.page = 1;
      this.limit = 10;
      this.dataList = [];
    },
    getTagList() {
      tagList().then(res => {
        if (res.code == 200) {
          this.tagList = res.data;
        } else {
          this.$commonUtil.message.error(res.message);
        }
      });
    },
    getBlogSortList() {
      blogSortList().then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.blogSortList = res.data;
        } else {
          this.$commonUtil.message.error(res.message);
        }
      });
    },
    getDataList() {
      var param = {
        currentPage: this.page,
        pageSize: this.limit,
        tagUid: this.searchForm.tagUid,
        topicSortUid: this.searchForm.topicSortUid,
        author: this.searchForm.author,
        content:this.searchForm.content
      };
      getList(param).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.total = res.data.total;
          this.dataList = res.data.records;
        } else {
          this.$commonUtil.message.error(res.message);
        }
      });
    },

    openEditMsg(row) {
      let param = {
        uid: row.uid,
        status: row.status == 1 ? 0 : 1
      };
      edit(param).then(res => {
        if (res.code == this.$ECode.SUCCESS) {
          this.getDataList();
          this.$commonUtil.message.success(
            row.status == 1 ? "下架成功" : "上架成功"
          );
        } else {
          this.$commonUtil.message.error(res.message);
        }
      });
    },

    // 表格前勾选框
    handleSelectionChange(val) {
      if (val.length === 1) this.editForm = Object.assign({}, val[0]);
      this.ids = val;
    },

    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.limit = newSize;
      this.getDataList();
    },

    search(){
      this.initParams();
      this.getDataList();
    },
    search(){
      this.initParams();
      this.getDataList();
    },
    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.page = newSize;
      this.getDataList();
    },

    //导出Excel表格
    exportExcel() {
      var params = {
        tagUid: this.searchForm.tagUid,
        topicSortUid: this.searchForm.topicSortUid,
        author: this.searchForm.author,
        uids:
          this.ids != null && this.ids.length != 0
            ? this.ids.map(item => item.uid).toString()
            : ""
      };
        let year = new Date().getFullYear();
        let month = (new Date().getMonth() + 1) < 10 ? ('0' + (new Date().getMonth() + 1)) : new Date().getMonth() + 1;
        let day = (new Date().getDate()) < 10 ? ('0' + new Date().getDate()) : new Date().getDate();
        let hour = new Date().getHours();
        let minute = new Date().getMinutes();
        let second = new Date().getSeconds();
      downloadExcel(params).then(res => {
        this.$commonUtil.downloadExcel(res,`话题${year}${month}${day}${hour}${minute}${second}`);
      });
    },

    // 删除
    async deleteMsg(ids) {
      var configResult = await this.$confirm(
        "此操作将永久删除该数据, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).catch(err => {
        return err;
      });
      if (configResult !== "confirm") {
        return this.$message.info({ message: "已经取消删除", duration: 1000 });
      }
      if (ids == null) {
        deleteBatch(this.ids).then(res => {
          if (res.code == this.$ECode.SUCCESS) {
            this.$commonUtil.message.success(res.data);
            this.getDataList();
          } else {
            this.$commonUtil.message.error(res.data);
          }
        });
      } else {
        deleteData(ids).then(res => {
          if (res.code == this.$ECode.SUCCESS) {
            this.$commonUtil.message.success(res.data);
            this.getDataList();
          } else {
            this.$commonUtil.message.error(res.data);
          }
        });
      }
    },
    getItemTag(uid) {
      if (!this.tagList || !uid) return;
      let param = this.tagList.find(item => item.uid == uid);
      return param ? param.content : "";
    },
    getItemTopSort(uid) {
      if (!this.blogSortList || !uid) return;
      let param = this.blogSortList.find(item => item.uid == uid);
      return param ? param.sortName : "";
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  padding: 20px;
  .header {
    display: flex;
    justify-content: space-between;
  }
}
/deep/ .el-dialog__header {
  display: none;
}
.dialog-main {
  position: relative;
  color: #000;
  .invoice-main-header {
    font-size: larger;
    margin-bottom: 20px;
    font-weight: bold;
  }
  .invoice-item {
    font-size: medium;
    margin-bottom: 15px;
  }
}

.dialog-close {
  font-size: x-large;
  margin-left: 10px;
  position: absolute;
  top: 0;
  right: 0;
}

.dialog-close:hover {
  cursor: pointer;
}

.open-invoice {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/deep/.el-button-width {
  padding: 12px;
  width: 140px;
  height: 42px;
}

.addImg {
  .avatar-uploader-icon {
    border: 1px solid balck;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
}

.upload_img {
  width: 100px;
  height: 100px;
  position: relative;

  .close-icon {
    // display: none;
    right: 2px;
    top: 2px;
    position: absolute;
    font-size: 24px;
    display: inline-block;
    z-index: 8;
    cursor: pointer;
  }
  // img:hover + .close-icon {
  //   font-size: 24px;
  //   display: inline-block;
  //   z-index: 8;
  // }
}
.uploadImgBody {
  width: 100px;
  height: 100px;
  border: dashed 1px #c0c0c0;
  float: left;
  position: relative;
}
.uploadImgBody :hover {
  border: dashed 1px #00ccff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.icon-main {
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-description {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
