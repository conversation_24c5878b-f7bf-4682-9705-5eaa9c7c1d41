<template>
  <div class="content flex-col h-100 p-20">
    <div class="flex ai-center jc-between">
      <div class="flex ai-center jc-between felx-1">
        <span style="width: 80px">时间筛选</span>
        <el-date-picker v-model="searchForm.time" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" class="mr-10" size="small" value-format="yyyy-MM-dd" />
        <el-input placeholder="请输入标签" v-model.trim="searchForm.labelName" style="width: 300px;" class="mr-10"
          size="small"></el-input>
        <el-button type="primary" icon="el-icon-search" size="small" @click="getData(1)">搜 索</el-button>
      </div>
      <el-button type="primary" icon="el-icon-plus" size="small" @click="openDialog(false, false, 0)">新建标签</el-button>
    </div>

    <el-table ref="multipleTable" :data="tableData" style="width: 100%" height="auto" class="mt-20"
      @expand-change="rowClick">
      <el-table-column type="expand" v-if="!isSelect">
        <template slot-scope="scopeParant">
          <el-table :data="scopeParant.row.child" style="width: 100%" :show-header="false" @expand-change="rowClick">
            <!-- <el-table-column prop="" label="" width="48px" /> -->
            <el-table-column type="expand">
              <template slot-scope="childScope">
                <el-table :data="childScope.row.child" style="width: 100%" :show-header="false">
                  <el-table-column width="48px" />
                  <el-table-column width="100px">
                    <template slot-scope="scope">
                      {{ getIndex(scopeParant, childScope, scope) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="labelName" label="证书标签" />
                  <el-table-column prop="orderNum" label="排序" />
                  <el-table-column prop="createTime" label="创建时间" />
                  <el-table-column label="操作">
                    <template slot-scope="scope">
                      <el-button type="text"
                        v-if="scope.row.type == 2 && scope.row.childrenTotal && scope.row.childrenTotal > 0"
                        @click="getTrainCertificateList(scope.row)">查看证书</el-button>
                      <el-button type="text" @click="openDialog(scope.row, false)">编辑</el-button>
                      <el-button type="text" style="color: #F56C6C;" @click="delData(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column width="100px">
              <template slot-scope="scope">
                {{ getIndex(scopeParant, scope) }}
              </template>
            </el-table-column>
            <el-table-column prop="labelName" label="证书标签" />
            <el-table-column prop="orderNum" label="排序" />
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" v-if="scope.row.type == 1" @click="openDialog(false, false, 2, scope.row.uid)"
                  style="color:#67C23A">添加发证机关</el-button>
                <el-button type="text" @click="openDialog(scope.row, false)">编辑</el-button>
                <el-button type="text" style="color: #F56C6C;" @click="delData(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column :index="indexMethod" type="index" label="序号" width="100px" />
      <el-table-column prop="labelName" label="证书标签" />
      <el-table-column prop="orderNum" label="排序" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.type == 0" @click="openDialog(false, false, 1, scope.row.uid)"
            style="color:#67C23A">添加许可单位</el-button>
          <el-button type="text" v-if="scope.row.type == 1" @click="openDialog(false, false, 2, scope.row.uid)"
            style="color:#67C23A">添加发证机关</el-button>
          <el-button type="text" v-if="scope.row.type == 2 && scope.row.childrenTotal && scope.row.childrenTotal > 0"
            @click="getTrainCertificateList(scope.row)">查看证书</el-button>
          <el-button type="text" @click="openDialog(scope.row, false)">编辑</el-button>
          <el-button type="text" style="color: #F56C6C;" @click="delData(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="flex-right mt-10">
      <el-pagination :current-page.sync="searchForm.currentPage" :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" background />
    </div>

    <el-dialog :title="dialog.name" :visible.sync="dialog.show" width="450px">
      <div>
        <div class="mb-20 flex ai-center">
          <span style="width: 80px">
            <span style="color:red">*</span>
            {{ { 0: '证书标签', 1: '许可单位', 2: '发证机关' }[dialog.form.type] }}
          </span>
          <el-input :placeholder="`请输入${{ 0: '证书标签', 1: '许可单位', 2: '发证机关' }[dialog.form.type]}`"
            v-model.trim="dialog.form.labelName" style="width: 300px" maxlength="30" show-word-limit></el-input>
        </div>
        <div class="mb-20 flex ai-center">
          <span style="width: 80px">
            <span style="color:red">*</span>
            排序
          </span>
          <el-input placeholder="请输入排序" v-model.trim="dialog.form.orderNum" style="width: 300px" type="number"
            min="0"></el-input>
        </div>
        <div class="dialog-button">
          <el-button class="el-button-width" @click="dialog.show = false"> 取 消 </el-button>
          <el-button class="el-button-width" @click="submitForm" type="primary"> 确 定 </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="checkDialog.show">
      <el-input size="small" placeholder="请输入证书简称或全称" style="width:300px" v-model.trim="checkDialog.form.keyword"
        @blur="getTrainCertificateList(null, 1)"></el-input>

      <el-select @change="getTrainCertificateList(null, 1)" v-model="checkDialog.form.status" placeholder="状态"
        size="small">
        <el-option label="不限" :value="''"></el-option>
        <el-option label="已发布" value="1"></el-option>
        <el-option label="已下架" value="0"></el-option>
      </el-select>

      <el-table :data="checkDialog.data" class="mt-20" style="width: 100%" border>
        <el-table-column prop="shortName" label="证书简称" show-overflow-tooltip />
        <el-table-column prop="fullName" label="证书全称" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 1" type="success">已发布</el-tag>
            <el-tag v-if="scope.row.status == 0" type="info">已下架</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex-right mt-20">
        <el-pagination :current-page.sync="checkDialog.form.currentPage" :page-sizes="[10, 20, 50, 100]"
          :page-size="checkDialog.form.pageSize" :total="checkDialog.total" layout=" prev, pager, next"
          @size-change="handleSizeChange2" @current-change="handleCurrentChange2" background />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTrainCertificate } from '@/api/authentication.js'
import { getLabelList, addLabel, editLabel, delLabel } from '@/api/authentication.js'
export default {
  data () {
    return {
      searchForm: {
        time: [],
        labelName: '',
        currentPage: 1,
        pageSize: 10
      },
      isSelect: false,
      tableData: [],
      total: 0,
      dialog: {
        name: '',
        show: false,
        rules: [],
        parentUid: '',
        form: {
          labelName: '',
          orderNum: '',
          parentUid: '',
          type: 0
        },
        isEdit: false,
      },
      checkDialog: {
        show: false,
        form: {
          keyword: '',
          currentPage: 1,
          pageSize: 10,
          labelUid: '',
          status: ''
        },
        total: 0,
        data: []
      }
    }
  },
  computed: {
  },
  mounted () {
    this.getData()
  },
  methods: {
    getData (val) {
      if (val) this.searchForm.currentPage = val
      const params = JSON.parse(JSON.stringify(this.searchForm))
      if (params.time && params.time.length > 0) {
        params.startTime = params.time[0] + ' 00:00:00'
        params.endTime = params.time[1] + ' 23:59:59'
      }
      delete params.time

      getLabelList(params).then(res => {
        if (res.code == 200) {
          this.total = res.data.total
          this.tableData = res.data.records
          this.tableData.forEach(item => {
            item.level = 1
          })

          if (params.labelName || params.endTime) {
            this.isSelect = true
          } else {
            this.isSelect = false
          }

        }
      })
    },

    rowClick (row) {
      if (row.child) return
      getLabelList({ uid: row.uid }).then(res => {
        if (res.code == 200) {
          row.child = res.data
          row.child.forEach(item => {
            item.level = row.level + 1
          })
          this.$nextTick(() => {
            this.$refs.multipleTable.toggleRowExpansion(row, true)
          })
        }
      })
    },

    openDialog (row, flag, type = '', uid = '') {
      this.dialog.form = row ? JSON.parse(JSON.stringify(row)) : { labelName: '', orderNum: 0, parentUid: '', type: 0 }
      this.dialog.isEdit = row ? true : false

      if (type != '') {
        this.dialog.form.parentUid = uid
        this.dialog.form.type = type
      }

      const name = { 0: '证书标签', 1: '许可单位', 2: '发证机关' }[this.dialog.form.type]
      this.dialog.name = this.dialog.isEdit ? '编辑' + name : '新建' + name

      this.dialog.show = true
    },

    submitForm () {
      if (this.dialog.form.orderNum < 0) return this.$message.warning('排序不能为负数')

      if (!this.dialog.form.labelName) return this.$message.warning('请补充标签信息')

      const requestFun = this.dialog.isEdit ? editLabel : addLabel
      requestFun(this.dialog.form).then(res => {
        if (res.code == 200) {
          this.dialog.show = false
          this.$message.success(this.dialog.isEdit ? '编辑成功' : '新建成功')
          this.getData()
        }
      })
    },

    async delData (row) {
      const configResult = await this.$confirm("此操作将永久删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning"
      }).catch(err => { return err; });

      if (configResult !== "confirm") {
        return this.$message.info({ message: "已经取消删除", duration: 1000 });
      }

      delLabel({ uid: row.uid }).then(res => {
        if (res.code == 200) {
          if (this.tableData.length == 1 && this.searchForm.currentPage != 1) {
            this.params.page--
          }
          this.getData()
          this.$message.success('删除成功')
        }
      })
    },

    // 计算索引
    indexMethod (index) {
      return (index + 1) + this.searchForm.pageSize * (this.searchForm.currentPage - 1)
    },
    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange (newSize) {
      this.searchForm.pageSize = newSize
      this.getData(1)
    },
    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange (page) {
      this.searchForm.currentPage = page
      this.getData()
    },

    // 获取证书列表
    getTrainCertificateList (row, val) {
      this.checkDialog.data = []
      if (row) this.checkDialog.form = { keyword: '', currentPage: 1, pageSize: 10, labelUid: row.uid, status: '' }

      if (val) this.checkDialog.form.currentPage = val
      getTrainCertificate(this.checkDialog.form).then((res) => {
        if (res.code == this.$ECode.SUCCESS) {
          this.checkDialog.data = res.data.records
          this.checkDialog.total = res.data.total
        }
      }).finally(() => { this.checkDialog.show = true })
    },

    handleSizeChange2 (newSize) {
      this.checkDialog.form.pageSize = newSize
      this.getTrainCertificateList(null, 1)
    },
    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange2 (page) {
      this.checkDialog.form.currentPage = page
      this.getTrainCertificateList()
    },

    // 获取序号
    getIndex (one, two, three) {
      let index
      if (one) index = (one.$index + 1)
      if (two) index = index + '.' + (two.$index + 1)
      if (three) index = index + '.' + (three.$index + 1)
      return index || 0
    }
  }
}
</script>

<style lang="scss" scoped></style>