import request from '@/utils/request'

export function addCompetition(params) {
    return request({
      url: process.env.ADMIN_API + '/competition/add',
      method: 'post',
      data: params
    })
}


export function getCompetition(params) {
    return request({
      url: process.env.ADMIN_API + '/competition/getList',
      method: 'post',
      data: params
    })
}

export function delCompetition(params) {
    return request({
      url: process.env.ADMIN_API + '/competition/deleteBatch',
      method: 'post',
      data: params
    })
}


export function editCompetition(params) {
  return request({
    url: process.env.ADMIN_API + '/competition/edit',
    method: 'post',
    data: params
  })
}

export function getBjMatchList(params) {
  return request({
    url: process.env.ADMIN_API + '/competition/getBjMatchList',
    method: 'get',
    params: params
  })
}
