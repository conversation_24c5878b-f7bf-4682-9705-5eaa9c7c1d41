/* Base16 Atelier Heath Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/heath) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON><PERSON>/base16) */
/* https://github.com/jmblog/color-themes-for-highlightjs */

/* Atelier Heath Dark Comment */
.hljs-comment,
.hljs-title {
  color: #9e8f9e;
}

/* Atelier Heath Dark Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #ca402b;
}

/* Atelier Heath Dark Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-pragma,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #a65926;
}

/* Atelier Heath Dark Yellow */
.hljs-ruby .hljs-class .hljs-title,
.css .hljs-rules .hljs-attribute {
  color: #bb8a35;
}

/* Atelier Heath Dark Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #379a37;
}

/* Atelier Heath Dark Aqua */
.css .hljs-hexcolor {
  color: #159393;
}

/* Atelier Heath Dark Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #516aec;
}

/* Atelier Heath Dark Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #7b59c0;
}

.hljs {
  display: block;
  background: #292329;
  color: #ab9bab;
  padding: 0.5em;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
