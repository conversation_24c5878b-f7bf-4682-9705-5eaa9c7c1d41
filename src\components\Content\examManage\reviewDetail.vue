<template>
    <div class="review-detail">
        <div class="head-person-info">
            <el-descriptions :labelStyle="{ display: 'flex', alignItems: 'center'}" title="" :column="4">
                <el-descriptions-item label="答题人">
                    <div class="cover">
                        <el-avatar size="large"
                            :src="reviewDetail.avatar">
                        </el-avatar>
                        <span>{{ reviewDetail.userName }}</span>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="交卷时间">{{ reviewDetail.commitTime }}</el-descriptions-item>
                <el-descriptions-item label="用时">{{ $commonUtil.formatSeconds((reviewDetail && reviewDetail.spendTime)||0) }}</el-descriptions-item>
                <el-descriptions-item label="成绩">{{ reviewDetail.finalScore }}分</el-descriptions-item>
            </el-descriptions>
        </div>
        <div class="answer-questions">
            <h4>客观题</h4>
            <h5>答题情况</h5>
            <div class="exam-info">
                <div class="item">
                    <div>
                        <span class="num">{{ reviewDetail.rightNum }}</span>
                        <span class="desc">道</span>
                    </div>
                    <span class="questions">答对</span>
                </div>
                <div class="item">
                    <div>
                        <span class="num">{{ reviewDetail.errorNum }}</span>
                        <span class="desc">道</span>
                    </div>
                    <span class="questions">答错</span>
                </div>
                <div class="item">
                    <div>
                        <span class="num">{{ reviewDetail.notNum }}</span>
                        <span class="desc">道</span>
                    </div>
                    <span class="questions">未答</span>
                </div>
                <div class="item">
                    <div>
                        <span class="num">{{ reviewDetail.finalScore }}</span>
                        <span class="desc">分</span>
                    </div>
                    <span class="questions">得分</span>
                </div>
            </div>
        </div>
        <!-- 作答 -->
        <div class="select-topic">
            <p>客观题</p>
            <div class="question-item" v-for="(item,index) in reviewDetail.paper.titleList" :key="item.uid">
                <p class="question">{{index+1}}.（{{ item.type|topicType }}，分值{{ item.score }}）{{item.title}}（）</p>
                <div class="option-item" v-for="(option,indexOp) in item.answerList" :key="option.uid">
                    <el-radio  disabled :class="filterAnswer(item.myAnswer).includes(option.uid) ?'selected':''"  :label="1">{{ indexOp | optionPrefix }}.{{ option.answer }}</el-radio>
                </div>
                <!-- 解答 -->
                <div class="analysis" :class="[shopAnswerStatus(item.answerList, item.myAnswer, item.type, item.hasAnswer)]">
                    <el-descriptions class="descriptions" title="" :column="1">
                        <!-- 选择题 -->
                        <template v-if="item.type == 0 || item.type == 1 || item.type==2">
                            <el-descriptions-item label="学员答案">{{ fliterOptionPrefixLabel(item.answerList, item.myAnswer) }}</el-descriptions-item>
                            <el-descriptions-item label="答案">{{ rightAnswersPrefixLabel(item.answerList)}}</el-descriptions-item>
                        </template>
                        <!-- 判断题 -->
                        <template v-if="item.type == 3">
                            <el-descriptions-item label="学员答案">{{ item.myAnswer ? item.myAnswer ==0?'错误':'正确':'未作答'}}</el-descriptions-item>
                            <el-descriptions-item label="答案">{{ item.hasAnswer==0?'错误':'正确'}}</el-descriptions-item>
                        </template>
                        <el-descriptions-item label="解析" :span="2">{{ item.analysis }}</el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
        </div>
        <div class="bottom-back" :style="[
        sidebar.opened ? { width: 'calc(100% - 180px)' }:{width: 'calc(100% - 35px)' } ]">
            <el-button type="primary" @click="gobackReviewList" icon="el-icon-arrow-left">返回批阅列表</el-button>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
    inject: ['examParentThis'],
    filters: {
        // 题目类型
        topicType(val) { 
            switch (val) { 
                case 0:
                    return '单选题';
                case 1:
                    return '多选题';
                case 2:
                    return '不定项选择题';
                case 3:
                    return '判断题';
                case 4:
                    return '填空题';
                case 5:
                    return '单问答题';
                case 6:
                    return '材料题';
            }
        },
        optionPrefix(val) { 
            let optionsLabelMaps = new Map();
            for (var i = 0; i < 26; i++) {
                optionsLabelMaps.set(i, String.fromCharCode(65 + i));
            }
            return optionsLabelMaps.get(val);
        }
    },
    computed: {
        sidebar() {
            return this.$store.state.app.sidebar;
        },
        ...mapGetters(['reviewDetail'])
    },
    data() { 
        return {
            option: 1
        }
    },
    methods: {
        // 根据状态码
        shopAnswerStatus(optionArr, userAnswerUid, type, hasAnswer) { 
            // 用户选项(字母)
            let userSelected = this.fliterOptionPrefixLabel(optionArr, userAnswerUid);
            // 正确答案(字母)
            let correctSelected = this.rightAnswersPrefixLabel(optionArr);
            // 选择题
            if ([0, 1, 2].includes(type)) {
                if (userSelected === correctSelected) {
                    // 回答正确
                    return 'correct';
                } else {
                    if (userSelected === '未作答') {
                        return 'unAnswer';
                    } else {
                        // 回答错误
                        return 'error';
                    }
                }
            } else if (type == 3) { 
                // 判断题
                if (!userAnswerUid) return 'unAnswer';
                return userAnswerUid == hasAnswer ? 'correct' : 'error';
            }
        },
        // 根据索引显示选项字母前缀
        optionPrefixDispose(val) {
            switch (val) {
                case 0:
                    return 'A';
                case 1:
                    return 'B';
                case 2:
                    return 'C';
                case 3:
                    return 'D';
                case 4:
                    return 'E';
                case 5:
                    return 'F';
                case 6:
                    return 'G';
                default:
                    return '选项异常';
            }
        },
        // 正确答案的字母前缀
        rightAnswersPrefixLabel(optionArr) { 
            let answerLabels = '';
            if (optionArr && optionArr.length > 0) { 
                for (let i = 0; i < optionArr.length; i++) {
                    if (optionArr[i].hasAnswer == 1) {
                        answerLabels += this.optionPrefixDispose(i);
                    }
                }
            }
            return answerLabels;
        },
        // 筛选用户已经选择的答案选项前缀
        fliterOptionPrefixLabel(optionArr, userAnswerUid) {
            if (!userAnswerUid) return '未作答';
            let userAswer = userAnswerUid.split(',');
            let answerLabels = '';
            for (let i = 0; i < optionArr.length; i++) { 
                if (userAswer.includes(optionArr[i].uid)) { 
                    answerLabels += this.optionPrefixDispose(i);
                }
            }
            return answerLabels ? answerLabels:'未作答';
        },
        // 多选题分割题目id字符串
        filterAnswer(data) { 
            if (!data) return [];
            return data.split(',');
        },
        // 返回批阅列表
        gobackReviewList() { 
            this.examParentThis.flag = 3;
        }
    }
}
</script>
<style lang="scss" scoped>
.selected {
    /deep/ .el-radio__input {
        .el-radio__inner {
            background: #409eff !important;
        }
    }
}
.review-detail{
    padding: 0 20px 80px 20px;
    .bottom-back{
        transition: all .5s;
        z-index: 99;
        width: calc(100% - 180px);
        right: 0;
        position: fixed;
        bottom: 0;
        background: white;
        box-shadow: 0 0 10px rgb(209, 209, 209);
        align-items: center;
        display: flex;
        justify-content: start;
        padding-left: 120px;
        height: 60px;
    }
    .select-topic{
        padding: 20px;
        font-size: 18px;
        .question-item{
            .question{
                font-size: 14px;
                .option-item{
                
                }
            }
            .analysis{
                position: relative;
                padding: 5px;
                border-radius: 3px;
                margin-top: 5px;
                min-height: 100px;
                background: #f5f7fa;
                .descriptions{
                    /deep/ .el-descriptions__body {
                        background: none;
                    }
                }
            }
            .correct::after{
                content: " ";
                display: block;
                position: absolute;
                left: 20%;
                top: 50%;
                margin-top: -25px;
                z-index: 10;
                width: 80px;
                height: 50px;
                background: url("https://assets.cdn.xiaoeknow.com/exam/admin_evaluation_fe/1.0.95/img/answer_right.png") no-repeat no-repeat;
                background-size: cover;
            }
            .unAnswer::after{
                content: " ";
                display: block;
                position: absolute;
                left: 20%;
                top: 50%;
                margin-top: -25px;
                z-index: 10;
                width: 80px;
                height: 50px;
                background: url("https://assets.cdn.xiaoeknow.com/exam/admin_evaluation_fe/1.0.95/img/answer_nothing.png") no-repeat no-repeat;
                background-size: cover;
            }
            .error::after{
                content: " ";
                display: block;
                position: absolute;
                left: 20%;
                top: 50%;
                margin-top: -25px;
                z-index: 10;
                width: 80px;
                height: 50px;
                background: url("https://assets.cdn.xiaoeknow.com/exam/admin_evaluation_fe/1.0.95/img/answer_error.png") no-repeat no-repeat;
                background-size: cover;
            }
        }
    }
    .answer-questions{
        padding: 20px;
        border-radius: 2px;
        background: #f5f7fa;
        min-height: 200px;
        .exam-info{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 135px;
            .item{
                margin: 0 120px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .questions{
                    font-size: 14px;
                }
                .num{
                    font-size: 55px;
                }
                .desc{
                    font-size: 14px;
                }
                &:first-child{
                    .num{
                        color: #2fce63;
                    }
                    .desc{
                        color: #2fce63;
                    }
                }
                &:nth-child(2){
                    .num {
                            color: #fb6161;
                        }
                    
                        .desc {
                            color: #fb6161;
                        }
                }
                &:nth-child(3),&:nth-child(4){
                    .num {
                            color: #495060;
                        }
                    
                        .desc {
                            color: #495060;
                        }
                }
            }
        }
        h4,h5{
            padding: 0;
            margin: 0;
            display: inline-block;
        }
        h5{
            margin-left: 5px;
        }
    }
    .head-person-info{
        .cover{
            display: flex;
            align-items: center;
            span{
                margin-left: 5px;
            }
        }
        .el-descriptions{
            /deep/ .el-descriptions__body{
                .el-descriptions__table{
                    td{
                        line-height: 5.2;
                    }
                }
            }
        }
    }
}
</style>