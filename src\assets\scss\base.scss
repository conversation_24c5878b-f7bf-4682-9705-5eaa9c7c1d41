@import './config.scss';

html,body,#app{
	width: 100%;
	height: 100%;
	overflow: hidden;
}

/*  常规配色 */
$colorMain: #006eff; //主色调
$colorBtn: #006eff; //按钮
$colorFont: #5FB41B;
$colorCourse: #EA4335;
$colorWhite: #fff;
$colorBlack: #333;
$colorWarning: #F77900;
$color666: #666;
$color999: #999;
$colorBg: #f8f8f8;
$colorMenuHover: #006eff; //菜单hover

.overflow{
	overflow-x: hidden;
	overflow-y: auto;
	scrollbar-color: transparent transparent;
	scrollbar-track-color: transparent;
	-mz-scrollbar-track-color: transparent;
	scrollbar-color:transparent transparent;
	scrollbar-track-color: transparent;
	-mz-scrollbar-track-color:transparent;
	-ms-scrollbar-track-color:transparent;
	-ms-overflow-style: none;
	-ms-content-zooming: zoom;
	-ms-scroll-rails: none;
	-ms-content-zoom-limit-min: 100%;
	-ms-content-zoom-limit-max: 500%;
	-ms-scroll-snap-type: proximity;
	-ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
	-ms-overflow-style: none;
}
.ffox{
	scrollbar-color:  #D9D9D9 #000;  /* 第一个方块颜色，第二个轨道颜色(用于更改火狐浏览器样式) */
	scrollbar-width: thin;  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
	-ms-overflow-style:none;  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
}
@import '../../assets/scss/mixin.scss';
.contextM{
	height: auto;
	position: absolute;
	background-color: #FFFFFF;
	// border: 1px solid #b7c8f6;
	display: none;
	// @include paddingBoxSizing(10px 0);
	ul{
		width: 100%;
		height: 100%;
    margin: 0;
    padding: 0;
    box-shadow: 0px 0px 16px 0px rgba(49, 130, 223, 0.22);
    border-radius: 4px;
    position: relative;
    &:after{
        position: absolute;
        content: '';
        left: 59px;
        top: -9px;
        border-bottom: 9px solid #FFFFFF;
        //border-left和border-right换成透明色 才能形成三角形 不然是长方形
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        //background-color: red;
    }
		li{
      list-style: none;
			cursor: pointer;
			@include paddingBoxSizing(0 0 0 5px);
			width: 100%;
			font-size: 16px;
			height: 30px;
      line-height: 30px;
			text-align: center;
			// border-bottom: 1px dotted #cccccc;
			color: #7d7d7d;
			&:hover{
				background-color: rgba(62, 157, 255, 0.2) !important;
			}
		}
	}
}



/*  字体颜色 */
.color-main {
  color: $colorMain;
}

.color-warning {
  color: $colorWarning;
}

.color-error {
  color: #FF564D;
}

.color-white {
  color: $colorWhite;
}

.color-black {
  color: $colorBlack;
}

.color-666 {
  color: $color666;
}

.color-999 {
  color: $color999;
}

.w-100 {
  width: 100%;
}
.h-100 {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.flex-space-between {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.flex-center-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;

}

.flex-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

}

.flex-top {
  display: flex;
  align-items: flex-start;

}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

.jc-start {
  justify-content: flex-start;
}

.jc-end {
  justify-content: flex-end;
}

.jc-center {
  justify-content: center;
}

.jc-around {
  justify-content: space-around;
}

.jc-between {
  justify-content: space-between;
}

.ai-start {
  align-items: flex-start;
}

.ai-end {
  align-items: flex-end;
}

.ai-center {
  align-items: center;
}




/*  网格布局相关公共样式 */
.grid {
  display: grid;
}

$grid-gap: (
  5,
  8,
  10,
  15,
  16,
  20,
  25,
  30,
  35,
  40,
  45,
  50
);

@each $gg in $grid-gap {
  .gg-#{$gg} {
    grid-gap:#{$gg}px;
  }
}

$g-t-c: (
  1:1fr,
  2:1fr 1fr,
  3:1fr 1fr 1fr,
  4:1fr 1fr 1fr 1fr,
  5:1fr 1fr 1fr 1fr 1fr,
  6:1fr 1fr 1fr 1fr 1fr 1fr,
); // grid-template-columns

@each $key,
$gtc in $g-t-c {
  .gtc-#{$key} {
    grid-template-columns:#{$gtc} !important
  }
}

/* 文字溢出 */
.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

.ellipsis2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.ellipsis3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.ellipsis4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}

/*  hover手势 */
.cursor {
  cursor: pointer
}
/*  hover字体颜色 */
.hover-color {
  transition: all .5s;

  &:hover {
    color: $colorMain;
  }
}

/* 边距生成：padding/margin */
$spacing-types: (
  m: margin,
  p: padding
);
$spacing-directions: (
  t: top,
  b: bottom,
  l: left,
  r: right
);

$spacing-base-size : 10px; //基数
$spacing-sizes: (
  5: 0.5,
  8:0.8,
  12:1.2,
  10: 1,
  15: 1.5,
  16:1.6,
  20: 2,
  24:2.4,
  25: 2.5,
  30: 3,
  35: 3.5,
  32:3.2,
  40: 4,
  45: 4.5,
  50: 5,
  55: 5.5,
  60: 6,
  65: 6.5,
  70: 7,
  75: 7.5,
  80: 8,
  180:18
);

/* 

  循环出 margin 与 padding 的各类值
  取值参考$spacing-sizes中的key
  如：m-5，mx-10，ml-20，p-5，py-10, pr-20等

*/
@each $typeKey,
$type in $spacing-types {

  // m-10 {margin:10px} || p-50 {padding:50px}
  @each $sizeKey,
  $size in $spacing-sizes {
    .#{$typeKey}-#{$sizeKey} {
      #{$type}: $size * $spacing-base-size;
    }
  }

  // mx-10 {margin-left:10px; margin-right:10px} || px-50 {padding-left:50px; padding-right:50px;}
  @each $sizeKey,
  $size in $spacing-sizes {
    .#{$typeKey}x-#{$sizeKey} {
      #{$type}-left: $size * $spacing-base-size;
      #{$type}-right: $size * $spacing-base-size;
    }
  }

  // my-10{margin-top:10px; margin-bottom:10px} || py-50{padding-top:50px; padding-bottom:50px;}
  @each $sizeKey,
  $size in $spacing-sizes {
    .#{$typeKey}y-#{$sizeKey} {
      #{$type}-top: $size * $spacing-base-size;
      #{$type}-bottom: $size * $spacing-base-size;
    }
  }

  // mt-10 { margin-top: 10px } || pb-50{paddding-bottom:50px;}||ml-20{margin-left:20px}||pr-40{padding-right:40px}
  @each $directionsKey,
  $directions in $spacing-directions {

    @each $sizeKey,
    $size in $spacing-sizes {
      .#{$typeKey}#{$directionsKey}-#{$sizeKey} {
        #{$type}-#{$directions}: $size * $spacing-base-size;
      }
    }
  }
}


/* 字体大小 */
$fontsize: (
  12:$fontA,
  14:$fontB,
  16:$fontC,
  18:$fontD,
  20:$fontE,
  22:$fontF,
  24:$fontG,
  26:$fontH,
  28:$fontI,
  30:$fontJ,
  32:$fontK,
  36:$fontL,
  // 40:$fontM
);

@each $key,
$size in $fontsize {
  .font-#{$key} {
    font-size: #{$size};
  }
}

/* 12px缩小为10px */
.font-scale {
  display: inline-block;
  transform: scale(0.83)
}

/* 圆角 */
$radiusList: 3, 4, 6, 9, 12, 50;

@each $i in $radiusList {
  .radius#{$i} {
    border-radius: #{$i}px;
    overflow: hidden;
  }
}

.circle {
  border-radius: 50%;
  overflow: hidden;
}


$fontWeight: (
  100,
  200,
  300,
  400,
  500,
  600,
  700,
  800,
  900,
  bold,
  bolder,
  lighter
);

@each $var in $fontWeight {
  .f-w-#{$var} {
    font-weight: $var;
  }
}