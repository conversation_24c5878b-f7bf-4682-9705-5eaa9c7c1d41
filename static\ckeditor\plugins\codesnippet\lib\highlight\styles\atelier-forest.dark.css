/* Base16 Atelier Forest Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/forest) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON>on/base16) */
/* https://github.com/jmblog/color-themes-for-highlightjs */

/* Atelier Forest Dark Comment */
.hljs-comment,
.hljs-title {
  color: #9c9491;
}

/* Atelier Forest Dark Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #f22c40;
}

/* Atelier Forest Dark Orange */
.hljs-number,
.hljs-preprocessor,
.hlj<PERSON>-prag<PERSON>,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #df5320;
}

/* Atelier Forest Dark Yellow */
.hljs-ruby .hljs-class .hljs-title,
.css .hljs-rules .hljs-attribute {
  color: #d5911a;
}

/* Atelier Forest Dark Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #5ab738;
}

/* Atelier Forest Dark Aqua */
.css .hljs-hexcolor {
  color: #00ad9c;
}

/* Atelier Forest Dark Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #407ee7;
}

/* Atelier Forest Dark Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #6666ea;
}

.hljs {
  display: block;
  background: #2c2421;
  color: #a8a19f;
  padding: 0.5em;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
