// 插件管理器注册插件
tinymce.PluginManager.add('wordlimit', function (editor) {
    // 插件名字
    var pluginName = '字数限制';
    // 延时
    var app = tinymce.util.Tools.resolve('tinymce.util.Delay');
    // 工具
    var Tools = tinymce.util.Tools.resolve('tinymce.util.Tools');
    // 入参名字:ax_listener_event 默认值:SetContent Undo Redo Keyup input paste
    var listener_event = editor.getParam('listener_event', 'SetContent Undo Redo Keyup input paste');
    // 传入的参数
    var options = editor.getParam('wordlimit', {}, 'object');
    var close = null;
    // 弹窗
    var toast = function (message) {
        close && close.close();
        close = editor.notificationManager.open({
            text: message,
            type: 'error',
            timeout: 3000,
        });
        return
    }
    // 默认配置
    var defaults = {
        // max: 0, // 最多可以输入多少字
        spaces: false, // 是否含空格
        isInput: false, // 是否在超出后还可以输入
        maxMessage: "超出最大输入字符数量！",
        changeCallback: () => { }, // 自定义的回调方法
        changeMaxCallback: () => { },
        toast // 提示弹窗
    };

    class WordLimit {
        constructor(editor, options) {
            options = Tools.extend(defaults, options);
            let preCount = 0;
            let _wordCount = 0;
            let oldContent = editor.getContent();
            // 获取字数统计插件
            let WordCount = editor.plugins.wordcount;
            // 纯文字内容
            let sumLetter = function (html) {
                var re1 = new RegExp("<.+?>", "g");
                var txt = html.replace(re1, '');
                txt = txt.replace(/\n/g, '');
                txt = txt.replace(/&nbsp;/g, ' ');
                return {
                    txt: txt,
                    html
                }
            }
            // 监听编辑器事件
            editor.on(listener_event, function (e) {
                var content = editor.getContent() || e.content || '';
                if (options.spaces) {
                    // 包含空格字数
                    _wordCount = WordCount.body.getCharacterCount();
                } else {
                    // 不含空格字数
                    _wordCount = WordCount.body.getCharacterCountWithoutSpaces();
                }
                // 内容改变回调
                options.changeCallback({
                    ...options,
                    editor,
                    num: _wordCount,
                    content,
                    ...sumLetter(content)
                })
                if (_wordCount > options.max) {
                    preCount = _wordCount;
                    // 禁止再次输入
                    if (!options.isInput) {
                        // 内容超出还原
                        editor.setContent(oldContent);
                        // 还原后重新统计
                        if (options.spaces) {
                            _wordCount = WordCount.body.getCharacterCount();
                        } else {
                            _wordCount = WordCount.body.getCharacterCountWithoutSpaces();
                        }
                    }
                    // 聚焦
                    editor.getBody().blur();
                    // 触发回调
                    editor.fire('wordlimit', {
                        maxCount: options.max,
                        wordCount: _wordCount,
                        preCount: preCount,
                        isPaste: (e.type === 'paste' || e.paste) || false
                    });
                    // 弹窗
                    toast(options.maxMessage)
                }
                // 更新符合范围内的值
                oldContent = editor.getContent();
            })
        }
    }
    var setup = function () {
        // 如果没设置最大输入限制，则不进行下一步操作
        if (!options && !options.max) return false;
        // 如果没有配置字数统计，则不进行下一步，并且提示配置字数统计
        if (!editor.plugins.wordcount) return toast('请先在tinymce的plugins配置wordlimit之前加入wordcount插件');
        // 定时
        app.setEditorTimeout(editor, function () {
            let editDom = editor.getContainer();
            // 字数统计插件所展示的dom元素
            let wordNum = editDom.querySelector("button.tox-statusbar__wordcount");
            // 必须引入wordcount插件,然后显示status
            if (wordNum.innerText.indexOf("字符") == -1) wordNum.click();
            new WordLimit(editor, options)
        }, 300);
    }
    // 激活插件
    setup();
    return {
        getMetadata: function () {
            return {
                name: pluginName
            };
        }
    };
});