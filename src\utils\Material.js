class Material {
  constructor() {
    this.title = "插入图片";
    this.tag = "button";
  }

  getValue(editor) {
    return "";
  }

  isActive(editor) {
    return false;
  }

  isDisabled(editor) {
    return false;
  }

  exec(editor) {
    if (this.isDisabled(editor)) return;
    // 触发自定义按钮点击事件
    editor.emit("materialShow");
  }
}
export default Material;
// class Material {
//   constructor() {
//     this.title = "选择图片";
//     this.tag = "button";
//   }
//   getValue(editor) {
//     return " hello ";
//   }
//   isActive(editor) {
//     return false; // or true
//   }
//   isDisabled(editor) {
//     return false; // or true
//   }
//   exec(editor, value) {
//     if (this.editor.disable()) {
//       return;
//     }
//     // 选择素材
//     that.materialShow = true;
//   }
// }
