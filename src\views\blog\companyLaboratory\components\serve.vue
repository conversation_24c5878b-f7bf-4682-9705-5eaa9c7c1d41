<template>
  <div class="content-manage-child">
    <div class="top">
      <div class="left">
        <span>时间筛选：</span>
        <el-date-picker
          size="mini"
          v-model="searchParams.date"
          type="date"
          placeholder="选择日期"
        >
        </el-date-picker>
      </div>
      <div class="right">
        <el-button v-if="ids.length" type="danger" size="mini" :disabled="ids.length == 0">删除</el-button>
        <el-button size="mini">操作日志</el-button>
        <el-button size="mini" @click="addDialog = true" type="primary">
          添加案例
        </el-button>
      </div>
    </div>

    <el-table
      :data="dataList"
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="tapName" label="标签标题"> </el-table-column>
      <el-table-column prop="introduction" label="标签描述">
        <template slot-scope="scope">
          <div style="border:1px solid black">{{ scope.row.introduction }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="jumpLink" label="跳转链接">
        <template slot-scope="scope">
          <div>{{ scope.row.jumpLink ? scope.row.jumpLink : "" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="imgPath" label="标签配图">
        <template slot-scope="scope">
          <el-image
            style="width: 200px; "
            :src="scope.row.imgPath"
            fit="contain"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="添加时间"> </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        :width="this.$store.getters.operationButtonWidth + 'px'"
      >
        <template slot-scope="scope">
          <div ref="operation">
            <el-button type="primary" size="mini" @click="openEditCase(scope)">
              编辑
            </el-button>
            <!-- <el-button
              type="danger"
              size="mini"
              @click="deleteCase(scope.row.id)"
            >
              删除
            </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部分页栏 -->
    <el-pagination
      style="float: right; margin-top:10px"
      class="bottom"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="searchParams.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>

    <el-dialog title="添加案例" :visible.sync="addDialog" width="700px">
      <el-form
        ref="addForm"
        :rules="addFormRules"
        :model="addForm"
        label-width="80px"
      >
        <el-form-item label="标签标题" prop="tapName">
          <el-input
            v-model="addForm.tapName"
            class="company-name"
            maxlength="10"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="标签描述" prop="introduction">
          <el-input
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            v-model="addForm.introduction"
          ></el-input>
        </el-form-item>
        <el-form-item label="跳转链接" prop="jumpLink">
          <el-input v-model="addForm.jumpLink"></el-input>
        </el-form-item>
        <el-form-item label="标签配图" prop="imgPath">
          <el-upload
            class="avatar-uploader"
            action="https://jsonplaceholder.typicode.com/posts/"
            :show-file-list="false"
            :on-error="_uploadImgError"
            :before-upload="_beforeUpload"
            :headers="{
              Authorization:$GetToken()
            }"
            :on-success="_uploadImgSuccess"
            :on-remove="_uploadImgRemove"
            :auto-upload="true"
          >
            <div
              class="addImg"
              :style="{ border: addForm.imgPath ? '' : '#8c939d 1px solid' }"
            >
              <el-image
                style="width: 200px; "
                v-if="addForm.imgPath"
                :src="addForm.imgPath"
                fit="contain"
              ></el-image>
              <!-- <img v-if="addForm.imgPath" :src="addForm.imgPath" /> -->
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="addCase">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="编辑案例信息" :visible.sync="editDialog" width="700px">
      <el-form
        ref="editForm"
        :rules="addFormRules"
        :model="editForm"
        label-width="80px"
      >
        <el-form-item label="标签标题" prop="tapName">
          <el-input
            v-model="editForm.tapName"
            maxlength="10"
            class="company-name"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="标签描述" prop="introduction">
          <el-input
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            v-model="editForm.introduction"
          ></el-input>
        </el-form-item>
        <el-form-item label="跳转链接" prop="jumpLink">
          <el-input v-model="editForm.jumpLink"></el-input>
        </el-form-item>
        <el-form-item label="标签配图" prop="imgPath">
          <el-upload
            class="avatar-uploader"
            action="https://jsonplaceholder.typicode.com/posts/"
            :show-file-list="false"
            :on-error="_uploadImgError"
            :before-upload="_beforeUpload"
            :on-success="_uploadImgSuccess"
            :headers="{
              Authorization:$GetToken()
            }"
            :on-remove="_uploadImgRemove"
            :auto-upload="true"
          >
            <div
              class="addImg"
              :style="{ border: editForm.imgPath ? '' : '#8c939d 1px solid' }"
            >
              <el-image
                style="width: 200px; "
                v-if="editForm.imgPath"
                :src="editForm.imgPath"
                fit="contain"
              ></el-image>
              <!-- <img v-if="editForm.imgPath" :src="editForm.imgPath" /> -->
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="editCase">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    var validateImgPath = (rule, value, callback) => {
      if (value === "") {
        if (this.isEdit) callback();
        callback(new Error("请上传标签配图"));
      } else {
        callback();
      }
    };
    return {
      searchParams: {
        status: "1",
        limit: 10,
        page: 1
      },
      total: 10,
      loading: false,
      dataList: [],
      ids: [],
      addDialog: false,
      addForm: { tapName: "", introduction: "", imgPath: "", jumpLink: "" },
      editDialog: false,
      editForm: {},
      addFormRules: {
        tapName: [
          { required: true, message: "请输入文章详情", trigger: "blur" },
          { max: 10, message: "标题最多十个10 个字符", trigger: "blur" }
        ],
        introduction: [
          { required: true, message: "请输入文章详情", trigger: "blur" },
          { max: 100, message: "描述最多十个100 个字符", trigger: "blur" }
        ],
        imgPath: [
          { required: true, validator: validateImgPath, trigger: "change" }
        ],
        jumpLink: [
          { required: true, message: "请输入跳转链接", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getDataList();
  },
  mounted() {
    // 设置操作栏长度
    this.$nextTick(() => {
      this.$store.dispatch("setWidth", this.$refs.operation.children.length);
    });
  },
  methods: {
    // 获取Case列表
    async getDataList() {
      this.loading = true;
      this.dataList = [
        {
          id: 1,
          imgPath:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          tapName: "企业实验室1",
          introduction:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          jumpLink:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          status: 1,
          createTime: "2022-05-30 12:00"
        },
        {
          id: 2,
          imgPath:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          tapName: "企业实验室2",
          introduction:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          jumpLink:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          status: 1,
          createTime: "2022-05-30 12:00"
        },
        {
          id: 3,
          imgPath:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          tapName: "企业实验室3",
          introduction:
            "提供专业网络安全服务、黑客技术知识、信息安全在线教育院校。为学员提供系统性的安全意识培训、网络安全工程师、CISP认证等在线教程,涵盖安全管理/咨询/认证三大咨询服务...",
          jumpLink:
            "https://images.pexels.com/photos/125510/pexels-photo-125510.jpeg?auto=compress&cs=tinysrgb&w=1600",
          status: 1,
          createTime: "2022-05-30 12:00"
        }
      ];
      this.loading = false;
    },

    // 一次查询多少条改变事件：limit=newSize
    handleSizeChange(newSize) {
      this.searchParams.limit = newSize;
      this.getDataList();
    },

    // 目前在第几页,当前页面改变： page = newSize
    handleCurrentChange(newSize) {
      this.searchParams.page = newSize;
      this.getDataList();
    },

    // 编辑
    async openEditCase(scope) {
      this.editDialog = true;
      this.editForm = Object.assign({}, scope.row);
    },
    async editCase() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return;
      });
    },
    // 删除
    async deleteCase(ids) {
      var configResult = await this.$confirm(
        "此操作将永久删除该Case图片, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).catch(err => {
        return err;
      });
      if (configResult !== "confirm") {
        return this.$message.info({ message: "已经取消删除", duration: 1000 });
      }
      this.$message.success({ message: "删除成功", duration: 1000 });
      console.log(ids);
      this.getDataList();
    },

    // 表格前勾选框
    handleSelectionChange(val) {
      this.ids = val.map(item => {
        return item.id;
      });
    },

    async addCase() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return;
      });
    },

    // 上传封面图片
    _uploadImgError(err, file, fileList) {
      this.$message.error("文件上传失败，请重试！");
    },
    _uploadImgSuccess(res, file) {
      let status = res.data && Object.keys(res.data)[0];
      if (status == 200) {
        // 图片审核通过
        // if (status == 400) this.form.status = "4"; // 把待审核状态传给后端
        // this.form.coverImage = res.data[status][0];
        this.addForm.imgPath = URL.createObjectURL(file.raw);
        console.log(this.addForm.imgPath);
        this.$refs.addForm.validateField("imgPath");
      } else if (status == 400) {
        this.$alert("图片疑似违规，请重新上传！", "提示", {
          confirmButtonText: "确定"
        });
      } else if (status == 500) {
      }
    },

    _beforeUpload(file) {
      let types = ["image/jpg", "image/png", "image/jpeg"];
      // console.log(file);
      const isJPG = types.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg或png 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      const isSize = new Promise(function(resolve, reject) {
        const img = new Image();
        const _URL = window.URL || window.webkitURl;
        img.src = _URL.createObjectURL(file);
        img.onload = function() {
          const valid = img.width === 1920 && img.height === 380; //上传图片尺寸判定
          valid ? resolve() : reject(new Error("error"));
        };
      }).then(
        () => {
          return file;
        },
        () => {
          this.$message.error("上传图片尺寸必须为 1920 * 380");
          return Promise.reject(new Error("error"));
        }
      );
      return isJPG && isLt2M && isSize;
    },

    _uploadImgRemove(file, fileList) {
      this.addForm.imgPath = "";
      let formName = this.addDialog ? "addFrom" : "editForm";
      this.$refs[formName].validateField("imgPath");
    }
  }
};
</script>

<style lang="scss" scoped>
.content-manage-child {
  padding: 20px;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /deep/ .el-dialog {
    .el-dialog__header {
    }
    .el-dialog__body {
      .el-form {
        .el-form-item {
          .el-form-item__content {
            .company-name {
              width: 200px;
              input {
              }
            }
            .el-textarea {
              //   width: 500px;
            }
          }
        }
      }
    }
  }
}
.addImg {
  .avatar-uploader-icon {
    border: 1px solid balck;
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
}
</style>
