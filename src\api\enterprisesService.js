// 后台 - 设置 -资源管理 - 文件管理

import request from "@/utils/request";
//

// 获取添加企业
export function addNewEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/add",
    method: "post",
    data: params
  });
}
// 更新企业排序
export function updateEnterpriseSort(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/exchangeSort",
    method: "post",
    data: params
  });
}
// 编辑企业信息
export function editEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/edit",
    method: "post",
    data: params
  });
}
//批量删除企业
export function deleteEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/deleteBatch",
    method: "post",
    data: params
  });
}
//批量公开企业
export function showEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/showBatch",
    method: "post",
    data: params
  });
}
//批量隐藏企业
export function hideEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/hideBatch",
    method: "post",
    data: params
  });
}
//企业上下架
export function updateEnterpriseStatus(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/updateStatus",
    method: "post",
    data: params
  });
}
//获取企业分类
export function getCategoryList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/getList",
    method: "post",
    data: params
  });
}
//获取企业列表
export function getEnterpriseList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/getList",
    method: "post",
    data: params
  });
}
//获取企业信息
export function getEnterpriseInfo(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/getInfo",
    method: "get",
    params: params
  });
}
//获取企业粉丝列表
export function getEnterpriseFansList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/live/fans/list",
    method: "post",
    data: params
  });
}
//批量禁用企业
export function disableEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/updateAbleStatus",
    method: "post",
    data: params
  });
}
//批量启用企业
export function enableEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/disableStatus",
    method: "post",
    data: params
  });
}
//批量通过审核企业
export function auditEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/auditBatch",
    method: "post",
    data: params
  });
}
//审核不通过企业
export function auditRejectEnterprise(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/auditFail",
    method: "post",
    data: params
  });
}
//审核不通过或待审核企业详情
export function auditRejectEnterpriseDetail(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/getNoPassInfo",
    method: "get",
    params: params
  });
}
//添加分类
export function addCategory(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/add",
    method: "post",
    data: params
  });
}
//修改分类
export function editCategory(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/edit",
    method: "post",
    data: params
  });
}
//删除分类
export function deletCategory(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/deleteBatch",
    method: "post",
    data: params
  });
}
//排序分类
export function sortCategory(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/dragSort",
    method: "post",
    data: params
  });
}
//排序置顶
export function sortCategoryTop(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/category/toTop",
    method: "get",
    params: params
  });
}
//获取工作台信息
export function getWorkbenchInfo(params) {
  return request({
    url:
      process.env.ADMIN_API + "/classroom/speaker/category/openClassWorkbench",
    method: "get",
    params: params
  });
}
//根据号码查询用户
export function getUserByPhone(params) {
  return request({
    url: process.env.ADMIN_API + "/user/getUserByPhone",
    method: "get",
    params: params
  });
}

//讲者  根据号码查询用户
export function getUserByPhoneSpeaker(params) {
  return request({
    url: process.env.ADMIN_API + "/user/getBoundList",
    method: "post",
    data: params
  });
}
//企业 根据号码查询用户关联创建人
export function getUserByPhoneCreater(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/getSpeakerList",
    method: "post",
    data: params
  });
}
//企业 根据号码查询用户关联讲者
export function getUserByPhoneEnterpriseSpeak(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/getMemberList",
    method: "post",
    data: params
  });
}
//企业 创建人关联
export function addCreater(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/linkCreator",
    method: "post",
    data: params
  });
}
//企业 移除创建人关联
export function removeCreater(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/removeCreator",
    method: "post",
    data: params
  });
}
//企业 讲者(成员)关联
export function addSpeaker(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/linkMember",
    method: "post",
    data: params
  });
}
//企业 移除讲者(成员)关联
export function removeSpeaker(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/removeMember",
    method: "post",
    data: params
  });
}
//企业企业讲者列表
export function getEnterpriseSpeakerList(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/getSpeakerMenberList",
    method: "post",
    data: params
  });
}

// 更改企业实验室主任
export function changeCreator(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/changeCreator",
    method: "get",
    params
  });
}

//讲者  根据号码查询用户
export function getUserCreator(params) {
  return request({
    url: process.env.ADMIN_API + "/user/getCreator",
    method: "post",
    data: params
  });
}

//获取实验室主任列表
export function getEnterpriseCreator(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/getEnterpriseCreator",
    method: "get",
    params
  });
}

//解绑获取实验室主任
export function unbind(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/enterprise/unbind",
    method: "get",
    params
  });
}

//解绑获取实验室主任
export function unbindSpeaker(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/unbind",
    method: "get",
    params
  });
}

//讲者置顶
export function updateTop(params) {
  return request({
    url: process.env.ADMIN_API + "/classroom/speaker/updateTop",
    method: "post",
    data: params
  });
}
