/*
    Paraíso (dark)
    Created by <PERSON> (http://github.com/idleberg)
    Inspired by the art of Rubens LP (http://www.rubenslp.com.br)
*/

/* Paraíso Comment */
.hljs-comment,
.hljs-title {
  color: #8d8687;
}

/* Paraíso Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #ef6155;
}

/* Paraíso Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #f99b15;
}

/* Paraíso Yellow */
.ruby .hljs-class .hljs-title,
.css .hljs-rules .hljs-attribute {
  color: #fec418;
}

/* Paraíso Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #48b685;
}

/* Paraíso Aqua */
.css .hljs-hexcolor {
  color: #5bc4bf;
}

/* Paraíso Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #06b6ef;
}

/* Paraíso Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #815ba4;
}

.hljs {
  display: block;
  background: #2f1e2e;
  color: #a39e9b;
  padding: 0.5em;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}
