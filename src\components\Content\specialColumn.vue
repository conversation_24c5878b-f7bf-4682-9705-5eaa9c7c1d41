<template>
  <div class="sc-container">
    <el-table
      v-loadMore="scrollLoad"
      :header-cell-style="{ textAlign: 'center' }"
      :data="ScList"
      highlight-current-row
      style="width: 100%"
      height="300"
      @current-change="selectScCurrentChange"
    >
      <!-- <el-table-column
        align="center"
        prop="fileUid"
        label="专栏ID"
        show-overflow-tooltip
        width="180"
      >
      </el-table-column> -->
      <el-table-column align="center" prop="title" label="标题"/>
      <el-table-column align="center" prop="title" label="类型">
        <template slot-scope="scope">
          {{ scope.row.hasTrySee == 1 ? "试听" : "非试听" }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="coverUrl"
        label="封面"
        width="180"
      >
        <template slot-scope="scope">
          <el-image
            :preview-src-list="[scope.row.coverUrl]"
            :src="scope.row.coverUrl"
            style="width: 90px; height: 60px"
            fit="fill"
          />
        </template>
      </el-table-column>
    </el-table>
    <!-- <div
      :style="{
        marginTop: '20px',
        display: 'flex',
        justifyContent: 'center',
      }"
    >
      <el-pagination
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :total="total"
        background
        layout="prev, pager, next"
        @size-change="selectScSizeChange"
        @current-change="selectScCurrentPageChange"
      />
    </div> -->
    <!-- <div
      :style="{
        marginTop: '20px',
        display: 'flex',
        justifyContent: 'flex-end',
      }"
    >
      <el-button @click="cancelSelectSc">取消</el-button>
      <el-button type="primary" @click="realSelectSc">确定</el-button>
    </div> -->
  </div>
</template>
<script>
import { getListData } from '@/api/course/course'
export default {
  props: {
    // 是否筛选试听课程
    hasTrySee: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      currentScInfo: '',
      ScList: [],
      currentPage: 1,
      pageSize: 10,
      loading: false,
      total: 0
    }
  },
  mounted() {
    this.initScInfo()
  },
  methods: {
    Popup() {
      this.initScInfo()
    },
    // 滚动到底部加载
    scrollLoad() {
      if (this.total <= this.ScList.length || this.loading) return
      this.currentPage++
      this.initScInfo()
    },
    // 取消专栏选择
    cancelSelectSc() {
      this.$emit('cancelSelectSc')
    },
    // 确定专栏选择
    realSelectSc() {
      this.$emit('update:currentScInfo', this.currentScInfo)
      this.$emit('realSelectSc', this.currentScInfo)
    },
    // 专栏单选
    selectScCurrentChange(data) {
      this.currentScInfo = data
      this.$emit('update:currentScInfo', this.currentScInfo)
      this.$emit('realSelectSc', this.currentScInfo)
    },
    selectScSizeChange(val) {
      this.pageSize = val
      this.initScInfo()
    },
    selectScCurrentPageChange(val) {
      this.currentPage = val
      this.initScInfo()
    },
    // 加载专栏信息
    async initScInfo() {
      this.loading = true
      const params = {
        type: 3,
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        hasTrySee: this.hasTrySee
      }
      const result = await getListData(params)
      console.log('专栏信息', result)
      if (result.code == this.$ECode.SUCCESS) {
        this.total = result.data.total
        this.ScList = [...this.ScList, ...result.data.records]
      } else {
        this.$commonUtil.message.error(res.message)
      }
      this.loading = false
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
