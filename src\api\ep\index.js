// 在线企业实验室接口
import request from "@/utils/request";

// 查询在线企业实验室列表数据
export function queryOnlineEp(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/selectEnterpriseLabList",
    method: "post",
    data: params
  });
}

// 修改在线企业实验室状态
export function changeOnlineEpStatus(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/manageLabStatus",
    method: "post",
    data: params
  });
}

// 查询认证审核后台列表
export function queryCertifAuditList(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/certificationAuditList",
    method: "post",
    data: params
  });
}

// 通过认证
export function passAudit(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/passAudit",
    method: "post",
    data: params
  });
}

// 拒绝认证
export function refuseAudit(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/refuseAudit",
    method: "post",
    data: params
  });
}

// 查看认证
export function checkAudit(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/getAuthInfo",
    method: "post",
    data: params
  });
}
// 查看认证
export function auditEp(params) {
  return request({
    url: process.env.ADMIN_API + "/enterprise/updateAuditStatusBatch",
    method: "post",
    data: params
  });
}
