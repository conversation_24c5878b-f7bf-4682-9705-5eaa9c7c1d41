<template>
  <div class="video-library-container">
    <el-table
      highlight-current-row
      @selection-change="currentSelectionChange"
      @current-change="selectVideoCurrentChange"
      :header-cell-style="{ textAlign: 'center' }"
      :data="videoList"
      row-key="uid"
      style="width: 100%"
    >
      <el-table-column  v-if="flag==2" type="selection" :reserve-selection="true" width="55">
      </el-table-column>
      <el-table-column
        align="center"
        prop="videoUid"
        label="视频ID"
        show-overflow-tooltip
        width="180"
      >
      </el-table-column>
      <el-table-column align="center" prop="title" label="标题">
      </el-table-column>
      <el-table-column
        align="center"
        prop="fileUrl"
        label="视频封面"
        width="180"
      >
        <template slot-scope="scope">
          <el-image
            :preview-src-list="[scope.row.fileUrl]"
            style="width: 100px; height: 100px"
            :src="scope.row.fileUrl"
            fit="fill"
          ></el-image>
        </template>
      </el-table-column>
    </el-table>
    <div
      :style="{
        marginTop: '20px',
        display: 'flex',
        justifyContent: 'center',
      }"
    >
      <el-pagination
        @size-change="selectVideoSizeChange"
        @current-change="selectVideoCurrentPageChange"
        background
        layout="prev, pager, next"
        :current-page.sync="currentPage"
        :page-size="pageSize"
        :total="total"
      >
      </el-pagination>
    </div>
    <div
      :style="{
        marginTop: '20px',
        display: 'flex',
        justifyContent: 'flex-end',
      }"
    >
      <el-button @click="cancelSelectVideo">取消</el-button>
      <el-button type="primary" @click="realSelectVideo">确定</el-button>
    </div>
  </div>
</template>
<script>
import { videoList } from "@/api/content/video";
export default {
  props: {
    flag: {
      type: Number,
      default:1
    }
  },
  data() {
    return {
      currentVideoInfo: "",
      videoList: [],
      currentPage: 1,
      pageSize: 3,
      total: 0,
    };
  },
  mounted(){
      this.initVideoInfo();
  },
  methods: {
    Popup() {
      this.initVideoInfo();
    },
    // 取消视频选择
    cancelSelectVideo() {
      this.$emit("cancelSelectVideo");
    },
    // 确定视频选择
    realSelectVideo() {
      this.$emit("update:currentVideoInfo", this.currentVideoInfo);
      this.$emit("realSelectVideo", this.currentVideoInfo);
    },
    // 视频库单选
    selectVideoCurrentChange(data) {
      this.currentVideoInfo = data;
    },
    // 表格多选触发
    currentSelectionChange(data) { 
      this.$emit('currentSelectionChange', data);
    },
    selectVideoSizeChange(val) {
      this.pageSize = val;
      this.initVideoInfo();
    },
    selectVideoCurrentPageChange(val) {
      this.currentPage = val;
      this.initVideoInfo();
    },
    // 加载视频库信息
    async initVideoInfo() {
      let params = {
        pageSize: this.pageSize,
        currentPage: this.currentPage,
      };
      let result = await videoList(params);
      if (result.code == this.$ECode.SUCCESS) {
        this.total = result.data.total;
        this.videoList = result.data.records;
      } else {
        this.$commonUtil.message.error(res.message);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
</style>