import request from '@/utils/request'

// 获取题目分页列表
export function getSubjectList(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/pageList',
    method: 'post',
    data: params
  })
}

// 获取题目分页列表
export function getSubjectListV2(params) {
  return request({
    url: process.env.ADMIN_API + '/train/titleV2/pageList',
    method: 'post',
    data: params
  })
}

// 获取题目详情接口
export function getSubjectDetail(params) {
  return request({
    url: process.env.ADMIN_API + `/train/title/details/${params.uid}`,
    method: 'get'
  })
}

// 获取题目分页列表（包含答案）
export function getSubjectAnswerList(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/pageListData',
    method: 'post',
    data: params
  })
}

// 添加题目
export function addSubject(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/add',
    method: 'post',
    data: params
  })
}

// 修改题目
export function editSubject(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/edit',
    method: 'post',
    data: params
  })
}

// 删除题目
export function deleteSubject(params) {
  return request({
    url: process.env.ADMIN_API + `/train/title/deleteBatch`,
    method: 'post',
    data: params
  })
}

// 转移题目
export function moveSubject(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/moving',
    method: 'post',
    data: params
  })
}

// 复制题目
export function copySubject(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/copy',
    method: 'post',
    data: params
  })
}

// 批量excel导入题目
export function importExcelSubject(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/importExcel',
    method: 'post',
    data: params
  })
}

//word导入题目
export function inputWordTitle(params) {
  return request({
    url: process.env.ADMIN_API + '/train/title/inputTitle',
    method: 'post',
    data: params
  })
}

//题目审核
export function manualAudit(params) {
  return request({
    url: process.env.ADMIN_API + `/train/title/manualAudit?operateType=${params.operateType}`,
    method: 'post',
    data: params.uidList
  })
}

//题目删除策略引用关系
export function clearReference(params) {
  return request({
    url: process.env.ADMIN_API + `/strategyTitleRelation/clearRelation`,
    method: 'post',
    data: params
  })
}

//获取审核状态数量
export function getAuditStatusCount(params) {
  return request({
    url: process.env.ADMIN_API + `/train/title/getAuditStatusCount`,
    method: 'get',
    params
  })
}

//获取星级题目数量
export function getStarLevelCountV2(params) {
  return request({
    url: process.env.ADMIN_API + `/train/titleV2/getStarLevelCount`,
    method: 'get',
    params
  })
}