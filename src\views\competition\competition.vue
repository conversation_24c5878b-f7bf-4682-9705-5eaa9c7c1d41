<template>
    <div class="root">
        <div class="top-form">
            <el-form :inline="true" :model="topForm" class="demo-form-inline">
                <el-form-item label="类型">
                    <el-select clearable @change="handleType" v-model="topForm.competitionType" placeholder="类型">
                        <el-option label="国赛" :value=1></el-option>
                        <el-option label="省赛" :value=2></el-option>
                        <el-option label="行业赛" :value=3></el-option>
                        <el-option label="企业赛" :value=4></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select clearable @change="handleStatus" v-model="topForm.status" placeholder="状态">
                        <el-option label="报名中" :value=4></el-option>
                        <el-option label="待开赛" :value=3></el-option>
                        <el-option label="进行中" :value=2></el-option>
                        <el-option label="已结束" :value=1></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-input size="medium" placeholder="搜索" suffix-icon="el-icon-search" v-model="topForm.keyword">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button v-if="multipleSelection.length" type="danger" @click="handleDelAll">删除</el-button>
                </el-form-item>
                <div style="float:right;margin-right:45px;" class="top_create">
                    <el-button type="primary" @click="onCreate">创建</el-button>
                </div>
            </el-form>
        </div>
        <div class="content_div">
            <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" header-align="center">
                </el-table-column>
                <el-table-column label="图片" width="140" align="center">
                    <template slot-scope="scope">
                        <img style="height:60px;" :src="scope.row.pictureUrl">
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" align="center" header-align="center">
                </el-table-column>
                <el-table-column prop="matchType" label="类型" width="140">
                    <template slot-scope="scope">
                        <span>{{ scope.row.competitionType | filterType }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center" header-align="center" width="120">
                    <template slot-scope="scope">
                        <span>{{ scope.row.status | filterStatus }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" align="center" header-align="center" width="220">
                </el-table-column>
                <el-table-column label="操作" align="center" header-align="center" width="220">
                    <template slot-scope="scope">
                        <span @click="handleLook(scope.row)" :class="{ 'span_col': scope.row.status != 2 }"
                            style="color:#006eff;cursor:pointer;">查看</span>
                        <span v-if="scope.row.status != 2" @click="handleEdit(scope.row)" class="span_col"
                            style="color:#006eff;margin-left:8px;cursor:pointer;">编辑</span>
                        <span v-if="scope.row.status != 2" @click="handleDel(scope.row)"
                            style="color:#006eff;margin-left:8px;cursor:pointer;">删除</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog :close-on-click-modal="false" class="dialog_div" :title="submitType == 'add' ? '创建赛事' : '修改赛事'"
            :visible.sync="dialogFormVisible" @close="handleClose">
            <el-form :model="createForm" :rules="matchRule" ref="matchForm" label-width="100px">
              <el-form-item  label="关联比赛" prop="bjMatchId">
                <el-select
                  v-model="createForm.bjMatchId"
                  clearable
                  placeholder="--请选择--"
                  style="width:140px"
                >
                  <el-option
                    v-for="item in bjMatchDataSelectList"
                    :key="item.uid"
                    :label="item.matchName"
                    :value="item.matchId"
                  ></el-option>
                </el-select>
              </el-form-item>

                <el-form-item class="match_title" label="比赛标题" prop="title">
                    <el-input type="textarea" v-model="createForm.title" show-word-limit maxlength="30"></el-input>
                    <el-form-item class="upload_div" label="上传封面" required>
                        <div class="tip">
                            <p>*支持.JPG,.JPEG,.PNG不超过3M</p>
                        </div>
                        <el-upload
                        class="avatar-uploader"
                        :action="actionPath"
                        :data="otherData"
                        :headers="{
                            Authorization:$GetToken()
                        }"
                            :show-file-list="false" :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload">
                            <img v-if="imageUrl" :src="imageUrl" class="avatar">
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <div class="err_text">
                            {{ imgText }}
                        </div>
                    </el-form-item>
                </el-form-item>
                <el-form-item label="报名时间">
                    <el-date-picker v-model="tempsignupDate" type="datetimerange" start-placeholder="报名开始时间"
                        end-placeholder="报名结束时间" format="yyyy-MM-dd HH:mm" @change="handlesignUpStart"
                        :picker-options="pickerSignUpStart">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="比赛时间" prop="competitionStartTime">
                    <el-date-picker v-model="tempmatchDate" type="datetimerange" start-placeholder="比赛开始时间"
                        end-placeholder="比赛结束时间" format="yyyy-MM-dd HH:mm" :picker-options="pickerMatchStart"
                        @change="handleMatchStart">
                    </el-date-picker>
                </el-form-item>
                <el-form-item class="yiban" label="比赛类型" prop="competitionType">
                    <el-select v-model="createForm.competitionType" placeholder="不限">
                        <el-option label="国赛" :value=1></el-option>
                        <el-option label="省赛" :value=2></el-option>
                        <el-option label="行业赛" :value=3></el-option>
                        <el-option label="企业赛" :value=4></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item class="yiban" label="主办方" prop="createBy">
                    <el-input placeholder="主办方名称" v-model="createForm.createBy" clearable maxlength="30"
                        show-word-limit>
                    </el-input>
                </el-form-item>
                <el-form-item class="yiban" label="比赛链接" prop="competitionLink">
                    <el-input placeholder="比赛链接详情" v-model="createForm.competitionLink" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item label="比赛模式" prop="competitionWay">
                    <el-input type="textarea" v-model="createForm.competitionWay" show-word-limit maxlength="30">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button :disabled="submitType == 'edit' && createForm.status == 2" type="primary"
                    @click="submitCreatMatch('matchForm')">{{ submitType == 'add' ? '创建' : '修改' }}
                </el-button>
            </div>
        </el-dialog>
        <competitionDialogVue ref="competition" :infoItem="infoItem"></competitionDialogVue>
        <div class="paging_div">
            <el-pagination v-show="tableData.length" @current-change="handleCurrentChange" background
                :current-page.sync="topForm.currentPage" :page-size="10" layout="total, prev, pager, next"
                :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {getToken} from '../../utils/auth';
import {msToDate} from '@/utils/formatDate.js';
import {
  addCompetition,
  delCompetition,
  editCompetition,
  getBjMatchList,
  getCompetition
} from '@/api/competitionManage.js';

import competitionDialogVue from './components/competitionDialog.vue';

export default {
    data() {
        return {
            submitType: 'add',
            total: 0,
            infoItem: {},
            topForm: {
                competitionType: '',
                status: '',
                keyword: '',
                type: 1,  //0:前端1后台
                currentPage: 1,
                pageSize: 10
            },
            bjMatchDataSelectList: [], // 比赛平台比赛列表下拉数据
            tableData: [],
            tempmatchDate: '',
            tempsignupDate: '',
            multipleSelection: [],
            dialogFormVisible: false,
            formLabelWidth: '80px',
            imgText: '',
            createForm: {
                title: '',  //标题
                bjMatchId: '',  //北京比赛平台关联id
                signUpStartTime: '', //报名开始时间
                signUpEndTime: '', //报名结束时间
                competitionStartTime: '', //比赛开始时间
                competitionEndTime: '',  //比赛结束时间
                fileUid: '', //封面
                competitionType: '', //比赛类型
                createBy: '', //主办方
                competitionLink: '', //比赛链接
                competitionWay: '', //比赛方式
                editUid: undefined, //编辑uid
            },
            pickerSignUpStart: {},
            pickerMatchStart: {},
            matchRule: {
                title: [
                    { required: true, message: '请输入比赛标题', trigger: ['blur'] },
                ],
                competitionType: [
                    { required: true, message: '请选择赛事类型', trigger: ['blur'] }
                ],
                createBy: [
                    { required: true, message: '请输入主办方名称', trigger: ['blur'] }
                ],
                competitionLink: [
                    { required: true, message: '请输入比赛链接', trigger: ['blur'] }
                ],
                competitionWay: [
                    { required: true, message: '请输入比赛方式', trigger: ['blur'] }
                ],
                competitionStartTime: [
                    {
                        required: true,
                        validator: (rules, value, callback) => {
                            if (!this.tempmatchDate) {
                                return callback(new Error('请选择比赛时间'));
                            } else {
                                return callback();
                            }
                        },
                    }
                ]
            },
            imageUrl: '',
            actionPath: '',
            otherData: {
                source: "picture",
                userUid: "uid00000000000000000000000000000000",
                adminUid: "uid00000000000000000000000000000000",
                projectName: "blog",
                sortName: "admin",
                token: null,
            },
        }
    },
    components: {
        competitionDialogVue
    },
    created() {
        // 图片上传地址
        this.actionPath = process.env.PICTURE_API + "/file/cropperPicture";
        this.getCompetitionList(this.topForm);
    },
    mounted() {
        this.otherData.token = getToken() || null;
    },
    filters: {
        filterType: function (val) {
            if (val == 1) {
                return '国赛';
            } else if (val == 2) {
                return '省赛';
            } else if (val == 3) {
                return '行业赛';
            } else if (val == 4) {
                return '企业赛';
            }
        },

        filterStatus: function (val) {
            if (val == 4) {
                return '报名中';
            } else if (val == 3) {
                return '待开赛';
            } else if (val == 2) {
                return '进行中';
            } else if (val == 1) {
                return '已结束';
            } else if (val == 0) {
                return '未开始';
            }
        }
    },
    methods: {
        initParams() {
            this.topForm.currentPage = 1;
            this.topForm.pageSize = 10;
            this.tableData = [];
        },
        // 初始化表单
        initForm() {
            this.createForm = {
                title: '',  //标题
                signUpStartTime: '', //报名开始时间
                signUpEndTime: '', //报名结束时间
                competitionStartTime: '', //比赛开始时间
                competitionEndTime: '',  //比赛结束时间
                fileUid: '', //封面
                competitionType: '', //比赛类型
                createBy: '', //主办方
                competitionLink: '', //比赛链接
                competitionWay: '', //比赛方式
                editUid: undefined, //编辑uid
            };
            this.imageUrl = '';
            this.tempmatchDate = '';
            this.tempsignupDate = '';
            this.pickerSignUpStart = {
                disabledDate(time) {
                    return false;
                }
            }
            this.pickerMatchStart = {
                disabledDate(time) {
                    return false;
                }
            };

        },
        //获取赛事列表
        getCompetitionList(params) {
            getCompetition(params).then((res) => {
                if (res.code == this.$ECode.SUCCESS) {
                    this.tableData = res.data.records;
                    this.total = res.data.total;
                }
            })
        },
        //新建赛事的点击事件
        submitCreatMatch(formName) {
            if (this.createForm.fileUid) {
                this.$refs.matchForm.validateField('competitionStartTime');
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        if (this.tempsignupDate) {
                            this.createForm.signUpStartTime = msToDate(this.tempsignupDate[0]).hasTime;
                            this.createForm.signUpEndTime = msToDate(this.tempsignupDate[1]).hasTime;
                        }
                        this.createForm.competitionStartTime = msToDate(this.tempmatchDate[0]).hasTime;
                        this.createForm.competitionEndTime = msToDate(this.tempmatchDate[1]).hasTime;
                        if (this.submitType == 'add') {
                            addCompetition(this.createForm).then((res) => {
                                if (res.code == this.$ECode.SUCCESS) {
                                    this.$commonUtil.message.success(res.message);
                                    this.dialogFormVisible = false;
                                    this.getCompetitionList(this.topForm);
                                    this.initForm();
                                } else {
                                    this.$commonUtil.message.error(res.message);
                                }
                            })
                        } else {
                            editCompetition(this.createForm).then((res) => {
                                if (res.code == this.$ECode.SUCCESS) {
                                    this.$commonUtil.message.success(res.message);
                                    this.dialogFormVisible = false;
                                    this.getCompetitionList(this.topForm);
                                    this.initForm();
                                } else {
                                    this.$commonUtil.message.error(res.message);
                                }
                            })
                        }
                    } else {
                        console.log('不能提交')
                        return false;
                    }
                })
            } else {
                this.imgText = '请上传封面'
            }
        },

        //选择赛事类型
        handleType(value) {
            this.initParams();
            this.getCompetitionList(this.topForm);
        },

        //选择赛事状态
        handleStatus(value) {
            this.initParams();
            this.getCompetitionList(this.topForm);
        },

        //删除赛事
        handleDel(item) {
            this.$confirm('确定要删除此比赛吗？', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                center: true
            }).then(() => {
                let params = [];
                params.push(item);
                delCompetition(params).then((res) => {
                    if (res.code == this.$ECode.SUCCESS) {
                        this.$commonUtil.message.success(res.message);
                        this.getCompetitionList(this.topForm);
                    }
                })
            });
        },

        //删除所选赛事
        handleDelAll() {
            let arr = [];
            if (this.multipleSelection.length == 0) {
                this.$commonUtil.message.warning('请选择要删除的赛事');
                return;
            }

            this.multipleSelection.filter((item) => {
                if (item.status == 2) {
                    arr.push(item);
                }
            })

            if (arr.length == 0) {
                this.$confirm('确定要删除所选比赛吗？', '', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    center: true
                }).then(() => {
                    delCompetition(this.multipleSelection).then((res) => {
                        if (res.code == this.$ECode.SUCCESS) {
                            this.$commonUtil.message.success(res.message);
                            this.getCompetitionList(this.topForm);
                        }
                    })
                });
            } else {
                this.$commonUtil.message.warning('不能勾选删除进行中的比赛，请勾选其他状态的赛事');
                return;
            }

        },

        //编辑赛事
        handleEdit(item) {
            this.createForm = item;
            this.createForm.editUid = item.uid;
            this.imageUrl = item.pictureUrl;
            if (item.signUpStartTime && item.signUpEndTime) {
                this.tempsignupDate = [item.signUpStartTime, item.signUpEndTime];
                this.pickerMatchStart = {
                    disabledDate(time) {
                        return time.getTime() < ((new Date(item.signUpEndTime).getTime()))
                    }
                }
            }
            this.tempmatchDate = [item.competitionStartTime, item.competitionEndTime];
            this.pickerSignUpStart = {
                disabledDate(time) {
                    return time.getTime() > ((new Date(item.competitionStartTime).getTime() - 24 * 60 * 60 * 1000))
                }
            }
            if (this.imageUrl) {
                this.imgText = '';
            }
            this.dialogFormVisible = true;
            this.submitType = 'edit';
        },

        // 查看赛事
        handleLook(item) {
            this.$refs['competition'].comDialog = true;
            this.infoItem = item;
        },

        onSubmit() {
            this.initParams();
            this.getCompetitionList(this.topForm);
        },

        //创建赛事
        onCreate() {
            // 获取赛事平台列表数据
            this.queryBjMatchList();
            if (this.submitType == 'edit') {
                this.submitType = 'add';
                this.initForm();
            }
            this.signStartFlag = false;
            this.dialogFormVisible = true;
        },

        toggleSelection(rows) {
            if (rows) {
                rows.forEach(row => {
                    this.$refs.multipleTable.toggleRowSelection(row);
                });
            } else {
                this.$refs.multipleTable.clearSelection();
            }
        },

        //删除多选的点击事件
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },

        //上传赛事封面成功
        handleAvatarSuccess(res, file) {
            if (res.code == this.$ECode.SUCCESS) {
                // this.imageUrl = URL.createObjectURL(file.raw);
                this.imageUrl = res.data[0] && res.data[0].url
                this.createForm.fileUid = res.data[0] && res.data[0].uid;
                this.imgText = '';
            }
        },

        //上传赛事封面前
        beforeAvatarUpload(file) {
            this.otherData.file = file;
            let fileType = ['image/jpeg', 'image/jpg', 'image/png']
            // const isJPG = file.type === 'image/jpeg';
            const isJPG = fileType.includes(file.type);
            const isLt3M = file.size / 1024 / 1024 < 3;
            if (!isJPG) {
                this.$message.error('上传封面图片只能是 JPG,JPEG,PNG 格式!');
            }
            if (!isLt3M) {
                this.$message.error('上传封面图片大小不能超过 3MB!');
            }
            return isJPG && isLt3M;
        },

        //改变页数
        handleCurrentChange(val) {
            // this.initParams();
            this.topForm.currentPage = val;
            this.getCompetitionList(this.topForm);
        },

        handlesignUpStart(value) {
            if (!value) {
                this.tempsignupDate = '';
                this.pickerMatchStart = {
                    disabledDate(time) {
                        return false;
                    }
                }
            } else {
                this.tempsignupDate = value;
                this.pickerMatchStart = {
                    disabledDate(time) {
                        return time.getTime() < ((value[1].getTime()))
                    }
                }
            }

        },

        handleMatchStart(value) {
            if (!value) {
                this.tempmatchDate = '';
                this.pickerSignUpStart = {
                    disabledDate(time) {
                        return false;
                    }
                }
            } else {
                this.tempmatchDate = value;
                this.pickerSignUpStart = {
                    disabledDate(time) {
                        return time.getTime() > ((value[0].getTime() - 24 * 60 * 60 * 1000))
                    }
                }
            }

        },

        handleClose() {
            this.dialogFormVisible = false;
            if (this.submitType == 'add') {
                this.initForm();
            }

            if (this.submitType == 'edit') {
                this.getCompetitionList(this.topForm);
            }
        },
        focusDate() {
            this.$nextTick(() => {
                for (let i = 0; i < document.getElementsByClassName('el-button--text').length; i++) {
                    document.getElementsByClassName('el-button--text')[i].setAttribute('style', 'display:none'); // 隐藏此刻按钮
                }
            })
        },
      /**
       * 获取比赛下拉数据
       */
      queryBjMatchList: function () {
        getBjMatchList().then(response => {
          if (response.code == this.$ECode.SUCCESS) {
            this.bjMatchDataSelectList = response.data.records;
          }
        });
      },
    }
}
</script>

<style lang="scss" scoped>
.root {
    .top-form {
        padding: 10px;

        .el-select {
            /deep/ .el-input {
                width: 125px;
            }
        }
    }

    .content_div {
        margin: 0px 45px 0px 30px;

        .span_col {
            position: relative;
        }

        .span_col::after {
            position: absolute;
            content: '';
            display: inline-block;
            width: 1px;
            height: 14px;
            background: #006eff;
            top: 1px;
            left: 34px;
        }
    }

    .dialog_div {
        /deep/ .el-dialog {
            width: 68%;

            .match_title {
                .upload_div {
                    position: absolute;
                    top: 150%;
                    right: 12%;
                    text-align: center;

                    .tip {
                        font-size: 10px;
                        color: #ccc;
                        width: 180px;
                        height: 40px;
                        text-align: center;
                    }

                    .err_text {
                        color: #F56C6C;
                        font-size: 12px;
                    }

                    .avatar-uploader .el-upload {
                        border: 1px dashed #d9d9d9;
                        border-radius: 6px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }

                    .avatar-uploader .el-upload:hover {
                        border-color: #409EFF;
                    }

                    /deep/.el-upload {
                        position: relative;
                    }

                    .avatar-uploader-icon {
                        font-size: 22px;
                        color: black;
                        width: 104px;
                        height: 105px;
                        background: #f3f3f3;
                        line-height: 98px;
                        text-align: center;
                    }

                    .avatar {
                        width: 178px;
                        height: 178px;
                        display: block;
                    }
                }
            }

            .time_div {
                display: flex;
            }

            .yiban {
                .el-form-item__content {
                    width: 40%;

                    .el-select {
                        width: 100%;
                    }
                }
            }

            .dialog_footer {
                display: flex;
                justify-content: center;
            }
        }

    }


    .paging_div {
        margin: 20px 0px;
        display: flex;
        justify-content: center;
    }
}
</style>
