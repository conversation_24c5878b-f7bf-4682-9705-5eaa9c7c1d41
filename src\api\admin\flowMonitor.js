import request from '@/utils/request'

/**
 * 监控流量查询
 */
export function searchAwdFlowAPI(data) {
  return request({
    url: '/admin/awdFlow/searchAwdFlow',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 监控流量导出
 */
export function exportAwdFlowAPI(data) {
  return request({
    url: '/admin/awdFlow/exportAwdFlow',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
