import request from '@/utils/request'

/**
 * 保存拓扑
 */
export function addTopoDataAPI(data) {
  return request({
    url: '/topology/cepoSysTopology/addModelExamTopo',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 启动拓扑
 */
export function startTopoAPI(params) {
  return request({
    url: `topology/cepoSysTopology/startModelExamTopo/${params}`
  })
}

/**
 * 释放拓扑
 */
export function releaseTopoAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/releaseModelExamTopo/${params}`
  })
}

/**
 * 获取拓扑
 * @param {*} params
 */
export function getTopoDataAPI(params) {
  return request({
    url: `/topology/cepoSysTopology/getModelExamTopo/${params}`,
    method: 'get'
  })
}
