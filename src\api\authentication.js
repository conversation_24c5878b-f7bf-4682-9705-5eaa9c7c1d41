import request from '@/utils/request'

export function addTrainCertificate(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/add',
    method: 'post',
    data: params
  })
}

export function delTrainCertificate(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/deleteBatch',
    method: 'post',
    data: params
  })
}

export function editTrainCertificate(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/edit',
    method: 'post',
    data: params
  })
}

export function getTrainCertificate(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/getList/v2',
    method: 'post',
    data: params
  })
}

export function getTrainCertificateList(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/enableList',
    method: 'get',
    data: params
  })
}

export function onLinepreview(params) {
  return request({
    url: process.env.PICTURE_API + '/file/preview',
    method: 'get',
    responseType: 'blob',
    params
  })
}
export function checkRepeatSort(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/checkRepeatSort',
    method: 'post',
    data: params
  })
}
// 获取证书类型列表
export function getCertAllList(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/allCertificateList',
    method: 'get',
    params
  })
}

// 获取证书标签列表
export function getLabelList(params) {
  return request({
    url: process.env.ADMIN_API + '/label/getList/v2',
    method: 'post',
    data: params
  })
}

// 新增证书标签
export function addLabel(params) {
  return request({
    url: process.env.ADMIN_API + '/label/add',
    method: 'post',
    data: params
  })
}

// 编辑证书标签
export function editLabel(params) {
  return request({
    url: process.env.ADMIN_API + '/label/update',
    method: 'post',
    data: params
  })
}

// 编辑证书标签
export function delLabel(params) {
  return request({
    url: process.env.ADMIN_API + '/label/delete',
    method: 'get',
    params
  })
}

// 证书区域新增
export function sysAdd(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/area/add',
    method: 'post',
    data: params
  })
}

// 证书区域更新
export function sysEdit(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/area/edit',
    method: 'post',
    data: params
  })
}

// 证书区域删除
export function sysDelBatch(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/area/deleteBatch',
    method: 'post',
    data: params
  })
}

// 证书区域详情
export function sysInfo(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/area/getInfo',
    method: 'get',
    params: params
  })
}

// 证书资料查询
export function getDetails(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/getDetails',
    method: 'get',
    params: params
  })
}

// 证书区域列表
export function getSysList(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/area/getList',
    method: 'post',
    data: params
  })
}

// 证书区域列表
export function sysSort(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/area/sort',
    method: 'post',
    data: params
  })
}

// 体系上下架
export function updateStatus(params) {
  return request({
    url: process.env.ADMIN_API + `/trainCertificate/area/updateStatus?status=${params.status}`,
    method: 'post',
    data: params.uids
  })
}

// 体系上下架
export function trainCertificateUserEdit(params) {
  return request({
    url: process.env.ADMIN_API + `/trainCertificateUser/edit`,
    method: 'post',
    data: params
  })
}

// 获取关联证书下拉列表
export function getDropDownList(params) {
  return request({
    url: process.env.ADMIN_API + '/trainCertificate/getDropDownList',
    method: 'get',
    params
  })
}
