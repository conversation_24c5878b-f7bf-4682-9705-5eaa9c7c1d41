import request from '@/utils/request'

/**
 * 后台-前台首页菜单查询
 */
export function backSearchHomePageMenuAPI(data) {
  return request({
    url: 'admin/adminMenu/backSearchHomePageMenu',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 后台-首页菜单新增菜单
 */
export function addMenuAPI(data) {
  return request({
    url: 'admin/adminMenu/addMenu',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 后台-首页菜单修改
 */
export function updateMenuAPI(data) {
  return request({
    url: 'admin/adminMenu/updateMenu',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 后台-首页菜单删除
 */
export function daleteMenuAPI(data) {
  return request({
    url: 'admin/adminMenu/daleteMenu?menuId=' + data.menuId,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
