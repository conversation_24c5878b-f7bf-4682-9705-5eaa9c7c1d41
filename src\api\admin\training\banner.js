import request from '@/utils/request'

/**
 * 查询banner列表
 */
export function searchBannerListAPI(data) {
  return request({
    url: '/admin/portal/searchBannerList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 上传banner图片
 */
export function upTeachingCoverAPI(data) {
  return request({
    url: '/admin/portal/upTeachingCover',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 修改banner信息
 */
export function updateBannerAPI(data) {
  return request({
    url: '/admin/portal/updateBanner',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 新增banner信息
 */
export function insertBannerAPI(data) {
  return request({
    url: '/admin/portal/insertBanner',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 删除banner信息
 */
export function deleteBannerAPI(data) {
  return request({
    url: '/admin/portal/deleteBanner',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
