<template>
  <div class="dialog-confi">
    <dialogCom
      :title="title"
      :dialog-visible="dialogVisible"
      :submit="_submit"
      :dis-flag="dialogFlag"
      :cancel="_closeDialog"
    >
      <!-- 内容区域 -->
      <template>
        <div class="dialog_con">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="100px"
            style="height: 300px"
            class="edit-experiment"
          >
            <el-form-item label="云主机类型" prop="specificationId">
              <el-select
                ref="headerSearchSelect"
                v-model="form.specificationId"
                class="header-search-select"
                @change="changeFlavor"
              >
                <el-option
                  v-for="option in specificationNameOptions"
                  :key="option.id"
                  :value="option.id"
                  :label="option.name"
                  :disabled="(option.ram < list[0].ram || option.vcpus < list[0].vcpus) && form.lResize"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="cloudCategory">
              <el-radio-group v-model="form.cloudCategory">
                <el-radio-button label="1">云主机</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="CPU" prop="vcpus">
              <el-radio-group v-model="form.vcpus" @change="changeCpu">
                <el-radio-button
                  v-for="item in cpuList"
                  :label="item.name"
                  :key="item.id"
                  :disabled="disCPU(item)"
                />
              </el-radio-group>
            </el-form-item>
            <el-form-item label="内存" prop="ram">
              <el-radio-group v-model="form.ram" @change="changeMemory">
                <el-radio-button
                  v-for="item in memoryList"
                  :label="item.name"
                  :key="item.id"
                  :disabled="disMemory(item)"
                />
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="form.lResize" @change="changelResize">
                <el-radio :label="false">冷调整</el-radio>
                <el-radio :label="true">热调整</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item style="margin-top: -30px">
              <p v-if="!form.lResize" class="el_form_p">注意：冷调整配置后主机会强制重启！</p>
              <p v-if="form.lResize" class="el_form_p">注意：热调整配置后在系统内重启可能出现配置未生效，关机再开机可使配置永久生效。</p>
            </el-form-item>
          </el-form>

          <div class="right_charts">
            <div class="title_text">
              <span>云主机配额</span>
              <span
                v-if="form && form.maxTotalInstances"
              >{{ form.totalInstancesUsed + '/' + form.maxTotalInstances }}</span>
            </div>
            <el-progress
              v-if="form.maxTotalInstances"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(form.totalInstancesUsed/form.maxTotalInstances)"
            />
            <div class="title_text">
              <span>CPU配额</span>
              <span
                v-if="form && form.maxTotalCores"
              >{{ computeCoresUsed + '/' + form.maxTotalCores }}</span>
            </div>
            <el-progress
              v-if="form.maxTotalCores"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(computeCoresUsed/form.maxTotalCores)"
            />
            <div class="title_text">
              <span>内存配额</span>
              <span
                v-if="form && form.maxTotalRAMSize"
              >{{ computeRAMSize + '/' + form.maxTotalRAMSize }}</span>
            </div>
            <el-progress
              v-if="form.maxTotalRAMSize"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(computeRAMSize/form.maxTotalRAMSize)"
            />
            <div class="title_text">
              <span>云硬盘数量</span>
              <span
                v-if="form && form.volumesList.maxTotalVolumes"
              >{{ computeVolumesUsed + '/' + form.volumesList.maxTotalVolumes }}</span>
            </div>
            <el-progress
              v-if="form.volumesList.maxTotalVolumes"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(computeVolumesUsed/form.volumesList.maxTotalVolumes)"
            />
            <div class="title_text">
              <span>云硬盘及快照总大小</span>
              <span
                v-if="form && form.volumesList.maxTotalVolumeGigabytes"
              >{{ computeDiskSize + '/' + form.volumesList.maxTotalVolumeGigabytes }}</span>
            </div>
            <el-progress
              v-if="form.volumesList.maxTotalVolumeGigabytes"
              :stroke-width="12"
              :show-text="false"
              :percentage="Math.ceil(computeDiskSize/form.volumesList.maxTotalVolumeGigabytes)"
            />
            <div class="title_text">
              <span>外网ip配额</span>
              <span>{{ 0 + '/' + 0 }}</span>
            </div>
            <el-progress :stroke-width="12" :show-text="false" :percentage="0"/>
          </div>
        </div>
      </template>
      <!-- <div class="dialog-footer _button">
        <el-button @click="cancal">取 消</el-button>
        <el-button type="primary" @click="sure" style="margin-left: 120px">确 定</el-button>
      </div>-->
    </dialogCom>
  </div>
</template>
<script>
import dialogCom from '@/components/SourceLibrary/dialogGroup/index'
import {
  getCloudHostType,
  instancesQuotas, // 查询云主机配额
  networksQuotas, // 查询网络配额
  volumesQuotas, // 查询云硬盘配额
  cloudHostResize
} from '@/api/sourceLibrary/virtualApi'
export default {
  components: {
    dialogCom
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: null
    },
    list: {
      type: Array,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: []
    }
  },
  data() {
    return {
      cpuList: [],
      cpuListAll: [],
      memoryList: [],
      specAllOptions: [],
      specificationNameOptions: [], // 云主机类型
      rules: {
        specificationId: [
          { required: true, message: '请选择云主机类型', trigger: 'change' }
        ],
        vcpus: [
          { required: true, message: '请选择云主机类型', trigger: 'change' }
        ],
        ram: [{ required: true, message: '请选择内存', trigger: 'change' }]
      },
      strokeWidth: 18,
      radio: 3,
      form: {
        lResize: false,
        specificationId: '',
        cloudCategory: '1',
        vcpus: 0,
        ram: 0,
        totalInstancesUsed: 0, // 云主机配额
        maxTotalInstances: 0, // 云主机配额
        totalRAMUsed: 0, // 内存
        maxTotalRAMSize: 0, // 内存
        totalCoresUsed: 0, // cpu
        maxTotalCores: 0, // cpu
        volumesList: {}, // 云硬盘配额
        diskSize: 20
      },
      dialogFlag: false,
      tabs: [
        {
          id: 1,
          name: '1CPU'
        },
        {
          id: 2,
          name: '2CPU'
        },
        {
          id: 3,
          name: '4CPU'
        },
        {
          id: 4,
          name: '5CPU'
        },
        {
          id: 5,
          name: '6CPU'
        },
        {
          id: 6,
          name: '7CPU'
        }
      ],
      tab: [
        {
          id: 1,
          name: '64MB'
        },
        {
          id: 2,
          name: '512MB'
        },
        {
          id: 3,
          name: '1GB'
        }
      ],
      options: [
        {
          value: '1',
          label: '1C1G'
        },
        {
          value: '2',
          label: '1C4G'
        },
        {
          value: '3',
          label: '4C16G'
        },
        {
          value: '4',
          label: '2C2G'
        },
        {
          value: '5',
          label: '4C4G'
        }
      ]
    }
  },

  computed: {
    // 计算cpu
    computeCoresUsed() {
      return parseInt(this.form.vcpus) + this.form.totalCoresUsed
    },
    // 计算ram
    computeRAMSize() {
      let memory = 0
      if (this.form.ram && this.form.ram.indexOf('MB') != -1) {
        memory = parseInt(this.form.ram)
      } else {
        memory = parseInt(this.form.ram) * 1024
      }
      return memory + this.form.totalRAMUsed
    },
    // 计算云主机配额
    computeDiskSize() {
      let size = 0
      if (this.form.diskVos && this.form.diskVos.length) {
        for (const item of this.form.diskVos) {
          size += item.diskSize
        }
      }
      return (
        this.form.diskSize + size + this.form.volumesList.totalGigabytesUsed
      )
    },
    // 计算云硬盘数量
    computeVolumesUsed() {
      return (
        (this.form.diskVos && this.form.diskVos.length
          ? this.form.diskVos.length
          : 0) +
        this.form.volumesList.totalVolumesUsed +
        1
      )
    }
  },
  created() {
    this.getCloudTypeList()
    this.getAllQuotas()
  },
  methods: {
    // 改变调整方式
    changelResize(val) {
      if (val) {
        this.form.specificationId = ''
      }
    },
    // 热记载时禁用cpu大于当前cpu的选项
    disCPU(item) {
      if (item.id < this.list[0].vcpus && this.form.lResize) {
        return true
      } else {
        return false
      }
    },
    // 热记载时禁用内存大于当前内存的选项
    disMemory(item) {
      if (item.id < this.list[0].ram && this.form.lResize) {
        return true
      } else {
        return false
      }
    },
    getAllQuotas() {
      this.getInstancesQuotas()
      this.getVolumesQuotas()
      this.getNetworksQuotas()
    },
    // 查询云主机配额
    async getInstancesQuotas() {
      await instancesQuotas()
        .then((res) => {
          if (res.code == 200) {
            // 云主机配额
            this.form.totalInstancesUsed = res.data.data.totalInstancesUsed
            this.form.maxTotalInstances = res.data.data.maxTotalInstances
            // 内存
            this.form.totalRAMUsed = res.data.data.totalRAMUsed
            this.form.maxTotalRAMSize = res.data.data.maxTotalRAMSize
            // cpu
            this.form.totalCoresUsed = res.data.data.totalCoresUsed
            this.form.maxTotalCores = res.data.data.maxTotalCores
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 查询网络配额
    async getNetworksQuotas() {
      await networksQuotas()
        .then((res) => {
          if (res.code == 200) {
            this.form.networksQuotasList = res.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    // 查询云硬盘配额
    async getVolumesQuotas() {
      await volumesQuotas()
        .then((res) => {
          if (res.code == 200) {
            this.form.volumesList = res.data.data
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 云主机类型下拉框
    async getCloudTypeList() {
      await getCloudHostType()
        .then((res) => {
          if (res.code == 200) {
            this.specificationNameOptions = res.data
            this.form.specificationId = this.list[0].specificationId
            for (const item of this.specificationNameOptions) {
              const obj = {
                name: item.vCpus + 'CPU',
                id: item.vCpus
              }
              const arr = this.cpuListAll.filter((ele) => ele.id == obj.id)
              if (!arr || !arr.length) {
                this.cpuListAll.push(obj)
              }
            }
            this.changeFlavor(this.form.specificationId)
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 云主机改变
    changeFlavor(val) {
      if (!val) {
        return
      }
      this.specAllOptions = []
      this.memoryList = []
      const arr = this.specificationNameOptions.filter((ele) => ele.id == val)
      if (arr && arr.length) {
        this.cpuList = this.cpuListAll.filter((ele) => ele.id <= arr[0].vCpus)
        this.cpuList.sort((a, b) => a.id - b.id)
        this.form.vcpus = arr[0].vCpus + 'CPU'
      }
      for (const item of this.specificationNameOptions) {
        const obj = {
          name: item.ram >= 1024 ? item.ram / 1024 + 'GB' : item.ram + 'MB',
          id: item.ram,
          cpu: item.vCpus
        }
        const memory = this.specAllOptions.filter((ele) => {
          return ele.name === obj.name && ele.cpu == arr[0].vCpus
        })
        if (!memory || !memory.length) {
          this.specAllOptions.push(obj)
        }
      }
      this.memoryList = this.specAllOptions.filter((ele) => {
        return ele.cpu == arr[0].vCpus
      })
      this.memoryList = this.memoryList.sort((a, b) => a.id - b.id)
      this.form.ram =
        arr[0].ram >= 1024 ? arr[0].ram / 1024 + 'GB' : arr[0].ram + 'MB'
    },
    // CPU改变
    changeCpu(val) {
      this.memoryList = []
      const arr = this.specificationNameOptions.filter(
        (ele) => ele.vCpus == parseInt(val)
      )
      for (const item of arr) {
        const obj = {
          id: item.ram,
          name: item.ram >= 1024 ? item.ram / 1024 + 'GB' : item.ram + 'MB'
        }
        this.memoryList.push(obj)
      }
      const arr1 = this.memoryList.filter((ele) => ele.name == this.form.ram)

      if (!arr1 || !arr1.length) {
        this.form.ram = this.memoryList[0].name
      }
      this.form.specificationId = this.specificationNameOptions.filter(
        (ele) =>
          ele.vCpus == parseInt(val) &&
          ele.ram ==
            (this.form.ram.indexOf('MB') != -1
              ? parseInt(this.form.ram)
              : parseInt(this.form.ram) * 1024)
      )[0].id
    },
    // 内存改变
    changeMemory(val) {
      let memory
      if (val.indexOf('MB') != -1) {
        memory = parseInt(val)
      } else {
        memory = parseInt(val) * 1024
      }
      // let memory = parseInt(val) < 1024 ? parseInt(val) * 1024 : parseInt(val)
      const arr = this.specificationNameOptions.filter(
        (ele) => ele.vCpus == parseInt(this.form.vcpus) && ele.ram == memory
      )
      this.form.specificationId = this.specificationNameOptions.filter(
        (ele) => ele.vCpus == parseInt(this.form.vcpus) && ele.ram == memory
      )[0].id
      console.log(arr)
    },
    _closeDialog() {
      this.dialogFlag = false
      this.$emit('cancelDialog', true)
    },
    _submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogFlag = true
          const paramas = {
            cloudHostId: this.list[0].id,
            specificationId: this.form.specificationId
          }
          if (this.form.lResize == true) {
            paramas.lResize = this.form.lResize
          }
          cloudHostResize(paramas)
            .then((res) => {
              if (res.code == 200) {
                // this.form.networksQuotasList = res.data
                // this.$message.success(res.msg)
                res.name = '更改' + this.list[0].facilityName + '配置， '
                this.$parent.msgList = res
                this.$emit('msgShow')
                this.$emit('updateList')
                this.$nextTick(() => {
                  this._closeDialog()
                })
              } else {
                this.$message.error(res.msg)
              }
              this.dialogFlag = false
            })
            .catch((err) => {
              this.dialogFlag = false
              console.log(err)
            })
        } else {
          return false
        }
      })
    },
    _beforeClose() {
      this.$emit('cancelDialog', true)
    }
  }
}
</script>
<style lang="scss"  scoped>
.dialog-confi ::v-deep {
  .dialog_con {
    display: flex;
    box-sizing: border-box;
    padding-right: 20px;
    .el-dialog__body {
      height: 500px;
    }
    .edit-experiment {
      width: 70%;
      padding-right: 20px;
      box-sizing: border-box;
    }
    .right_charts {
      width: 30%;
      padding-left: 20px;
      box-sizing: border-box;
      border-left: 1px solid #999;
      .title_text {
        margin-bottom: 10px;
        > span {
          margin-right: 10px;
        }
      }
      .el-progress {
        margin-bottom: 10px;
      }
    }
    ._progress {
      .el-progress-bar__outer {
        border-radius: 2px;
        .el-progress-bar__inner {
          border-radius: 2px;
        }
      }
      .p {
        margin: 5px 0 13px;
      }
    }
    .el-progress__text {
      display: none;
    }
    ._button {
      text-align: center;
      padding-top: 110px;
    }
    .el_form_p {
      background-color: rgba(72, 72, 73, 0.3);
      line-height: 35px;
      padding: 0 0 0 10px;
    }
  }
}
</style>
