import request from '@/utils/request'

/**
 * 报名列表
 * @param params
 */
export function applyList(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/getEnrollPeopleList',
    method: 'post',
    data: params
  })
}

/**
 * 删除报名人员
 * @param params
 */
 export function deleteApply(params) {
    return request({
      url: process.env.ADMIN_API + '/activity/deleteByEnrollIds',
      method: 'post',
      data: params
    })
}

/**
 * 导出报名人员
 * @param params
 */
 export function exportApplyPersonnel(params) {
    return request({
      url: process.env.ADMIN_API + '/activity/downloadExcel',
      method: 'post',
      data: params,
      responseType: "blob"
    })
}

/**
 * 查看活动详情
 * @param params
 */
 export function getEnrollPeopleInfo(params) {
  return request({
    url: process.env.ADMIN_API + '/activity/getEnrollPeopleInfo',
    method: 'get',
    params: params,
  })
}



