import request from "@/utils/request";

//获取企业集团树形
export function getUnitTree(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/getTree",
    method: "get"
  });
}
//新增单位
export function addUnit(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/add",
    method: "post",
    data: params
  });
}

//更新单位
export function updateUnit(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/edit",
    method: "post",
    data: params
  });
}
//删除单位
export function deleteUnit(params={}) {
  return request({
    url: process.env.ADMIN_API + "/unit/delete/"+params.uid,
    method: "delete"
  });
}
